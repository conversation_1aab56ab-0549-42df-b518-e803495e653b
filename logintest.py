import base64

import requests
import zhixuewang.session
from zhixuewang.account import login_session

# 复制的cookie字符串
cookie_string = input("请输入cookie：")

# 将cookie字符串转换为字典
cookies = dict(item.split("=") for item in cookie_string.split("; "))

# 创建一个会话
session =zhixuewang.session.get_basic_session()

# 更新会话的cookie
session.cookies.update(cookies)
session.cookies.set("uname", base64.b64encode(cookies["loginUserName"].encode()).decode())

# 使用会话进行请求
student = login_session(session)