# coding:utf-8
import datetime
import json
import time

import requests
from zhixuewang import login_student
from zhixuewang.account import login_playwright

try:
    #student = login_student(input("请输入智学网用户名："), input("请输入密码："))
    # student = login_student('310116200805160630', '56415880wen')
    student = login_playwright('310116200808180012', 'chen992487401')
    print(student.get_token())
    print('正在获取所有的作业...')
    homeworks = student.get_homeworks(200)
    # print(homeworks)
    print('正在筛选所有的打卡作业...')
    clock_homeworks = []
    i = 1
    for homework in homeworks:
        if homework.type.code == 107:
            clock_homeworks.append(homework)
            print(f'{i}.{homework.title}')
            i += 1
    print('筛选完成！')
    choice = clock_homeworks[int(input('请选择要提交的打卡作业：')) - 1]
    print(choice)
    print('请将需要上传的链接存入filedata.json(请务必确认上传的是网页，否则pad无法打开)，json生成方式请找haorwen获取')
    input('放置完成后按回车键以继续...')
    f = open('filedata.json', 'r')
    content = f.read()
    attachmentDTOs = json.loads(content)
    f.close()
    session = student.get_session()
    token = student.get_token()
    confirm = input('是否确认提交？（y/n）')
    if confirm != 'y':
        input('已取消提交，按回车键退出')
        exit()
    print('正在提交...')
    r = session.post(
        'https://mhw.zhixue.com/hw/clock/answer/submit',
        json={
            "base": {
                "Authorization": token,
                "appId": "OAXI57PG",
                "appVersion": "2.0.1906",
                "packageName": "com.iflytek.elpmobile.student",
                "sucAccessDeviceId": "e8d3a31d3b8e44d7-83ed917048b94f42-ca480ede2110d90e",
                "sucClientType": "android",
                "sucOriginAppKey": "",
                "sucUserToken": token,
                "sysType": "Android",
                "sysVersion": "22021211RC",
                "udid": "",
                "userId": student.id,
                "utag": ""
            },
            "params": {
                "attachmentDTOs": attachmentDTOs,
                "type": 3,
                "hwId": choice.id,
                "stuHwId": choice.stu_hwid
            }
        },
        headers={
            "Authorization": token,
            "sucAccessDeviceId": "e8d3a31d3b8e44d7-83ed917048b94f42-ca480ede2110d90e",
            "sucUserToken": token,
            "Referer": "https://mhw.zhixue.com/zhixuestudent/views/learningTask/topic.html"
        },
    )
    data = r.json()
    print(data['code'])
    print(data)
    if data['code'] == '900003':
        print('检测到为第一次提交，正在重新提交...')
        r = session.post(
            'https://mhw.zhixue.com/hw/clock/answer/submit',
            json={
                "base": {
                    "Authorization": token,
                    "appId": "OAXI57PG",
                    "appVersion": "2.0.1906",
                    "packageName": "com.iflytek.elpmobile.student",
                    "sucAccessDeviceId": "e8d3a31d3b8e44d7-83ed917048b94f42-ca480ede2110d90e",
                    "sucClientType": "android",
                    "sucOriginAppKey": "",
                    "sucUserToken": token,
                    "sysType": "Android",
                    "sysVersion": "22021211RC",
                    "udid": "",
                    "userId": student.id,
                    "utag": ""
                },
                "params": {
                    "attachmentDTOs": attachmentDTOs,
                    "type": 2,
                    "hwId": choice.id,
                    "stuHwId": choice.stu_hwid
                }
            },
            headers={
                "Authorization": token,
                "sucAccessDeviceId": "e8d3a31d3b8e44d7-83ed917048b94f42-ca480ede2110d90e",
                "sucUserToken": token,
                "Referer": "https://mhw.zhixue.com/zhixuestudent/views/learningTask/topic.html"
            },
        )
        data = r.json()
        print(data['code'])
        print(data)
    if data['code'] == "000000":
        print('提交成功！')
        url = 'https://mhw.zhixue.com/hwreport/learning/learningRecordList'
        time.sleep(3)
        now = datetime.datetime.now()
        headers = {
            "Host": "mhw.zhixue.com",
            "Connection": "keep-alive",
            "Content-Length": "355",
            "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Microsoft Edge\";v=\"120\"",
            "sucOriginAppKey": "pc_web",
            "sucAccessDeviceId": "48840E9D-D1FE-4F68-840E-58FAA62070E9",
            "sucUserToken": token,
            "sec-ch-ua-mobile": "?0",
            "Authorization": token,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Content-Type": "application/json;charset=UTF-8",
            "sucClientType": "pc-web",
            "Accept": "application/json, text/plain, */*",
            "appName": "com.iflytek.zxzy.web.zx.tea",
            "sec-ch-ua-platform": "\"Windows\"",
            "Origin": "https://www.zhixue.com",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.zhixue.com/middlehomework/web-teacher/views/",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9",
        }
        payload = {
            "base": {
                "appId": "OAXI57PG",
                "appVersion": "",
                "sysVersion": "v1001",
                "sysType": "web",
                "packageName": "com.iflytek.edu.hw",
                "expand": {}
            },
            "params": {
                "hwId": choice.id,
                "hwType": 107,
                "classId": choice.class_id
            }
        }

        response = requests.post(url, json=payload, headers=headers)
        try:
            datas = response.json()['result']['learningRecordList']
        except Exception as e:
            print(e)
            exit()
        for studentwork in datas:
            if studentwork['studentName'] == student.name and studentwork['status'] == 2:
                stuhwid = studentwork['stuHwId']
                clockid = studentwork['clockRecordId']
                payload = {
                    "base": {
                        "appId": "OAXI57PG",
                        "appVersion": "",
                        "sysVersion": "v1001",
                        "sysType": "web",
                        "packageName": "com.iflytek.edu.hw",
                        "expand": {}
                    },
                    "params": {
                        "hwId": choice.id,
                        "stuHwId": stuhwid,
                        "clockRecordId": clockid,
                        "redoReason": f"{now.hour}:{now.minute} 打回成功",
                        "classId": choice.class_id
                    }
                }
                url = "https://mhw.zhixue.com/hw/clock/comment/redo"
                try:
                    response = requests.post(url, json=payload, headers=headers)
                except Exception as e:
                    print(e)
                    exit()
                print('打回成功！重装应用后即可获取到打回的文件！')
    else:
        print('提交失败！')
except Exception as e:
    print('error:',e)
    input('按回车键退出...')