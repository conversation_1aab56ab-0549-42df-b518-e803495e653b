use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};
use pkcs1::EncodeRsaPrivateKey;
use rsa::pkcs1::{EncodeRsaPublicKey, LineEnding};
use rsa::{PaddingScheme, PublicKey, RsaPrivate<PERSON>ey, RsaPublicKey};

#[derive(Debug)]
pub struct License {
    pub domains: Vec<String>,
    pub user_limit: u32,
    pub created_at: DateTime<Utc>,
    pub expired_at: DateTime<Utc>,
    pub sign: Vec<u8>,
}

impl Default for License {
    fn default() -> Self {
        License {
            domains: vec![],
            user_limit: 0,
            created_at: DateTime::<Utc>::MIN_UTC,
            expired_at: DateTime::<Utc>::MIN_UTC,
            sign: vec![],
        }
    }
}

impl License {
    pub fn encode(&self) -> String {
        format!(
            "{},{},{},{}",
            self.domains.join("|"),
            self.created_at,
            self.expired_at,
            self.user_limit,
        )
    }

    // compatiable with vocechat-server v0.4.1-
    pub fn encode_old(&self) -> String {
        format!(
            "{},{},{}",
            self.domains.join("|"),
            self.created_at,
            self.expired_at,
        )
    }
    #[allow(clippy::inherent_to_string)]
    pub fn to_string(&self) -> String {
        let data = format!(
            "{},{},{},{},{}",
            self.domains.join("|"),
            self.user_limit,
            self.created_at.to_rfc3339(),
            self.expired_at.to_rfc3339(),
            hex::encode(&self.sign)
        );
        bs58::encode(data.as_bytes()).into_string()
    }

    pub fn from_string(s: String) -> Result<License> {
        let a = bs58::decode(&s).into_vec()?;
        let b = String::from_utf8(a)?;
        let arr = b.split(',').collect::<Vec<_>>();
        if arr.len() < 5 {
            return Err(anyhow!("Bad Data: {}", &s));
        }

        let user_limit = arr[1].parse::<u32>().unwrap_or_default();
        let created_at = DateTime::parse_from_rfc3339(arr[2])?;
        let expired_at = DateTime::parse_from_rfc3339(arr[3])?;
        let created_at = DateTime::<Utc>::from_utc(created_at.naive_utc(), Utc);
        let expired_at = DateTime::<Utc>::from_utc(expired_at.naive_utc(), Utc);

        let sign = hex::decode(arr[4])?;
        Ok(License {
            domains: arr[0].split('|').map(|v|v.to_string()).collect::<Vec<_>>(),
            user_limit,
            created_at,
            expired_at,
            sign,
        })
    }

    pub fn check_sign(&self, public_key_pem: &str) -> Result<()> {
        let public_key: RsaPublicKey =  pkcs1::DecodeRsaPublicKey::from_pkcs1_pem(public_key_pem)?;
        let data = self.encode();
        if rsa_check_sign(data.as_bytes(), &self.sign, &public_key).is_err() {
            return Err(anyhow!("Invalid sign!"));
        }
        Ok(())
    }

}

#[derive(Debug)]
pub struct LicenseGenerator {
    private_key: RsaPrivateKey,
    public_key: RsaPublicKey,
}

// generate new
impl LicenseGenerator {
    pub fn new(private_key: RsaPrivateKey, public_key: RsaPublicKey) -> Self {
        LicenseGenerator {
            private_key,
            public_key,
        }
    }

    pub fn new_from_pem(private_key_pem: &str, public_key_pem: &str) -> Result<Self> {
        let private_key = pkcs1::DecodeRsaPrivateKey::from_pkcs1_pem(private_key_pem)?;
        let public_key = pkcs1::DecodeRsaPublicKey::from_pkcs1_pem(public_key_pem)?;
        Ok(LicenseGenerator {
            private_key,
            public_key,
        })
    }

    pub fn gen(&self, domains: &str, expired_at: DateTime<Utc>, user_limit: u32) -> License {
        let created_at = Utc::now();
        let mut license = License {
            domains: domains.split('|').map(|v|v.to_string()).collect::<Vec<_>>(),
            user_limit,
            created_at,
            expired_at,
            sign: vec![],
        };
        let data = license.encode();
        let sign = rsa_sign(data.as_bytes(), &self.private_key);
        license.sign = sign;
        license
    }

    pub fn check(&self, license_str: &str) -> Result<()> {
        let license = License::from_string(license_str.to_string())?;
        if license.expired_at < Utc::now() {
            return Err(anyhow!("License expired at {}!", license.expired_at));
        }
        let data = license.encode();
        if rsa_check_sign(data.as_bytes(), &license.sign, &self.public_key).is_err() {
            return Err(anyhow!("Invalid sign!"));
        }
        Ok(())
    }
}

pub fn gen_rsa_pair() -> (RsaPrivateKey, RsaPublicKey) {
    let mut rng = rand::thread_rng();
    let bits = 2048;
    let private_key = RsaPrivateKey::new(&mut rng, bits).expect("failed to generate a key");
    let public_key = RsaPublicKey::from(&private_key);
    (private_key, public_key)
}

pub fn gen_rsa_pem_pair() -> Result<(String, String)> {
    let mut rng = rand::thread_rng();
    let bits = 2048;
    let private_key = RsaPrivateKey::new(&mut rng, bits).expect("failed to generate a key");
    let a = private_key.to_pkcs1_pem(LineEnding::CRLF)?.to_string();
    let public_key = RsaPublicKey::from(&private_key);
    let b = public_key.to_pkcs1_pem(LineEnding::CRLF)?;
    Ok((a, b))
}

fn rsa_sign(digest_in: &[u8], private_key: &RsaPrivateKey) -> Vec<u8> {
    let padding = PaddingScheme::new_pkcs1v15_sign(None);
    private_key.sign(padding, digest_in).unwrap()
}

fn rsa_check_sign(hashed: &[u8], sig: &[u8], public_key: &RsaPublicKey) -> Result<()> {
    Ok(public_key.verify(PaddingScheme::new_pkcs1v15_sign(None), hashed, sig)?)
}

pub fn rsa_check_license_bs58(license_bs58: &str, public_key_pem: &str) -> Result<()> {
    let public_key: RsaPublicKey = pkcs1::DecodeRsaPublicKey::from_pkcs1_pem(public_key_pem)?;
    let license = License::from_string(license_bs58.to_string())?;
    let data = license.encode();
    rsa_check_sign(data.as_bytes(), license.sign.as_slice(), &public_key)
}

pub fn rsa_check_license(license: &License, public_key_pem: &str) -> Result<()> {
    let public_key: RsaPublicKey = pkcs1::DecodeRsaPublicKey::from_pkcs1_pem(public_key_pem)?;
    let data = license.encode();
    rsa_check_sign(data.as_bytes(), license.sign.as_slice(), &public_key)
}

#[cfg(test)]
mod test {
    use super::*;
    use std::ops::Add;

    #[test]
    fn test_license() {
        let (private_key, public_key) = gen_rsa_pair();
        let licensegen = LicenseGenerator::new(private_key, public_key);
        let expired_at = Utc::now().add(chrono::Duration::seconds(365 * 86400));
        let license = licensegen.gen("www.domain.com|www.domain2.com", expired_at, 10);
        assert_eq!(license.domains[0], "www.domain.com");
        assert_eq!(license.domains[1], "www.domain2.com");
        assert_eq!(license.expired_at, expired_at);
        let b = licensegen.check(&license.to_string());
        assert_eq!(b.is_ok(), true);
    }

    #[test]
    fn test_check_license() {
        let (private_key, public_key) = gen_rsa_pair();
        let licensegen = LicenseGenerator::new(private_key, public_key.clone());
        let expired_at = Utc::now().add(chrono::Duration::seconds(365 * 86400));
        let license = licensegen.gen("www.domain.com|www.domain2.com", expired_at, 20);

        let license_bs58 = license.to_string();

        let public_key_pem = public_key.to_pkcs1_pem(LineEnding::CRLF).unwrap();
        let a = rsa_check_license_bs58(&license_bs58, &public_key_pem);
        assert!(a.is_ok());
    }

    #[test]
    fn test_gen_license() {
        let private_key_pem = r#"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"#;

        let public_key_pem = r#"-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEApqGLPAiVzx42qRkjDGqCT4+BrS3BReJA7UAXQt3YNfw2HIB+CJSD
F22KnpqmnsaLWmxrUP1Q+ttb+fZhMZ569s5ZLs9h6pq2oTBK8kBUKz127rpwHSpG
VnuGbkPB4NUcTOYiDTLT7iD9NSN38Cr1ITTD3+4EiSiCuf9aUpggfo06fqF69ebD
C0pPSTRvIDgKrJiku93c3d1uDq1DWfYKu3GP23ie5+3WwQcsd/XG/0xyMk1hfVQJ
qTf5Z2rVdmhVGt0XjV6cmaVshJOxGeoAubPLJX4G4DLTvXKGy/WlQlQTqIBz8xUB
dnwtOymXGQpaS/Vfo0q1kGzZoXsCx3v7BQIDAQAB
-----END RSA PUBLIC KEY-----"#;

        let licensegen = LicenseGenerator::new_from_pem(private_key_pem, public_key_pem).unwrap();

        let expired_at = chrono::NaiveDate::parse_from_str("2025-01-01", "%Y-%m-%d")
            .expect("Date format error: %Y-%m-%d, just like: 2024-02-01");
        let expired_at =
            chrono::NaiveDateTime::new(expired_at, chrono::NaiveTime::from_hms(0, 0, 0));
        let expired_at = chrono::DateTime::<Utc>::from_utc(expired_at, Utc);
        let license = licensegen.gen("www.domain.com|www.domain2.com", expired_at, 20);

        let license_bs58 = license.to_string();
        //dbg!(license_bs58);
        // assert_eq!(&license_bs58, "2RvN4krfyQbvuLcXzz4PwoJfckRFptqLKKXSP7f4Np7AGqgixrfdUzZ5qk2iT2gFwCntivyYnSaumyao7QF2SfQ3TKyLAgGDYeYeJcbBri9re5esG2PMrxZBjq68eR94yYqdTg6LAP9oc5WtLAaBX25RkVu2zt59kdgRzk5CbnnmZnMAHgZEDnsVJfCQa6HKnb3p1cpZa6LTANrKh1VuvCAerdCHCoc1YHNwUipg5JJXmxJQadFShv1sYREUHHzLuPMb8xHb7GkPJ6MJpQBzHfDnTySRG7BGxNz5GKCutFp1o7YjkQqZvKBTvWQ8ic4HmbHErLHw4aJ5CAMyugX4v2GAgxS8Z4zi9tjdzRtbbqzncnXSNBTumBxobBskNAbEaQC9HgBavgcAw4wHrHSyG3v2rTdsDUJZgTtanEfxnxZhSpKtXZRFzVNdjmo66GeLhvWwMZSXKzH89uTvMcEokmbUyLz9mKXzRP9dhTJ4bC6YWNrbDueYX9pqrsmXm3Z4aYP7DjknXwPCMKZbsCXZi2YQVUjggCyRarR4eThY6gZ8iWGkvi1ybAADooh8KXSAFNGSRRTGC5La6Atug7y6e6QnFmaRndLHdCyxqyq9LM1Ly2icYAFa2ZspXyL3MyBTLFvgQeqTmL1KQVHzhjwtkTvcFsUxScYNXhVgCkyD5vkSZpcwixGJYyYtkrF27XpMcq8mR6dAyi3Zqt2w68X3xA748a8ofky1KYwHmA1U4BaDpXbbVKSu6wtonMhzvQ6xMssyWJVhrzeymPnMwM8Xiut3pZcpC77Ri");

        let license = License::from_string(license_bs58).unwrap();
        // dbg!(license.domain);

        let license_bs58 = "333RgyjbHG5Bw3KUNTVGqDCD6BDfyxBs3YRVWGn1MkhMqnPiyfc5Fw5NwBtMyFgpnKuX1DB2AoD33QA36ePibGvcieFP2Jr9E85MyCW1wuHMVv466tPBVUcJcHwiQbd5dYcPi2JRRnguG4AuWrnrWJEaRMhurvKKppQKgC7f8vKQSPAHLj23eniyT757FDWos4H9xaTYZCiQxnLLVVi8bv3jh1aJMSLrsCTHh4VAnyB4XuXPrMz4jgYapdC43dLXbcGLAxUr6czRgBpwqcRAVmtfTXJf72Bfse2bKowm3kcffUd8hjUmiRoZsJRs28BdqHX3rxpq2K1HJFxeyVSKArZMg5uM9UT8MnbNmaBq7LaRmGXUQuDTLn3MNtp9jM8KnbzpV4BPDbvoqNTaP8ykq4idEQqeZUUFygjGB4GhWqqJLZrGefi32yxRo71uSV2cb5Q7AqayGLiHKLsC1MYYrimGfa4t7pgQJrMUE7PMf1baPGk7B5SkTnANRwfxrceczRu6dikzQJ3dc9eJSdKKMEHNfimCNuXdP1bPbKBYY4tAMC5MZkQi9QVAeUHjBiWCMRvdcjHmHUVuHgGZgY4jranNs5vur2oNCyBA5pZ39HbESwjGkrRBP6n1mkCvGCAGtRBm6mZ9fbiarzGkHYVdM3ZN6Rfu7nxW9EDdNKfeg234az81Kf6MGJpex8jdwoJcmSbowmZFb1NZqLohWdbcEFNR3yvQr7sB9jhK5nrK84vXkctzhWs3Ed6zPb1bAVL5vdepNzwsbEsAXySEsjG";
        let license = License::from_string(license_bs58.into()).unwrap();
        println!("encode: {}", license.encode().as_str());
        let r = license.check_sign(public_key_pem).unwrap();


    }
}
