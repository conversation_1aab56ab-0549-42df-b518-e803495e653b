

// const JsonNone: Option<()> = None;

fn load_private_public() -> (String, String) {
    let private_key_pem_file = "./private.pem";
    let public_key_pem_file = "./public.pem";
    /*if !std::path::Path::new(private_key_pem_file).exists() || !std::path::Path::new(public_key_pem_file).exists() {
        let (private_key_pem, public_key_pem) = vc_license::gen_rsa_pem_pair().expect("gen rsa failed!");
        std::fs::write(private_key_pem_file, private_key_pem.as_bytes()).unwrap();
        std::fs::write(public_key_pem_file, public_key_pem.as_bytes()).unwrap();
    }*/
    let private1 = std::fs::read_to_string(private_key_pem_file).expect("load private.pem failed!");
    let public1 = std::fs::read_to_string(public_key_pem_file).expect("load public.pem failed!");
    (private1, public1)
}

#[derive(Debug, thiserror::Error)]
struct CustomError<T> {
    code: i32,
    message: String,
    data: T,
}

impl CustomError<Option<String>> {
    fn new<S: Into<String>>(code: i32, message: S)
                            -> Self {
        CustomError {
            code: code,
            message: message.into(),
            data: None,
        }
    }
}

impl<T> CustomError<T> where T: Serialize {
    fn new_with_data(code: i32, message: &str, data: T)
                     -> Self {
        CustomError {
            code: code,
            message: message.to_string(),
            data,
        }
    }
}

impl<T> Display for CustomError<T> where T: Serialize {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        let j = json!({
            "code": &self.code,
            "message": &self.message,
            "data": &self.data
        });
        f.write_str(&j.to_string())
    }
}

impl<T> ResponseError for CustomError<T> {
    fn status(&self) -> StatusCode {
        StatusCode::BAD_REQUEST
    }
    fn as_response(&self) -> Response
        where
            Self: Error + Send + Sync + 'static,
    {
        Response::builder()
            .status(StatusCode::OK)
            .content_type("application/json")
            .body(self.to_string())
    }
}

#[derive(Debug, Default, Clone)]
pub struct State {
    pub private_key_pem: String,
    pub public_key_pem: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct CheckLicenseArg {
    license: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct GenLicenseArg {
    domain: String,
    expiry_at: String,
    user_limit: u32,
}

#[handler]
async fn license_gen(Json(args): Json<GenLicenseArg>, state: Data<&State>) -> poem::Result<Json<serde_json::Value>> {
    let expired_at = chrono::NaiveDate::parse_from_str(&args.expiry_at, "%Y-%m-%d")
        .map_err(|err| CustomError::new(-1, "Date format error: %Y-%m-%d, just like: 2024-02-01"))?;
    let expired_at = NaiveDateTime::new(expired_at, NaiveTime::from_hms(0, 0, 0));
    let expired_at = chrono::DateTime::<Utc>::from_utc(expired_at, Utc);

    let lg = vc_license::LicenseGenerator::new_from_pem(&state.private_key_pem, &state.public_key_pem)
        .map_err(|err| CustomError::new(-1, "Bad Certificate"))?;
    let license = lg.gen(&args.domain, expired_at, args.user_limit).to_string();
    Ok(Json(json!({
        "code": 0,
        "message": "ok",
        "data": {
            "license": license,
        }
    })))
}

#[handler]
async fn license_check(Json(args): Json<CheckLicenseArg>, state: Data<&State>) -> poem::Result<Json<serde_json::Value>> {
    let license = vc_license::License::from_string(args.license.clone())
        .map_err(|err| CustomError::new(-1, "Bad License"))?;
    let lg = vc_license::LicenseGenerator::new_from_pem(&state.private_key_pem, &state.public_key_pem)
        .map_err(|err| CustomError::new(-1, "Bad Certificate"))?;
    let sign_bool = lg.check(&args.license).is_ok();
    Ok(Json(json!({
        "code": 0,
        "message": "ok",
        "data": {
            "license": json!({
                "domains": license.domains,
                "user_limit": license.user_limit,
                "created_at": license.created_at.to_string(),
                "expired_at": license.expired_at.to_string(),
                "sign": sign_bool,
            }),
        }
    })))
}

use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), std::io::Error> {
    if std::env::var_os("RUST_LOG").is_none() {
        std::env::set_var("RUST_LOG", "license=debug,poem=debug");
    }
    tracing_subscriber::fmt::init();
    tracing::info!("verion: 1.0");

    let (private_key_pem, public_key_pem) = load_private_public();
    let state = State {
        private_key_pem,
        public_key_pem,
    };

    let app = Route::new()
        .at("/license/gen", post(license_gen))
        .at("/license/check", post(license_check))
        .with(Tracing)
        .data(state)
        .before(|req| async move {
            if req.header("token").map(|v| v.to_string()).unwrap_or_default() == ACCESS_TOKEN {
                Ok(req)
            } else {
                Err(poem::Error::from_string(ERROR_STR_BAD_TOKEN, poem::http::StatusCode::FORBIDDEN))
            }
        })
        ;

    Server::new(TcpListener::bind("0.0.0.0:3000"))
        .name("vocechat-license")
        .run(app)
        .await
}

// Limited to 20 users, expires on 2156-1-1.
#[test]
fn test_gen_default_license() {
    let expiry_at = "2156-1-1";
    let domain = "*";
    let user_limit = 20;
    let (private_key_pem, public_key_pem) = load_private_public();
    let expired_at = chrono::NaiveDate::parse_from_str(expiry_at, "%Y-%m-%d").unwrap();
    let expired_at = NaiveDateTime::new(expired_at, NaiveTime::from_hms(0, 0, 0));
    let expired_at = chrono::DateTime::<Utc>::from_utc(expired_at, Utc);

    let lg = vc_license::LicenseGenerator::new_from_pem(&private_key_pem, &public_key_pem).unwrap();
    let license = lg.gen(domain, expired_at, user_limit).to_string();
    dbg!(license);
}


#[allow(dead_code)]
fn main2() {
    let args = std::env::args().collect::<Vec<_>>();
    // let args = vec!["a", "www.xxx.com", "2023-01-01"];
    let current_exe = std::env::current_exe().unwrap().to_str().unwrap_or_default().to_string();
    if args.len() < 2 {
        println!("Usage: {} gen www.domain.com 2032-1-1 20", &current_exe);
        println!("Usage: {} gen \"www.domain.com|www.domain2.com\" 2032-1-1 20", &current_exe);
        println!("Usage: {} check Dfy55yXRttCUWRaK53RM4sP3X6jA71...", &current_exe);
        return;
    }

    let (private_key_pem, public_key_pem) = load_private_public();
    let lg = vc_license::LicenseGenerator::new_from_pem(&private_key_pem, &public_key_pem).expect("load certificate failed!");

    if args[1] == "gen" {
        dbg!(&args);
        let expired_at = chrono::NaiveDate::parse_from_str(&args[3], "%Y-%m-%d").expect("Date format error: %Y-%m-%d, just like: 2024-02-01");
        let expired_at = NaiveDateTime::new(expired_at, NaiveTime::from_hms(0, 0, 0));
        let expired_at = chrono::DateTime::<Utc>::from_utc(expired_at, Utc);
        let user_limit = args[4].parse::<u32>().unwrap_or_default();

        let license = lg.gen(&args[2], expired_at, user_limit).to_string();
        println!("License: {}", &license);
    } else if args[1] == "check" {
        let license = vc_license::License::from_string(args[2].to_string()).expect("License invalid!");
        let r = lg.check(&args[2]);
        println!("domains: {}", &license.domains.join("|"));
        println!("created_at: {}", &license.created_at);
        println!("expired_at: {}", &license.expired_at);
        println!("sign: {}", if r.is_ok() { "Valid" } else { "Invalid" });
    }
}
