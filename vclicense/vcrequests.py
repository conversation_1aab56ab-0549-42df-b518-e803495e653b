import os
import sys
import argparse
import datetime
import base58
import binascii
import rsa
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.asymmetric import utils
from cryptography.hazmat.primitives.serialization import load_pem_private_key, load_pem_public_key, Encoding, PublicFormat

class License:
    def __init__(self):
        self.domains = []
        self.user_limit = 0
        self.created_at = datetime.datetime.min
        self.expired_at = datetime.datetime.min
        self.sign = b''
    
    def encode(self):
        """编码许可证信息，与 Rust 代码中的 encode 方法对应"""
        return f"{self._join_domains()},{self.created_at.isoformat()},{self.expired_at.isoformat()},{self.user_limit}"
    
    def encode_old(self):
        """兼容 vocechat-server v0.4.1- 的编码方法"""
        return f"{self._join_domains()},{self.created_at.isoformat()},{self.expired_at.isoformat()}"
    
    def _join_domains(self):
        """将域名列表用 | 连接"""
        return "|".join(self.domains)
    
    def to_string(self):
        """将许可证转换为 bs58 编码的字符串"""
        data = f"{self._join_domains()},{self.user_limit},{self.created_at.isoformat()},{self.expired_at.isoformat()},{binascii.hexlify(self.sign).decode()}"
        return base58.b58encode(data.encode()).decode()
    
    @classmethod
    def from_string(cls, s):
        """从 bs58 编码的字符串解析许可证"""
        try:
            decoded = base58.b58decode(s).decode()
            parts = decoded.split(',')
            if len(parts) < 5:
                raise ValueError(f"Bad Data: {s}")
            
            license = cls()
            license.domains = parts[0].split('|')
            license.user_limit = int(parts[1])
            license.created_at = datetime.datetime.fromisoformat(parts[2])
            license.expired_at = datetime.datetime.fromisoformat(parts[3])
            license.sign = binascii.unhexlify(parts[4])
            return license
        except Exception as e:
            raise ValueError(f"解析许可证失败: {str(e)}")
    
    def check_sign(self, public_key_pem):
        """验证许可证签名"""
        try:
            public_key = load_pem_public_key(public_key_pem.encode())
            data = self.encode().encode()
            
            # 使用预哈希方式验证
            chosen_hash = hashes.SHA256()
            hasher = hashes.Hash(chosen_hash)
            hasher.update(data)
            digest = hasher.finalize()
            
            public_key.verify(
                self.sign,
                data,
                padding.PKCS1v15(),
                utils.Prehashed(chosen_hash)
            )
            return True
        except Exception:
            return False

class LicenseGenerator:
    def __init__(self, private_key_pem, public_key_pem):
        self.private_key = load_pem_private_key(private_key_pem.encode(), password=None)
        self.public_key = load_pem_public_key(public_key_pem.encode())
    
    def gen(self, domains, expired_at, user_limit):
        """生成新的许可证"""
        license = License()
        license.domains = domains.split('|')
        license.user_limit = user_limit
        license.created_at = datetime.datetime.now(datetime.timezone.utc)
        license.expired_at = expired_at
        
        # 使用预哈希方式签名
        data = license.encode().encode()
        chosen_hash = hashes.SHA256()
        hasher = hashes.Hash(chosen_hash)
        hasher.update(data)
        digest = hasher.finalize()
        
        license.sign = self.private_key.sign(
            data,
            padding.PKCS1v15(),
            utils.Prehashed(chosen_hash)
        )
        
        return license
    
    def check(self, license_str):
        """检查许可证是否有效"""
        try:
            license = License.from_string(license_str)
            # 获取当前时间（带时区）
            now = datetime.datetime.now(datetime.timezone.utc)
            # 确保过期时间有时区信息
            if license.expired_at.tzinfo is None:
                license.expired_at = license.expired_at.replace(tzinfo=datetime.timezone.utc)
            
            if license.expired_at < now:
                return False, "许可证已过期"
            
            if not license.check_sign(self._get_public_key_pem()):
                return False, "签名无效"
            
            return True, "许可证有效"
        except Exception as e:
            return False, f"检查许可证失败: {str(e)}"
    
    def _get_public_key_pem(self):
        """获取公钥 PEM 格式"""
        return self.public_key.public_bytes(
            encoding=Encoding.PEM,
            format=PublicFormat.PKCS1
        ).decode()

def load_private_public():
    """加载私钥和公钥文件"""
    private_key_pem_file = "./private.pem"
    public_key_pem_file = "./public.pem"
    
    if not os.path.exists(private_key_pem_file) or not os.path.exists(public_key_pem_file):
        print("错误: 密钥文件不存在，请确保 private.pem 和 public.pem 文件在当前目录")
        sys.exit(1)
    
    try:
        with open(private_key_pem_file, 'r') as f:
            private_key_pem = f.read()
        
        with open(public_key_pem_file, 'r') as f:
            public_key_pem = f.read()
        
        return private_key_pem, public_key_pem
    except Exception as e:
        print(f"加载密钥文件失败: {str(e)}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='VoceChat 许可证生成工具')
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # 生成许可证命令
    gen_parser = subparsers.add_parser('gen', help='生成许可证')
    gen_parser.add_argument('domain', help='域名，多个域名用 | 分隔，例如: www.domain.com|www.domain2.com')
    gen_parser.add_argument('expiry_date', help='过期日期，格式: YYYY-MM-DD，例如: 2032-1-1')
    gen_parser.add_argument('user_limit', type=int, help='用户数量限制')
    
    # 检查许可证命令
    check_parser = subparsers.add_parser('check', help='检查许可证')
    check_parser.add_argument('license', help='许可证字符串')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    private_key_pem, public_key_pem = load_private_public()
    license_gen = LicenseGenerator(private_key_pem, public_key_pem)
    
    if args.command == 'gen':
        try:
            # 解析过期日期，添加时区信息
            expiry_date = datetime.datetime.strptime(args.expiry_date + " 00:00:00+00:00", "%Y-%m-%d %H:%M:%S%z")
            # 生成许可证
            license = license_gen.gen(args.domain, expiry_date, args.user_limit)
            print(f"许可证: {license.to_string()}")
        except Exception as e:
            print(f"生成许可证失败: {str(e)}")
    
    elif args.command == 'check':
        try:
            license = License.from_string(args.license)
            is_valid, message = license_gen.check(args.license)
            print(f"域名: {license._join_domains()}")
            print(f"用户限制: {license.user_limit}")
            print(f"创建时间: {license.created_at}")
            print(f"过期时间: {license.expired_at}")
            print(f"签名: {'有效' if is_valid else '无效'} - {message}")
        except Exception as e:
            print(f"检查许可证失败: {str(e)}")

if __name__ == "__main__":
    main()