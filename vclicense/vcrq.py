import requests
import json

def login():
    url = "https://vocechat.xf-yun.cn/api/token/login"
    
    headers = {
        "accept": "application/json; charset=utf-8",
        "Content-Type": "application/json; charset=utf-8"
    }
    
    data = {
        "credential": {
            "email": "<EMAIL>",
            "password": "56415880Wen",
            "type": "password"
        },
        "device": "web",
        "device_token": None
    }
    
    response = requests.post(url, headers=headers, json=data)
    print(f"登录状态码: {response.status_code}")
    return response.json()

def check_license(license_key, api_key):
    url = "https://vocechat.xf-yun.cn/api/license/check"
    
    headers = {
        "accept": "application/json; charset=utf-8",
        "Content-Type": "application/json; charset=utf-8",
        "x-api-key": api_key
    }
    
    data = {
        "license": license_key
    }
    
    response = requests.post(url, headers=headers, json=data)
    print(f"检查license状态码: {response.status_code}")
    return response.text

def update_license(license_key, api_key):
    url = "https://vocechat.xf-yun.cn/api/license"
    
    headers = {
        "accept": "application/json; charset=utf-8",
        "Content-Type": "application/json; charset=utf-8",
        "x-api-key": api_key
    }
    
    data = {
        "license": license_key
    }
    
    response = requests.put(url, headers=headers, json=data)
    print(f"更新license状态码: {response.status_code}")
    return response.text

if __name__ == "__main__":
    license_key = "DZczZ6yQ52XnLa9WCJyamAoP4CQBgXtJygWnqFNgvuZ4ToaBKpYADu9tivXhhXm8nKiqqogiwfQEUTbq9FxXqxmb5m2Mua3Vsp97m3uUruNrxuhghAqULYLgr4Eeuw7n16wqKGMZAjAWK5oDsSjX4kkpMG3ESVUVpU3j9WBcejJCgSVSF42PZXppv7SAye85Uzd8npbhjR7VzCiAn1ngoAiKCDqYs6Z45ggvqAqr2br5aRrh3CUPGUUbXqwoFR1KEGdrgBa1CQQG5y5AruxukJvRP4xVVqWYJEhyfxJwe4uw4fqNWZw1nY6zDy73eNWNqkh1oHJhfVGacWnxwvTzV6AobbLa3ac8eRJS3pcNfPJmag3i3VuCaxtA7U1neR1VMyEcRG6YC7HLNKtNKKCsuY46oSNhCpEcc4EP1jFaNAFZsCxJRLjhmmw1R5U5hbPDwpExBzgWqZQCokgXHDvxXnYSGq1cshrkZSmKaV8Gzv1oNp45V7cqTVARsf1nQsLUyMMuHFmz14kGAokSSq2QjYz4cxfkMBTvfWYCikmgDzfG3to71fsJ6uERD4wPJoq1z4EZDsFEzZJatQEySqPUU4ftC2z4xQtrEvhpjqZPLCrbA6ifX59quMP3rzK4QFaBwEAfpiKeDsRjxMfZEF4nLTCQaryoAeumPEbZD5aXmLCpGfyB2nBgssGQnF6KZdNeUsHeSbLs2LbiiumwZzzbd1S5cxFGLokBJVfrFh8G9phtQRBVRBLXfwfWP6We9JW5sxakoAu1b8Dr4XRkWTJpf2JbsrJ"
    
    try:
        # 先登录获取token
        login_result = login()
        api_key = login_result.get('token')
        print("登录成功，获取到token")
        
        # 使用token进行后续操作
        result = update_license(license_key, api_key)
        print("响应结果:")
        print(result)
    except Exception as e:
        print(f"请求出错: {e}")