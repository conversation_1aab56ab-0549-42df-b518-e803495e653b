import requests
import json
import tkinter as tk
from tkinter import filedialog
import os
import mimetypes

def select_files():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    file_paths = filedialog.askopenfilenames(
        title="选择图片文件",
        filetypes=[("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp")]
    )
    return file_paths

def create_files_payload(file_paths):
    files = []
    for file_path in file_paths:
        # 获取文件名
        filename = os.path.basename(file_path)
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(file_path)
        # 如果无法判断MIME类型，默认使用image/png
        if not mime_type:
            mime_type = 'image/png'
        
        # 创建文件元组
        file_tuple = ('image', (filename, open(file_path, 'rb'), mime_type))
        files.append(file_tuple)
    return files

def main():
    # 选择文件
    selected_files = select_files()
    if not selected_files:
        print("未选择任何文件")
        return

    # 准备文件数据
    files = create_files_payload(selected_files)

    url = "https://gengruihuan.cn/v1/images/edits"
    
    # Remove json.dumps() and send the payload as a dictionary
    payload = {
        "model": "gpt-image-1-all",
        "prompt": "合并两张图片，可以美化，人物细节不要改变，下巴可以不那么尖，横屏",
        "n": 1,
        "size": "1536x1024"
    }
    
    headers = {
        'Authorization': 'Bearer sk-2ZiumtaBdRQrdepqIomhl1pvepFTcAy6ZBtDtWiP8yMAaI4u'
    }
    # Remove 'Content-Type': 'application/json' from headers since we're sending multipart/form-data

    # 发送请求
    response = requests.request("POST", url, headers=headers, data=payload, files=files)
    print(response.text)
    with open("response.json", "w", encoding="utf-8") as f:
        json.dump(response.json(), f, ensure_ascii=False, indent=4)

if __name__ == "__main__":
    main()