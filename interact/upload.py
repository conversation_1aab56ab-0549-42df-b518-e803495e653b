from aiohttp import web
from aiohttp.web_request import Request
from pathlib import Path
import json
import uuid
import time

def time13():
    return int(round(time.time() * 1000))

async def handler(request: Request):
    print(request.path)
    if request.path.startswith("/static"):
        # danger ftp
        p = Path("./interact" + request.path)
        if not p.is_file():
            res = web.Response(status=403, text="403 Forbidden")
        elif not p.exists():
            res = web.Response(status=404, text="404 Not Found")
        else:
            res = web.FileResponse(str(p))
        return res
    
    if request.path == "/checkUpload":
        return web.Response(text=json.dumps({
            "code": 0,
            "uuid": str(uuid.uuid4())
        }))
    elif request.path == "/file/upload/" or request.path == "/file/classQuizUpload/":
        post = await request.post()
        image = post.get("file")
        with open(f"./interact/upload/{time13()}.jpg", "wb") as f:
            f.write(image.file.read())
        return web.Response(text="upload suc")
    else:
        return web.Response(text="file server")

app = web.Application()
app.router.add_get("/{path:.*}", handler)
app.router.add_post("/{path:.*}", handler)
web.run_app(app, host="0.0.0.0", port=8022)
