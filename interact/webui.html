<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智学网服务器控制台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 15px;
        }
        .status-info {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .status-item {
            flex: 1;
            min-width: 200px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f9f9f9;
        }
        .command-panel {
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            cursor: pointer;
            border: none;
        }
        button:hover {
            background-color: #2980b9;
        }
        .log-panel {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .refresh-btn {
            margin-left: 10px;
            background-color: #2ecc71;
        }
        .refresh-btn:hover {
            background-color: #27ae60;
        }
        .error {
            color: #e74c3c;
        }
        .success {
            color: #2ecc71;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智学网服务器控制台</h1>
        
        <div class="card">
            <h2>服务器状态 <button id="refreshStatus" class="refresh-btn">刷新</button></h2>
            <div class="status-info">
                <div class="status-item">
                    <p><strong>运行时间:</strong> <span id="uptime">加载中...</span></p>
                    <p><strong>连接数:</strong> <span id="connectionCount">加载中...</span></p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>连接的客户端</h2>
            <table id="connectionsTable">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>用户名</th>
                        <th>角色</th>
                        <th>IP地址</th>
                        <th>连接时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5" style="text-align: center;">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="card command-panel">
            <h2>发送命令</h2>
            <div class="form-group">
                <label for="commandSelect">选择命令:</label>
                <select id="commandSelect">
                    <option value="random_select_from_core">随机选人</option>
                    <option value="showgoodview">展示优秀作业</option>
                    <option value="showExcelentView">展示优秀作业(另一种)</option>
                    <option value="startclass">开始上课</option>
                    <option value="classover">下课</option>
                </select>
            </div>
            <div class="form-group">
                <label for="userSelect">选择用户:</label>
                <select id="userSelect" multiple size="5">
                    <option value="all">所有用户</option>
                    <!-- 用户列表将通过JavaScript动态填充 -->
                </select>
                <p><small>按住Ctrl键可以选择多个用户</small></p>
            </div>
            <button id="sendCommand">发送命令</button>
        </div>
        
        <div class="card">
            <h2>命令历史</h2>
            <table id="commandHistoryTable">
                <thead>
                    <tr>
                        <th>命令</th>
                        <th>用户</th>
                        <th>时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="3" style="text-align: center;">暂无命令历史</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="card log-panel" id="logPanel">
            <h2>操作日志</h2>
            <div id="logEntries"></div>
        </div>
    </div>

    <script>
        // WebSocket连接
        let ws = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        
        // 连接WebSocket
        function connectWebSocket() {
            // 确定WebSocket URL (使用相对路径，自动适应HTTP/HTTPS)
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}/ws/webui`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                addLogEntry('WebSocket连接已建立');
                reconnectAttempts = 0;
                // 请求初始状态
                ws.send(JSON.stringify({type: 'refresh'}));
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'status') {
                        updateUIWithStatus(data.data);
                        addLogEntry('通过WebSocket接收到状态更新');
                    }
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                    addLogEntry(`解析WebSocket消息失败: ${error.message}`, true);
                }
            };
            
            ws.onclose = function() {
                addLogEntry('WebSocket连接已关闭', true);
                // 尝试重新连接
                if (reconnectAttempts < maxReconnectAttempts) {
                    reconnectAttempts++;
                    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                    addLogEntry(`${delay/1000}秒后尝试重新连接 (${reconnectAttempts}/${maxReconnectAttempts})...`);
                    setTimeout(connectWebSocket, delay);
                } else {
                    addLogEntry('达到最大重连次数，切换到HTTP轮询模式');
                    // 切换到HTTP轮询
                    setInterval(refreshServerStatus, 5000);
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                addLogEntry('WebSocket连接错误', true);
            };
        }
        
        // 更新UI显示状态
        function updateUIWithStatus(data) {
            // 更新状态信息
            document.getElementById('uptime').textContent = formatTime(data.uptime);
            document.getElementById('connectionCount').textContent = data.connections_count;
            
            // 更新连接表格
            const connectionsTable = document.getElementById('connectionsTable').getElementsByTagName('tbody')[0];
            connectionsTable.innerHTML = '';
            
            if (data.connections.length === 0) {
                const row = connectionsTable.insertRow();
                const cell = row.insertCell(0);
                cell.colSpan = 5;
                cell.style.textAlign = 'center';
                cell.textContent = '暂无连接';
            } else {
                // 更新用户选择下拉框
                const userSelect = document.getElementById('userSelect');
                // 保留"所有用户"选项
                userSelect.innerHTML = '<option value="all">所有用户</option>';
                
                data.connections.forEach(conn => {
                    // 添加到连接表格
                    const row = connectionsTable.insertRow();
                    row.insertCell(0).textContent = conn.userid;
                    row.insertCell(1).textContent = conn.username;
                    row.insertCell(2).textContent = conn.role;
                    row.insertCell(3).textContent = conn.ip;
                    row.insertCell(4).textContent = formatDateTime(conn.connect_time);
                    
                    // 添加到用户选择下拉框
                    const option = document.createElement('option');
                    option.value = conn.userid;
                    option.textContent = `${conn.username} (${conn.userid})`;
                    userSelect.appendChild(option);
                });
            }
            
            // 更新命令历史
            const commandHistoryTable = document.getElementById('commandHistoryTable').getElementsByTagName('tbody')[0];
            commandHistoryTable.innerHTML = '';
            
            if (data.commands_history.length === 0) {
                const row = commandHistoryTable.insertRow();
                const cell = row.insertCell(0);
                cell.colSpan = 3;
                cell.style.textAlign = 'center';
                cell.textContent = '暂无命令历史';
            } else {
                data.commands_history.forEach(cmd => {
                    const row = commandHistoryTable.insertRow();
                    row.insertCell(0).textContent = cmd.command;
                    row.insertCell(1).textContent = cmd.user_ids.join(', ');
                    row.insertCell(2).textContent = formatDateTime(cmd.time);
                });
            }
        }
        
        // 格式化时间函数
        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            return `${hours}小时 ${minutes}分钟 ${secs}秒`;
        }
        
        // 格式化日期时间
        function formatDateTime(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN');
        }
        
        // 添加日志条目
        function addLogEntry(message, isError = false) {
            const logEntries = document.getElementById('logEntries');
            const entry = document.createElement('div');
            entry.className = `log-entry ${isError ? 'error' : ''}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logEntries.prepend(entry);
        }
        
        // 刷新服务器状态 (HTTP方式，作为WebSocket的备用)
        async function refreshServerStatus() {
            try {
                // 如果WebSocket连接可用，则通过WebSocket请求刷新
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({type: 'refresh'}));
                    return;
                }
                
                const response = await fetch('/api/status');
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const data = await response.json();
                updateUIWithStatus(data);
                addLogEntry('状态刷新成功(HTTP)');
            } catch (error) {
                console.error('获取服务器状态失败:', error);
                addLogEntry(`获取服务器状态失败: ${error.message}`, true);
            }
        }
        
        // 发送命令
        async function sendCommand() {
            const commandSelect = document.getElementById('commandSelect');
            const userSelect = document.getElementById('userSelect');
            const command = commandSelect.value;
            
            // 获取选中的用户
            const selectedUsers = Array.from(userSelect.selectedOptions)
                .map(option => option.value);
            
            // 如果选择了"所有用户"或没有选择用户，则获取所有用户ID
            let userIds = [];
            if (selectedUsers.includes('all') || selectedUsers.length === 0) {
                // 获取所有用户ID（除了"所有用户"选项）
                userIds = Array.from(userSelect.options)
                    .filter(option => option.value !== 'all')
                    .map(option => option.value);
            } else {
                userIds = selectedUsers;
            }
            
            if (userIds.length === 0) {
                addLogEntry('没有可用的用户来发送命令', true);
                return;
            }
            
            // 尝试通过WebSocket发送命令
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'command',
                    command: command,
                    user_ids: userIds
                }));
                addLogEntry(`命令 "${command}" 已通过WebSocket发送给 ${userIds.length} 个用户`);
                return;
            }
            
            // WebSocket不可用时，使用HTTP API
            try {
                const response = await fetch('/api/send_command', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        command: command,
                        user_ids: userIds
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    addLogEntry(`命令 "${command}" 已通过HTTP发送给 ${userIds.length} 个用户`);
                    // 刷新状态以显示新的命令历史
                    setTimeout(refreshServerStatus, 500);
                } else {
                    throw new Error(result.error || '未知错误');
                }
            } catch (error) {
                console.error('发送命令失败:', error);
                addLogEntry(`发送命令失败: ${error.message}`, true);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 尝试建立WebSocket连接
            connectWebSocket();
            
            // 初始刷新状态
            refreshServerStatus();
            
            // 设置刷新按钮事件
            document.getElementById('refreshStatus').addEventListener('click', refreshServerStatus);
            
            // 设置发送命令按钮事件
            document.getElementById('sendCommand').addEventListener('click', sendCommand);
            
            // 设置定时刷新（每30秒）
            setInterval(refreshServerStatus, 30000);
            
            addLogEntry('WebUI已加载');
        });
    </script>
</body>
</html>
