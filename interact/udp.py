import socket
import json
import time

UDP_IP = "*********"
UDP_PORT = 5155

host = "************"
cmd = {
    "cmd": "ipinfo",
    "ws_url": f"ws://{host}:8021/chat",
    "displayname": "what?!",
    "clsid": "tea",
    "ssid": ""
}


print("host", host)
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)
sock.bind((host, UDP_PORT))

while True:
    msg = json.dumps(cmd)
    sock.sendto(msg.encode(), (UDP_IP, UDP_PORT))
    time.sleep(1)