import asyncio
import websockets
from websockets.asyncio.server import ServerConnection
from websockets.http11 import Response
from websockets.datastructures import Headers
from urllib.parse import unquote, parse_qs
import socket
import json
import time
import uuid
import logging
import os
from typing import Dict, List, Optional, Any, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("zhixue-server")

# 配置参数
CONFIG = {
    "host": "************",
    "port": 8021,
    "encoding": "GBK",
    "timeout": 20.0,
    "default_user_id": "4444000020023323152",
    "tcp_src": "tcp://127.0.0.1:8020",
    "room_id": "m_tea",
    "sid": "0.2514"
}

# 服务器状态
SERVER_STATUS = {
    "start_time": time.time(),
    "connections": {},  # 存储连接的客户端信息
    "commands_history": []  # 存储发送的命令历史
}

def time15() -> str:
    """生成15位时间戳字符串"""
    return str(int(round(time.time() * 100000)))

class WebSocketPackage:
    """WebSocket消息包处理类"""
    
    @staticmethod
    def make_package(method: str, *params) -> bytes:
        """
        创建WebSocket消息包
        
        Args:
            method: 消息方法名
            params: 消息参数列表
            
        Returns:
            bytes: 编码后的消息包
        """
        i = 0
        sb: bytes = b""
        sb += method.encode(CONFIG["encoding"]) + b"\r\n"
        for p in params:
            ind = str(i) if i <= 9 else ":"
            p = p if isinstance(p, bytes) else str(p).encode(CONFIG["encoding"])
            sb += f"{ind}: {len(p)}\r\n".encode(CONFIG["encoding"])
            sb += p + b"\r\n"
            i += 1
        return sb

class HttpHandler:
    """HTTP请求处理类"""
    
    @staticmethod
    def make_response(status: int, headers: Dict[str, str], resp: bytes) -> Response:
        """创建HTTP响应"""
        return Response(status, "", Headers(headers), resp)
    
    @staticmethod
    async def process_request(con, request):
        """处理HTTP请求"""
        logger.debug(f"收到HTTP请求: {request.path}")
        
        # 检查是否为WebSocket升级请求
        upgrade = request.headers.get("Upgrade", "").lower()
        if upgrade == "websocket":
            return None  # 让WebSocket处理器处理
        
        # 处理静态文件请求
        if request.path == "/":
            # 返回WebUI主页
            webui_path = os.path.join(os.path.dirname(__file__), "webui.html")
            if os.path.exists(webui_path):
                with open(webui_path, "rb") as f:
                    content = f.read()
                return HttpHandler.make_response(200, {
                    "Content-Type": "text/html; charset=utf-8"
                }, content)
            else:
                return HttpHandler.make_response(200, {
                    "Content-Type": "text/plain; charset=utf-8"
                }, b'server online - WebUI not found')
        
        # API接口 - 获取服务器状态
        if request.path == "/api/status":
            status_data = {
                "uptime": time.time() - SERVER_STATUS["start_time"],
                "connections_count": len(SERVER_STATUS["connections"]),
                "connections": list(SERVER_STATUS["connections"].values()),
                "commands_history": SERVER_STATUS["commands_history"][-10:] if SERVER_STATUS["commands_history"] else []
            }
            return HttpHandler.make_response(200, {
                "Content-Type": "application/json; charset=utf-8",
                "Access-Control-Allow-Origin": "*"
            }, json.dumps(status_data).encode("utf-8"))
        
        # API接口 - 发送命令
        if request.path == "/api/send_command" and request.method == "POST":
            # 解析POST数据
            content_length = int(request.headers.get("Content-Length", 0))
            body = await con.read_exact(content_length)
            try:
                command_data = json.loads(body.decode("utf-8"))
                command = command_data.get("command", "")
                user_ids = command_data.get("user_ids", [CONFIG["default_user_id"]])
                
                # 记录命令
                SERVER_STATUS["commands_history"].append({
                    "command": command,
                    "user_ids": user_ids,
                    "time": time.time()
                })
                
                # 通知ZhiXueServer发送命令
                if hasattr(con, "_zhixue_server"):
                    await con._zhixue_server.send_command(command, user_ids)
                    return HttpHandler.make_response(200, {
                        "Content-Type": "application/json; charset=utf-8",
                        "Access-Control-Allow-Origin": "*"
                    }, json.dumps({"success": True}).encode("utf-8"))
                else:
                    return HttpHandler.make_response(500, {
                        "Content-Type": "application/json; charset=utf-8",
                        "Access-Control-Allow-Origin": "*"
                    }, json.dumps({"success": False, "error": "Server not initialized"}).encode("utf-8"))
            except Exception as e:
                logger.error(f"处理命令请求错误: {e}")
                return HttpHandler.make_response(400, {
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*"
                }, json.dumps({"success": False, "error": str(e)}).encode("utf-8"))
        
        # 处理查询请求
        if request.path.startswith("/qry"):
            params = {
                "errcode": 0,
                "room_id": CONFIG["room_id"],
                "session_count": len(SERVER_STATUS["connections"]),
                "time": int(time.time()),
                "session": list(SERVER_STATUS["connections"].values())
            }
            return HttpHandler.make_response(200, {
                "Connection": "close",
                "Content-Type": f"text/html; charset={CONFIG['encoding']}"
            }, json.dumps(params).encode(CONFIG["encoding"]))
            
        # 处理预检请求
        if request.method == "OPTIONS":
            return HttpHandler.make_response(200, {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }, b'')
            
        # 默认返回404
        return HttpHandler.make_response(404, {
            "Content-Type": "text/plain; charset=utf-8"
        }, b'Not Found')

class ZhiXueServer:
    """智学网WebSocket服务器"""
    
    def __init__(self):
        """初始化服务器"""
        self.server = None
        self.active_connections = set()
        self.webui_connections = set()  # 存储WebUI的WebSocket连接
    
    async def start(self):
        """启动服务器"""
        logger.info(f"启动服务器 - 主机: {CONFIG['host']}, 端口: {CONFIG['port']}")
        
        # 启动服务器
        self.server = await websockets.serve(
            self.handle_connection, '0.0.0.0', CONFIG['port'],
            process_request=self.process_request, 
            ping_interval=None
        )
        await self.server.wait_closed()
    
    async def process_request(self, con, request):
        """处理HTTP请求，添加服务器引用"""
        con._zhixue_server = self
        return await HttpHandler.process_request(con, request)
    
    async def handle_connection(self, websocket: ServerConnection):
        """处理WebSocket连接"""
        client_ip = websocket.remote_address[0]
        logger.info(f"新连接: {client_ip}")
        
        # 添加到活动连接集合
        self.active_connections.add(websocket)
        
        try:
            # 尝试接收第一条消息来确定连接类型
            async with asyncio.timeout(CONFIG["timeout"]):
                first_message = await websocket.recv()
                
            # 检查是否是WebUI连接
            try:
                data = json.loads(first_message)
                if isinstance(data, dict) and data.get("type") in ["webui_init", "refresh"]:
                    # 这是WebUI连接
                    await self.handle_webui_connection(websocket, first_message)
                else:
                    # 这是普通客户端连接，需要处理第一条消息
                    response_bytes = await self.process_message(websocket, first_message if isinstance(first_message, bytes) else first_message.encode())
                    if response_bytes:
                        await websocket.send(response_bytes)
                    await self.main_logic(websocket)
            except json.JSONDecodeError:
                # 不是JSON格式，应该是普通客户端
                response_bytes = await self.process_message(websocket, first_message if isinstance(first_message, bytes) else first_message.encode())
                if response_bytes:
                    await websocket.send(response_bytes)
                await self.main_logic(websocket)
                
        finally:
            # 从活动连接中移除
            self.active_connections.remove(websocket)
            # 从WebUI连接中移除
            if websocket in self.webui_connections:
                self.webui_connections.remove(websocket)
            # 从状态中移除
            for uid in list(SERVER_STATUS["connections"].keys()):
                if SERVER_STATUS["connections"][uid].get("ip") == client_ip:
                    del SERVER_STATUS["connections"][uid]
    
    async def handle_webui_connection(self, websocket: ServerConnection, first_message=None):
        """处理WebUI的WebSocket连接"""
        logger.info("WebUI WebSocket连接已建立")
        self.webui_connections.add(websocket)
        
        # 发送初始状态
        await self.send_status_to_webui(websocket)
        
        # 处理第一条消息（如果有）
        if first_message:
            try:
                data = json.loads(first_message)
                if data.get("type") == "refresh":
                    # 已经在上面发送了初始状态，不需要再次发送
                    pass
                elif data.get("type") == "command":
                    command = data.get("command", "")
                    user_ids = data.get("user_ids", [CONFIG["default_user_id"]])
                    
                    # 记录命令
                    SERVER_STATUS["commands_history"].append({
                        "command": command,
                        "user_ids": user_ids,
                        "time": time.time()
                    })
                    
                    # 发送命令
                    await self.send_command(command, user_ids)
                    
                    # 通知所有WebUI客户端更新状态
                    await self.broadcast_status_to_webui()
            except json.JSONDecodeError:
                logger.warning(f"无效的WebUI消息格式: {first_message}")
        
        try:
            while True:
                # 接收WebUI的消息
                async with asyncio.timeout(CONFIG["timeout"]):
                    message = await websocket.recv()
                
                # 处理WebUI发送的消息
                try:
                    data = json.loads(message)
                    if data.get("type") == "refresh":
                        await self.send_status_to_webui(websocket)
                    elif data.get("type") == "command":
                        command = data.get("command", "")
                        user_ids = data.get("user_ids", [CONFIG["default_user_id"]])
                        
                        # 记录命令
                        SERVER_STATUS["commands_history"].append({
                            "command": command,
                            "user_ids": user_ids,
                            "time": time.time()
                        })
                        
                        # 发送命令
                        await self.send_command(command, user_ids)
                        
                        # 通知所有WebUI客户端更新状态
                        await self.broadcast_status_to_webui()
                except json.JSONDecodeError:
                    logger.warning(f"无效的WebUI消息格式: {message}")
        except asyncio.TimeoutError:
            logger.warning("WebUI连接超时")
        except Exception as e:
            logger.error(f"WebUI连接关闭: {e}")
    
    async def send_status_to_webui(self, websocket):
        """发送状态信息到WebUI"""
        status_data = {
            "type": "status",
            "data": {
                "uptime": time.time() - SERVER_STATUS["start_time"],
                "connections_count": len(SERVER_STATUS["connections"]),
                "connections": list(SERVER_STATUS["connections"].values()),
                "commands_history": SERVER_STATUS["commands_history"][-10:] if SERVER_STATUS["commands_history"] else []
            }
        }
        await websocket.send(json.dumps(status_data))
    
    async def broadcast_status_to_webui(self):
        """广播状态到所有WebUI连接"""
        for ws in self.webui_connections:
            try:
                await self.send_status_to_webui(ws)
            except Exception as e:
                logger.error(f"向WebUI广播状态失败: {e}")
    
    # 在send_command方法后添加广播状态的调用
    async def send_command(self, command: str, user_ids: List[str]):
        """发送命令到客户端"""
        # 构建控制命令
        command_data = {
            "sortid": "control",
            "qtype": command,
            "userids": user_ids,
        }
        
        # 发送命令
        msg = WebSocketPackage.make_package(
            "chat.onsub_send",
            "ra", json.dumps(command_data),
            "", ""
        )
        logger.info(f"发送命令: {command} 到用户: {user_ids}")
        
        # 发送到所有活动连接
        for ws in self.active_connections:
            if ws not in self.webui_connections:  # 不向WebUI连接发送命令
                try:
                    await ws.send(msg)
                except Exception as e:
                    logger.error(f"发送命令错误: {e}")
        
        # 广播状态更新到所有WebUI
        await self.broadcast_status_to_webui()
    
    async def main_logic(self, websocket: ServerConnection):
        """主要的WebSocket消息处理逻辑"""
        while True:
            try:
                # 接收消息，设置超时
                async with asyncio.timeout(CONFIG["timeout"]):
                    recv_bytes: bytes = await websocket.recv()
                
                # 处理接收到的消息
                response_bytes = await self.process_message(websocket, recv_bytes)
                if response_bytes:
                    logger.debug(f"发送响应: {response_bytes[:100]}...")
                    await websocket.send(response_bytes)
                    
            except asyncio.TimeoutError:
                logger.warning("连接超时")
                break
            except Exception as e:
                logger.error(f"连接关闭: {e}")
                break
    
    async def process_message(self, websocket: ServerConnection, recv_bytes: bytes) -> Optional[bytes]:
        """
        处理接收到的WebSocket消息
        
        Args:
            websocket: WebSocket连接
            recv_bytes: 接收到的字节数据
            
        Returns:
            Optional[bytes]: 响应数据，如果不需要响应则返回None
        """
        logger.debug(f"接收: {recv_bytes[:100]}...")
        
        # 处理订阅消息
        if recv_bytes.startswith(b"chat.subscribe"):
            return self.handle_subscribe(websocket, recv_bytes)
            
        # 处理心跳消息
        elif recv_bytes.startswith(b"chat.heartbeat"):
            return WebSocketPackage.make_package("chat.heartbeat", CONFIG["room_id"])
        
        # 处理发送消息
        elif recv_bytes.startswith(b"chat.sub_send"):
            self.handle_sub_send(recv_bytes)
            return None
            
        # 处理其他消息
        else:
            logger.warning(f"未知消息类型: {recv_bytes[:50]}...")
            return None
    
    def handle_subscribe(self, websocket: ServerConnection, recv_bytes: bytes) -> bytes:
        """处理订阅消息"""
        data = recv_bytes.decode().strip().splitlines()
        # 示例: ['chat.subscribe', '0: 16', 'chat.onsubscribe', '1: 5', 'm_tea', '2: 7', 'student', '3: 21', 'm_4444000020023323152', '4: 27', '%E5%90%B4%E5%AE%B6%E4%B8%9E', '5: 4', 'true', '6: 1', '0', '7: 0']
        
        role, uid, name = data[6], data[8], data[10]
        userid = uid.replace("m_", "")
        username = unquote(name)
        client_ip = websocket.remote_address[0]
        logger.info(f"用户订阅: {role}, {userid}, {username}, IP: {client_ip}")
        
        # 更新连接状态
        SERVER_STATUS["connections"][userid] = {
            "uid": uid,
            "userid": userid,
            "username": username,
            "role": role,
            "ip": client_ip,
            "connect_time": time.time()
        }
        
        # 构建用户列表
        user_list = [
            {"uid": uid, "name": name, "role": role, "sid": "...", "src": CONFIG["tcp_src"], "meiid": ""},
            {"uid": "ra", "name": "ra", "role": "agent", "sid": "...", "src": CONFIG["tcp_src"], "meiid": ""}
        ]
        
        # 构建响应
        return WebSocketPackage.make_package(
            "chat.onsubscribe",
            CONFIG["room_id"], "true", CONFIG["sid"], uid, name, role, "",
            json.dumps(user_list),
            '{"oldsid": "' + CONFIG["sid"] + '"}', time15(), CONFIG["tcp_src"]
        )
    
    def handle_sub_send(self, recv_bytes: bytes) -> None:
        """处理发送消息"""
        if b"discuss.speech" in recv_bytes:
            # 处理讨论消息
            info = recv_bytes.decode().strip().splitlines()[-3]
            data = json.loads(info)["speachData"]
            data = json.loads(unquote(data))
            logger.info(f"[讨论] {data['username']}: {data['text']}")
        else:
            # 处理其他消息
            logger.info(unquote(recv_bytes.decode()))
 

def main():
    """主函数"""
    server = ZhiXueServer()
    try:
        asyncio.run(server.start())
    except KeyboardInterrupt:
        logger.info("服务器被用户中断")
    except Exception as e:
        logger.error(f"服务器异常: {e}")

if __name__ == "__main__":
    print(f"主机: {CONFIG['host']}")
    main()