import asyncio
import websockets
from websockets.asyncio.server import ServerConnection
from websockets.http11 import Response
from websockets.datastructures import Headers
from urllib.parse import unquote
import socket
import json
import time
import uuid

def time15():
    return str(int(round(time.time() * 100000)))

def log(*args):
    return
    print(*args)

def make_ws_pkg(method, *params):
    i = 0
    sb : bytes = b""
    sb += method.encode("GBK") + b"\r\n"
    for p in params:
        ind = str(i) if i <= 9 else ":"
        p = p if type(p) == bytes else str(p).encode("GBK")
        sb += f"{ind}: {len(p)}\r\n".encode("GBK")
        sb += p + b"\r\n"
        i += 1
    return sb

async def user_event(ws):
    "CommandFactory,ControlCmdInfo"
    while True:
        try:
            "发送文件"
            # # content = b"Hello World\n"
            # content = open("interact/ying.jpg", "rb").read()
            # msg = make_ws_pkg(
            #     "chat.ondownload", "",
            #     "MyFolder", "MyFile.jpg",
            #     0, content,
            #     "fileend", "0")
            # print("user", "chat.ondownload", "interact/ying.jpg")

            "关机、互动等命令"
            data = await asyncio.to_thread(input, "$ ")

            j = {
                "sortid": "control",
                # "qtype": "random_select_from_core", "showgoodview", "showExcelentView"
                # startclass, classover
                "qtype": data,
                "userids": ["4444000020023323152"],      # screen_status
                # "groupnum": "",
                # "workid": "",
                # "userid": "",
                # "username": ""
            }
            msg = make_ws_pkg(
                "chat.onsub_send",
                "ra", json.dumps(j),
                "", ""
            )

            # rtspplayer 49995

            "讨论（上传图片）"
            # data = await asyncio.to_thread(input, "$ ")
            # # attachment_bean = {
            # #     "id": "id",
            # #     "localName": "download.png",
            # #     "localPath": "MyDir",
            # #     "remoteName": "ying.jpg",
            # #     "remotePath": f"http://{host}:8022/static/ying.jpg",
            # #     "type": "img"
            # # }
            # discuss_data = {
            #     "attachment": [],
            #     "topic": "hello world",
            #     # "type": ""
            # }
            # group_data = {
            #     # "data": [],
            #     # "joinGroupNum": 0,
            #     # "joinresult": True,
            #     # "joinUserId": ""
            # }
            # j = {
            #     "sortid": "discuss.create",
            #     "discussData": json.dumps(discuss_data),
            #     "groupData": json.dumps(group_data),
            #     "uuid": str(uuid.uuid4())
            # }
            # msg = make_ws_pkg(
            #     "chat.onsub_send",
            #     "ra", json.dumps(j),
            #     "", ""
            # )

            "题库"
            # data = await asyncio.to_thread(input, "$ ")
            # question = {
            # }
            # j = {
            #     "sortid": "activityAll.connect.create",
            #     "wtype": "share",   # "talkend"
            #     "devicetype": "lcd",
            #     "version": "4.0",
            #     "sendQuesMark": "questionBank",
            #     "questionBankInfo": question
            # }
            # """
            # questionCmdInfo.questions = parseQuestions(jSONObject.optJSONArray("questions"));
            # questionCmdInfo.picUrls = parsePicUrls(jSONObject.optJSONArray("pic_url"));
            # questionCmdInfo.quickIds = parsePicUrls(jSONObject.optJSONArray("quickIds"));
            # """
            # msg = make_ws_pkg(
            #     "chat.onsub_send",
            #     "ra", json.dumps(j),
            #     "", ""
            # )


            print("send user msg", msg[:512])
            await ws.send(msg)
        except Exception as e:
            print("user", e, e.args)
            break

async def main_logic(websocket: ServerConnection):
    print(websocket.remote_address[0])
    asyncio.create_task(user_event(websocket))
    while True:
        try:
            async with asyncio.timeout(20.0):
                recv_bytes: bytes = await websocket.recv()
        except Exception as e:
            print("connection close", e, e.args)
            break
        log("recv:", recv_bytes)

        if recv_bytes.startswith(b"chat.subscribe"):
            data = recv_bytes.decode().strip().splitlines()
            # ['chat.subscribe', '0: 16', 'chat.onsubscribe', '1: 5', 'm_tea', '2: 7', 'student', '3: 21', 'm_4444000020023323152', '4: 27', '%E5%90%B4%E5%AE%B6%E4%B8%9E', '5: 4', 'true', '6: 1', '0', '7: 0']
            role, uid, name = data[6], data[8], data[10]
            userid = uid.replace("m_", "")
            username = unquote(name)
            print(role, userid, username)

            user_list = [
                {"uid": uid, "name": name, "role": role, "sid": "...", "src": "tcp://127.0.0.1:8020", "meiid": ""},
                {"uid": "ra", "name": "ra", "role": "agent", "sid": "...", "src": "tcp://127.0.0.1:8020", "meiid": ""}
            ]
            response_bytes = make_ws_pkg(
                "chat.onsubscribe",
                "m_tea", "true", "0.2514", uid, name, role, "",
                json.dumps(user_list),
                '{"oldsid": "0.2514"}', time15(), "tcp://127.0.0.1:8020"
            )
        elif recv_bytes.startswith(b"chat.heartbeat"):
            response_bytes = make_ws_pkg("chat.heartbeat", "m_tea")
        
        elif recv_bytes.startswith(b"chat.sub_send"):
            if b"discuss.speech" in recv_bytes:
                info = recv_bytes.decode().strip().splitlines()[-3]
                data = json.loads(info)["speachData"]
                data = json.loads(unquote(data))
                print("[discuss]", data["username"], data["text"])
            else:
                print(unquote(recv_bytes.decode()))
            continue
        else:
            #assert 0
            print(recv_bytes)
            continue
        
        log("send", response_bytes)
        await websocket.send(response_bytes.decode())
        

def make_response(status, headers, resp):
    return Response(status, "", Headers(headers), resp)

async def http_server(con, request):
    log(request.path)
    # 检查是否为WebSocket升级请求
    upgrade = request.headers.get("Upgrade", "").lower()
    if upgrade != "websocket":
        return make_response(404, {
            "Connection": "close",
            "Content-Type": "text/plain; charset=utf-8"
        }, b'Not Found - WebSocket connection required')
    
    if request.path == "/":
        return make_response(200, {}, b'server online')
    if request.path.startswith("/qry"):
        params = {
            "errcode": 0,
            "room_id": "m_tea",
            "session_count": 0,
            "time": int(time.time()),
            "session": []
        }
        return make_response(200, {
            "Connection": "close",
            "Content-Type": "text/html; charset=GBK"
        }, json.dumps(params).encode("GBK"))
    return None

host = "************"
print("host", host)

async def start_server():
    server = await websockets.serve(
        main_logic, '0.0.0.0', 8021,
        process_request=http_server, ping_interval=None
    )
    await server.wait_closed()

try:
    asyncio.run(start_server())
except KeyboardInterrupt:
    pass
except:
    pass