webpackJsonp([2],{TGgG:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={name:"quill-button-italic",props:{value:{type:Boolean,default:!1}},methods:{handleClick:function(){this.$emit("click"),this.$emit("input",!this.value)}}},l={render:function(){var t=this.$createElement,e=this._self._c||t;return e("button",{staticClass:"ql-italic",class:{"quill-active":this.value},attrs:{type:"button"},on:{click:this.handleClick}},[e("svg",{attrs:{viewBox:"0 0 18 18"}},[e("line",{staticClass:"ql-stroke",attrs:{x1:"7",x2:"13",y1:"4",y2:"4"}}),this._v(" "),e("line",{staticClass:"ql-stroke",attrs:{x1:"5",x2:"11",y1:"14",y2:"14"}}),this._v(" "),e("line",{staticClass:"ql-stroke",attrs:{x1:"8",x2:"10",y1:"14",y2:"4"}})])])},staticRenderFns:[]};var a=s("VU/8")(i,l,!1,function(t){s("dsXh")},"data-v-3ed43e50",null);e.default=a.exports},dsXh:function(t,e){}});