webpackJsonp([0],{"5hXx":function(t,e){},QKj2:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a={name:"quill-button-bold",props:{value:{type:Boolean,default:!1}},methods:{handleClick:function(){this.$emit("click"),this.$emit("input",!this.value)}}},l={render:function(){var t=this.$createElement,e=this._self._c||t;return e("button",{staticClass:"quill-bold",class:{"quill-active":this.value},attrs:{type:"button"},on:{click:this.handleClick}},[e("svg",{attrs:{viewBox:"0 0 18 18"}},[e("path",{staticClass:"ql-stroke",attrs:{d:"M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"}}),this._v(" "),e("path",{staticClass:"ql-stroke",attrs:{d:"M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"}})])])},staticRenderFns:[]};var i=s("VU/8")(a,l,!1,function(t){s("5hXx")},"data-v-e6de3f7e",null);e.default=i.exports}});