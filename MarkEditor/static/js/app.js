webpackJsonp([6],{"+BTi":function(t,e){},"3f40":function(t,e){},"4qOc":function(t,e){},BxWU:function(t,e){},CZJ2:function(t,e){},"Cw+g":function(t,e){},NHnr:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});n("BxWU"),n("+BTi");var i=n("g2bL"),l=n.n(i),o=n("7+uW"),r=(n("erWL"),{render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{attrs:{id:"app"}},[e("router-view")],1)},staticRenderFns:[]});var a=n("VU/8")({name:"App"},r,!1,function(t){n("Cw+g")},"data-v-088ec525",null).exports,u=n("/ocq"),s=n("woOf"),c=n.n(s),d=n("Zx67"),f=n.n(d),h=n("Zrlr"),p=n.n(h),b=n("wxAW"),v=n.n(b),m=n("zwoO"),_=n.n(m),q=n("yEsh"),g=n.n(q),w=n("Pf15"),k=n.n(w),x=(n("3f40"),n("4qOc"),n("r+QJ"),n("yPE/")),y=n.n(x),C=y.a.import("attributors/style/size");C.whitelist=["16px","18px","20px","22px","24px","28px","32px","36px"],y.a.register(C,!0);var z=function(t){function e(){return p()(this,e),_()(this,(e.__proto__||f()(e)).apply(this,arguments))}return k()(e,t),v()(e,null,[{key:"create",value:function(t){var n=g()(e.__proto__||f()(e),"create",this).call(this);return n.setAttribute("href","javascript:void(0);"),n.setAttribute("target","_blank"),n}},{key:"formats",value:function(t){return t.getAttribute("href")}}]),e}(y.a.import("blots/inline"));z.blotName="link",z.tagName="a",y.a.register(z);var A=y.a,B=function(t){function e(){return p()(this,e),_()(this,(e.__proto__||f()(e)).apply(this,arguments))}return k()(e,t),v()(e,null,[{key:"create",value:function(t){var n=g()(e.__proto__||f()(e),"create",this).call(this);return n.setAttribute("href","javascript:void(0);"),n.setAttribute("target","_blank"),n}},{key:"formats",value:function(t){return t.getAttribute("href")}}]),e}(A.import("blots/inline"));B.blotName="link",B.tagName="a",A.register(B);var T=function(t){function e(){return p()(this,e),_()(this,(e.__proto__||f()(e)).apply(this,arguments))}return k()(e,t),v()(e,null,[{key:"create",value:function(t){var n=g()(e.__proto__||f()(e),"create",this).call(this);return n.setAttribute("src",""),n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!1),n}},{key:"value",value:function(t){return t.getAttribute("src")}}]),e}(A.import("blots/block/embed"));T.blotName="video",T.tagName="iframe",A.register(T);var $={name:"quill-editor",components:{BtnBold:function(){return n.e(0).then(n.bind(null,"QKj2"))},BtnItalic:function(){return n.e(2).then(n.bind(null,"TGgG"))},BtnUnderline:function(){return n.e(4).then(n.bind(null,"JRbr"))},BtnFontsize:function(){return n.e(1).then(n.bind(null,"Eu8a"))},BtnColor:function(){return n.e(3).then(n.bind(null,"TXqv"))}},data:function(){return{_options:{},_content:"",defaultOptions:{theme:"snow",readOnly:!1,placeholder:"请点击此处输入文本...",modules:{toolbar:[]}},bold:!1,italic:!1,underline:!1,fontsize:"20px",color:"#2C2C2C"}},props:{value:String,disabled:{type:Boolean,default:!1},fullscreen:{type:Boolean,default:!1},options:{type:Object,required:!1,default:function(){return{}}}},mounted:function(){this.init()},beforeDestroy:function(){this.quill=null,delete this.quill},methods:{updateButtonStatus:function(t){try{var e=this.quill.getFormat();console.log(t,e),this.bold=!!e.bold,this.italic=!!e.italic,this.underline=!!e.underline,e.size?this.fontsize=Array.isArray(e.size)?e.size[e.size.length-1]:e.size:this.fontsize="20px",e.color?this.color=Array.isArray(e.color)?e.color[e.color.length-1]:e.color:this.color="#2C2C2C"}catch(t){console.error(t)}},init:function(){var t=this;this.$el&&(this._options=c()({},this.defaultOptions,this.options),this.quill=new A(this.$refs.editor,this._options),this.quill.enable(!1),this.value&&this.quill.pasteHTML(this.value),this.disabled||this.quill.enable(!0),this.quill.on("selection-change",function(e){e?t.$emit("focus",t.quill):t.$emit("blur",t.quill),setTimeout(function(){t.updateButtonStatus("selection-change")},0)}),this.quill.on("text-change",function(e,n,i){var l=t.$refs.editor.children[0].innerHTML,o=t.quill,r=t.quill.getText();"<p><br></p>"===l&&(l=""),t._content=l,t.$emit("input",t._content),t.$emit("change",{html:l,text:r,quill:o}),setTimeout(function(){t.updateButtonStatus("text-change")},0)}),this.$emit("ready",this.quill))},handleBold:function(){this.quill.format("bold",!this.bold)},handleItalic:function(){this.quill.format("italic",!this.italic)},handleUnderline:function(){this.quill.format("underline",!this.underline)},handleFontsize:function(t){console.log("handleFontsize -> fontsize",t),this.quill.format("size",t)},handleColor:function(t){this.quill.format("color",t)}},watch:{value:function(t,e){this.quill&&(t&&t!==this._content?(this._content=t,this.quill.pasteHTML(t)):t||this.quill.setText(""))},disabled:function(t,e){this.quill&&this.quill.enable(!t)}}},E={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"quill-editor",class:{"is-fullscreen":t.fullscreen}},[n("div",{ref:"editor"}),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!t.disabled,expression:"!disabled"}],staticClass:"quill-toolbar ql-snow"},[n("btn-bold",{on:{click:t.handleBold},model:{value:t.bold,callback:function(e){t.bold=e},expression:"bold"}}),t._v(" "),n("btn-italic",{on:{click:t.handleItalic},model:{value:t.italic,callback:function(e){t.italic=e},expression:"italic"}}),t._v(" "),n("btn-underline",{on:{click:t.handleUnderline},model:{value:t.underline,callback:function(e){t.underline=e},expression:"underline"}}),t._v(" "),n("btn-fontsize",{on:{change:t.handleFontsize},model:{value:t.fontsize,callback:function(e){t.fontsize=e},expression:"fontsize"}}),t._v(" "),n("btn-color",{on:{select:t.handleColor},model:{value:t.color,callback:function(e){t.color=e},expression:"color"}})],1)])},staticRenderFns:[]};var M={name:"mark-editor",data:function(){return{content:"",disabled:!0,fullscreen:!1}},components:{quillEditor:n("VU/8")($,E,!1,function(t){n("Pmg4")},"data-v-6d98f9bc",null).exports},mounted:function(){var t=this;window.writeMarkContent=function(e,n){try{t.content=e||"",t.disabled=!!n,t.fullscreen=!("fullscreen"!==n)}catch(t){console.error(t)}},window.readMarkContent=function(){try{var e=t.content;return window.markEditor&&window.markEditor.readMarkContent(e),e}catch(t){console.error(t)}}}},N={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"mark-editor"},[n("quill-editor",{attrs:{disabled:t.disabled,fullscreen:t.fullscreen},model:{value:t.content,callback:function(e){t.content=e},expression:"content"}})],1)},staticRenderFns:[]};var O=n("VU/8")(M,N,!1,function(t){n("CZJ2")},"data-v-fb6ab728",null).exports;o.default.use(u.a);var F=new u.a({routes:[{path:"/",name:"MarkEditor",component:O}]});n("tvR6");o.default.config.productionTip=!1,o.default.use(l.a),window.writeMarkContent=function(){},window.readMarkContent=function(){},new o.default({el:"#app",router:F,components:{App:a},template:"<App/>"})},Pmg4:function(t,e){},erWL:function(t,e){},"r+QJ":function(t,e){},tvR6:function(t,e){}},["NHnr"]);