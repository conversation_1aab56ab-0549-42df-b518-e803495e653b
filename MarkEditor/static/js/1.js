webpackJsonp([1],{Eu8a:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={name:"quill-button-fontsize",props:{value:{type:String,default:"20px"}},data:function(){return{fontSize:["16px","18px","20px","22px","24px","28px","32px","36px"],fontStep:2}},watch:{value:function(){this.setFontStep(this.value)}},methods:{handleChange:function(){this.$emit("change",this.fontSize[this.fontStep]),this.$emit("input",this.fontSize[this.fontStep])},formatTooltip:function(t){return this.fontSize[t]},setFontStep:function(t){try{t=t||this.value;for(var e=this.fontSize.length,n=0;n<e;n++){var i=this.fontSize[n];t.toLowerCase()===i&&(this.fontStep=n)}}catch(t){console.error(t)}}},mounted:function(){this.setFontStep(this.value)}},o={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"quill-fontsize"},[n("span",{staticClass:"size-mini"},[t._v("A")]),t._v(" "),n("el-slider",{attrs:{step:1,min:0,max:7,"show-tooltip":!0,"format-tooltip":t.formatTooltip},on:{change:t.handleChange},model:{value:t.fontStep,callback:function(e){t.fontStep=e},expression:"fontStep"}}),t._v(" "),n("span",{staticClass:"size-max"},[t._v("A")])],1)},staticRenderFns:[]};var a=n("VU/8")(i,o,!1,function(t){n("ahaQ")},"data-v-98d615ba",null);e.default=a.exports},ahaQ:function(t,e){}});