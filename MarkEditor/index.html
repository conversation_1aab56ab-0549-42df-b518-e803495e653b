<!DOCTYPE html><html><head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><title>批注编辑器</title><script>window.startTime = Date.now()
      var resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize'
      function calc() {
        document.documentElement.style.fontSize = (window.innerWidth / 19.2) + 'px'
      }
      document.documentElement.style.fontSize = (window.innerWidth / 19.2) + 'px'
      window.addEventListener(resizeEvt, calc)
      document.addEventListener('DOMContentLoaded', calc)</script><link href=./static/css/app.css rel=stylesheet></head><body><div id=app></div><script type=text/javascript src=./static/js/manifest.js></script><script type=text/javascript src=./static/js/vendor.js></script><script type=text/javascript src=./static/js/app.js></script></body></html>