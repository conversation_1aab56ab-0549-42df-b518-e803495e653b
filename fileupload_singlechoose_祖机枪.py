import json
import os
import sys

import requests
from zhix<PERSON>wang.account import login_playwright
import base64
import webbrowser

import requests
import json
import zhixuewang
import subprocess
import time
import psutil


def is_process_running(process_name):
    """检查是否有正在运行的进程包含给定的名称。"""
    for proc in psutil.process_iter(['name']):
        try:
            if process_name.lower() in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False

def start_executable():
    process_name = 'geetest_api_packed.exe'

    if not is_process_running(process_name):
        try:
            # 使用 cmdstart 命令启动可执行文件
            command = f'start {process_name}'
            subprocess.Popen(command, shell=True)
            print("等待验证码API启动...")
            time.sleep(3)
            print("可执行文件启动成功。")
        except Exception as e:
            print(f"发生错误：{e}")
    else:
        print(f"{process_name} 已在运行。")

start_executable()




class ZhiXueException(Exception):
    pass


class ZhiXue:
    def __init__(self, user_account: str, password: str):
        self.user_account = user_account
        self.password = password
        self.user_id = None

    def login_student(self) -> requests.Session:
        session = zhixuewang.session.get_basic_session()

        try:
            captcha_data, captcha_id = self._login_step_0(session)
            lt, execution = self._login_step_1(session)
            st = self._login_step_2(session, lt, execution, captcha_data, captcha_id)
            self._login_step_3(session, st)
            print("登录成功.")
            return session
        except ZhiXueException as e:
            print(f"登录失败: {e}")

    def _login_step_0(self, session: requests.Session) -> tuple:
        print("    [-1/6] 初始化登录.")
        for i in range(3):
            try:
                captcha_data = requests.get("http://127.0.0.1:8080/get_geetest", timeout=4).json()["data"]
                if captcha_data["result"] != "success":
                    raise ZhiXueException("登录智学网异常: 验证码获取失败.")
                break
            except Exception as exc:
                print(exc)
        else:
            raise ZhiXueException("登录智学网异常: 验证码获取失败.")

        url = "https://www.zhixue.com/edition/login?from=web_login"
        data = {
            "appId": "zx-container-client",
            "captchaType": "third",
            "thirdCaptchaExtInfo[lot_number]": captcha_data["seccode"]["lot_number"],
            "thirdCaptchaExtInfo[pass_token]": captcha_data["seccode"]["pass_token"],
            "thirdCaptchaExtInfo[gen_time]": captcha_data["seccode"]["gen_time"],
            "thirdCaptchaExtInfo[captcha_output]": captcha_data["seccode"]["captcha_output"],
            "loginName": self.user_account,
            "password": self.password,
        }
        result = session.post(url, params={"from": "web_login"}, data=data).json()
        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['message']}")

        self.user_id = result["data"]["userId"]
        return (captcha_data, result["data"]["captchaId"])

    def _login_step_1(self, session: requests.Session) -> tuple:
        print("    [0/6] 发送登录请求.")
        if "&" in self.password:
            raise ZhiXueException("登录智学网异常: 不支持登录密码包含 & 的账号.")

        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {"service": "https://www.zhixue.com:443/ssoservice.jsp"}
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" in result["data"]:
            raise ZhiXueException("登录智学网异常: 此会话已登录.")

        lt = result["data"]["lt"]
        execution = result["data"]["execution"]
        return (lt, execution)

    def _login_step_2(self, session: requests.Session, lt: str, execution: str, captcha_data: dict, captcha_id: str) -> str:
        print("    [1/6] 发送账号密码.")
        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {
            "service": "https://www.zhixue.com:443/ssoservice.jsp",
            "captchaId": captcha_id,
            "captchaType": "third",
            "thirdCaptchaParam": captcha_data["seccode"],
            "version": "v3",
            "_eventId": "submit",
            "key": "auto",
            "lt": lt,
            "execution": execution,
            "username": self.user_account,
            "password": self.password,
        }
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" not in result["data"]:
            raise ZhiXueException("登录智学网异常: st 未找到.")

        st = result["data"]["st"]
        return st

    def _login_step_3(self, session: requests.Session, st: str) -> None:
        print("    [2/6] 发送登录凭证.")
        url = "https://www.zhixue.com/ssoservice.jsp"
        data = {"ticket": st}
        result = session.post(url, params=data).text
        result = result.split("\n", 1)[0]

        if "<!DOCTYPE HTML" in result:
            raise ZhiXueException("登录智学网异常: 服务器 IP 被智学网封禁.")

        if result != "success":
            raise ZhiXueException(f"登录智学网异常: {result}")

        session.cookies.set("uname", base64.b64encode(self.user_account.encode()).decode())

# 使用示例
zhi_xue = ZhiXue(user_account="zhixue@upload_download", password="xianxing@password")
session = zhi_xue.login_student()
student = zhixuewang.account.StudentAccount(session).set_base_info()
print(student.role)
import cffi
import tkinter
from tkinter import filedialog
def upload_file_to_zhixue(student_id, token, local_file_path):
    import requests
    import json
    import uuid

    def generate_uuid():
        return str(uuid.uuid4())
    filename = f'EDU_{student_id}_{generate_uuid()}.docx'
    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    filepath = "middleHomework/android/zxzy/" + today + filename
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": filename,
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
    def percentage(consumed_bytes, total_bytes):
        if total_bytes:
            rate = int(100 * (float(consumed_bytes) / float(total_bytes)))
            print('\r{0}% '.format(rate), end='')
            sys.stdout.flush()
    result = bucket.put_object_from_file(filepath, local_file_path,progress_callback=percentage)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"
    # HTTP返回码。
    print('http status: {0}'.format(result.status))
    # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    print('request_id: {0}'.format(result.request_id))
    # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    print('ETag: {0}'.format(result.etag))
    # HTTP响应头部。
    print('date: {0}'.format(result.headers['date']))
def edit_file_to_zhixue(student_id, token, local_file_path, filename, filepath):
    import requests
    import json
    import uuid

    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": filename,
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e638-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
    def percentage(consumed_bytes, total_bytes):
        if total_bytes:
            rate = int(100 * (float(consumed_bytes) / float(total_bytes)))
            print('\r{0}% '.format(rate), end='')
            sys.stdout.flush()
    result = bucket.put_object_from_file(filepath, local_file_path,progress_callback=percentage)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"
    # HTTP返回码。
    print('http status: {0}'.format(result.status))
    # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    print('request_id: {0}'.format(result.request_id))
    # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    print('ETag: {0}'.format(result.etag))
    # HTTP响应头部。
    print('date: {0}'.format(result.headers['date']))


# tk库选择多个文件
root = tkinter.Tk()
# root.withdraw()
filepaths = tkinter.filedialog.askopenfilenames()
print(filepaths)
res_list_new = []
for filepath in filepaths:
    # 获取无后缀文件名
    filename = os.path.basename(filepath)
    filename = os.path.splitext(filename)[0]
    print(filename)

    # 上传文件
    print(f'文件 {filename} 正在上传...')
    file_url = upload_file_to_zhixue(student.id, student.get_auth_header()['XToken'], filepath)
    print(file_url)
    file_size = os.path.getsize(filepath)  # 获取文件尺寸
    current_time = int(time.time())  # 获取当前时间戳

    res_list_new.append({
        "name": filename,
        "filetype": os.path.splitext(filepath)[1][1:], # 由路径获取扩展名
        "url": file_url,
        "timestamp": current_time,
        "file_size": file_size
    })


print(res_list_new)
# 更新资源列表
reslisturl = "https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/10/01/linear_pan_A1hAfoSfaeC.json"

# res_list = list(json.loads(requests.get(reslisturl).text))
# 先请求，如果404采用空列表
# 请求
response = requests.get(reslisturl)
# res_list = ["密码为linear_+文件后缀名，如文件名为1.1.1068版本三件套(2024.11.08)，密码为linear_zip"]
if response.status_code == 404:
    res_list = ["密码为linear_+文件后缀名，如文件名为1.1.1068版本三件套(2024.11.08)，密码为linear_zip"]
else:
    res_list = list(json.loads(response.text))

for i in res_list_new:
    res_list.append(i)
print(res_list)

with open('res_list_zujiqiang.json', 'w') as f:
    f.write(json.dumps(res_list))

try:
    filename = "linear_pan_A1hAfoSfaeC.json"
    filepath = "middleHomework/android/zxzy/2024/10/01/linear_pan_A1hAfoSfaeC.json"
    print(filename, filepath)
    local_file_path = "res_list_zujiqiang.json"
    print('正在上传...')
    file_url = edit_file_to_zhixue(student.id, student.get_auth_header()['XToken'], local_file_path, filename,
                                   filepath)
    print(file_url)
    print('文件修改完成！')
except Exception as e:
    print('error:', e)
