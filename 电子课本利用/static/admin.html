<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户端管理页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2196F3;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .client-list {
            margin-bottom: 20px;
        }
        
        .client-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .client-id {
            flex: 1;
            font-family: monospace;
        }
        
        .client-status {
            margin-right: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .connected {
            background-color: #4CAF50;
        }
        
        .disconnected {
            background-color: #f44336;
        }
        
        .control-panel {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #1976D2;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        
        .success {
            background-color: #E8F5E9;
            color: #2E7D32;
        }
        
        .error {
            background-color: #FFEBEE;
            color: #C62828;
        }
        
        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
            display: none;
        }
        
        .progress-bar {
            height: 20px;
            background-color: #4CAF50;
            text-align: center;
            line-height: 20px;
            color: white;
            transition: width 0.3s;
            border-radius: 4px;
        }
        
        .progress-info {
            margin-top: 5px;
            font-size: 14px;
            color: #666;
        }
        
        .client-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .client-item:hover {
            background-color: #e3f2fd;
        }
        
        .client-item.selected {
            background-color: #e3f2fd;
            border-left: 3px solid #2196F3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>客户端管理</h1>

        <div class="client-list" id="clientList">
            <h2>已连接的客户端</h2>
            <!-- 客户端列表将通过JavaScript动态添加 -->
        </div>

        <div class="control-panel">
            <h2>控制面板</h2>
            <div class="input-group">
                <input type="text" id="urlInput" placeholder="请输入要请求的URL">
                <button id="sendToAll" onclick="sendToAllClients()">发送给所有客户端</button>
                <button id="sendToSelected" onclick="sendToSelectedClient()" disabled>发送给选中的客户端</button>
            </div>
            
            <div id="progressContainer" class="progress-container">
                <div id="progressBar" class="progress-bar" style="width: 0%">0%</div>
                <div id="progressInfo" class="progress-info"></div>
            </div>
            
            <div id="result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="./socket.io.js"></script>
    <script>
        const socket = io(window.location.origin + '?admin=true');
        let selectedClientId = null;
        const clients = new Set();
        let currentTransferStatus = {};

        socket.on('connect', () => {
            console.log('Connected to server');
        });

        socket.on('client_list_update', (data) => {
            updateClientList(data.clients);
        });

        socket.on('client_connected', (data) => {
            clients.add(data.client_id);
            updateClientList(Array.from(clients));
        });

        socket.on('client_disconnected', (data) => {
            clients.delete(data.client_id);
            updateClientList(Array.from(clients));
        });
        
        // 监听传输进度更新
        socket.on('transfer_progress', (data) => {
            updateProgress(data.client_id, data.progress, data.info);
        });
        
        // 监听传输完成事件
        socket.on('transfer_complete', (data) => {
            completeProgress(data.client_id, data.success, data.message);
        });

        function updateClientList(clientList) {
            const clientListElement = document.getElementById('clientList');
            const clientsHtml = clientList.map(clientId => `
                <div class="client-item ${selectedClientId === clientId ? 'selected' : ''}" 
                     onclick="selectClient('${clientId}')" 
                     id="client-${clientId}">
                    <div class="client-id">${clientId}</div>
                    <div class="client-status">
                        <span class="status-indicator connected"></span>
                        在线
                    </div>
                </div>
            `).join('');

            clientListElement.innerHTML = `
                <h2>已连接的客户端</h2>
                ${clientsHtml || '<p>暂无连接的客户端</p>'}
            `;
        }

        function selectClient(clientId) {
            selectedClientId = clientId;
            document.querySelectorAll('.client-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.getElementById(`client-${clientId}`).classList.add('selected');
            document.getElementById('sendToSelected').disabled = false;
        }

        function sendToAllClients() {
            const url = document.getElementById('urlInput').value.trim();
            if (!url) {
                showResult('请输入URL', false);
                return;
            }

            showProgress(0, '正在发送请求...');
            
            fetch('/request-url-fetch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: url
                    })
                })
                .then(response => response.json())
                .then(data => {
                    showResult(data.message, true);
                })
                .catch(error => {
                    showResult(error.message, false);
                    hideProgress();
                });
        }

        function sendToSelectedClient() {
            if (!selectedClientId) {
                showResult('请先选择一个客户端', false);
                return;
            }

            const url = document.getElementById('urlInput').value.trim();
            if (!url) {
                showResult('请输入URL', false);
                return;
            }

            showProgress(0, '正在发送请求...');
            
            fetch('/request-url-fetch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: url,
                        client_id: selectedClientId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    showResult(data.message, true);
                })
                .catch(error => {
                    showResult(error.message, false);
                    hideProgress();
                });
        }
        
        function showProgress(percent, info) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressInfo = document.getElementById('progressInfo');
            
            progressContainer.style.display = 'block';
            progressBar.style.width = `${percent}%`;
            progressBar.textContent = `${percent}%`;
            progressInfo.textContent = info || '';
        }
        
        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }
        
        function updateProgress(clientId, percent, info) {
            // 更新当前传输状态
            currentTransferStatus[clientId] = {
                percent: percent,
                info: info
            };
            
            // 如果是当前选中的客户端或者是全局传输，则更新进度条
            if (!selectedClientId || selectedClientId === clientId) {
                showProgress(percent, info);
            }
        }
        
        function completeProgress(clientId, success, message) {
            // 清除当前传输状态
            delete currentTransferStatus[clientId];
            
            // 如果是当前选中的客户端或者是全局传输，则隐藏进度条并显示结果
            if (!selectedClientId || selectedClientId === clientId) {
                hideProgress();
                showResult(message, success);
            }
        }

        function showResult(message, isSuccess) {
            const resultElement = document.getElementById('result');
            resultElement.textContent = message;
            resultElement.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultElement.style.display = 'block';

            setTimeout(() => {
                resultElement.style.display = 'none';
            }, 5000); // 5秒后自动隐藏结果提示
        }

        // 监听URL获取结果
        socket.on('url_fetch_result', (data) => {
            const clientId = data.client_id || '未知客户端';
            const success = data.success;
            const message = success ? 
                `客户端 ${clientId} 成功获取URL: ${data.url}` : 
                `客户端 ${clientId} 获取URL失败: ${data.error}`;
            
            completeProgress(clientId, success, message);
        });

        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('Admin page loaded');
        });
    </script>
</body>

</html>