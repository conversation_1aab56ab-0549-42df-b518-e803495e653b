# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['zhixuewang_submit_multifile_out.py'],
    pathex=['G:\\zhixue\\venv\\Lib\\site-packages', 'G:\\zhixue\\venv\\Lib\\site-packages\\zhixuewang'],
    binaries=[],
    datas=[],
    hiddenimports=['zhixuewang'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='zhixuewang_submit_multifile_out',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
