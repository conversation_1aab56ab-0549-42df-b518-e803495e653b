# coding:utf-8
import webbrowser
from zhixuewang import login_student
student = login_student(input('请输入智学网用户名：'), input('请输入智学网密码：'))
# 打开网页
webbrowser.open('https://www.zhixue.com/login.html')
input('请先在弹出的浏览器中登录智学网，登录完成后按回车键继续...')
print('正在获取所有的作业...')
homeworks = student.get_homeworks(200)
print('正在筛选所有的自由出题作业...')
common_homeworks = []
i = 1
for homework in homeworks:
    if homework.type.code == 105:
        common_homeworks.append(homework)
        print(f'{i}.{homework.title}')
        i += 1
print('筛选完成！')
choice = common_homeworks[int(input('请选择要提交的打卡作业：')) - 1]
webbrowser.open(f'https://www.zhixue.com/middlehomework/web-teacher/views/#/work-center/free-topic-report/explanation?hwId={choice.id}&hwType=105&reviseType=3')
input('作业详情应该已经弹出，按回车键退出...')
