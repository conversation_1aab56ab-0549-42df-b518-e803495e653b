import requests
import json
import hashlib
import base64
import random
import os
from typing import List, Dict, Optional
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from Crypto.Util.Padding import pad


class NetEaseEncryption:
    """网易云音乐加密类，基于项目中的加密实现"""

    def __init__(self):
        # 基于项目中的加密常量
        self.iv = b'0102030405060708'
        self.preset_key = b'0CoJUm6Qyw8W8jud'
        self.eapi_key = b'e82ckenh8dichen8'
        self.base62 = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        self.public_key = '''-----BEGIN PUBLIC KEY-----  
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDgtQn2JZ34ZC28NWYpAUd98iZ37BUrX/aKzmFbt7clFSs6sXqHauqKWqdtLkF2KexO40H1YTX8z2lSgBBOAxLsvaklV8k4cBFK9snQXE9/DDaFt6Rr7iVZMldczhC0JNgTz+SHXT6CBHuX3e9SdB1Ua44oncaTWz7OBGLbCiK45wIDAQAB  
-----END PUBLIC KEY-----'''

    def _aes_encrypt(self, text: bytes, key: bytes, mode='CBC') -> bytes:
        """AES加密"""
        if mode == 'CBC':
            cipher = AES.new(key, AES.MODE_CBC, self.iv)
            return cipher.encrypt(pad(text, 16))
        else:  # ECB
            cipher = AES.new(key, AES.MODE_ECB)
            return cipher.encrypt(pad(text, 16))

    def _rsa_encrypt(self, text: bytes) -> str:
        """RSA加密"""
        # 填充到128字节
        text = bytes(128 - len(text)) + text

        # RSA加密
        rsa_key = RSA.import_key(self.public_key)
        cipher = PKCS1_v1_5.new(rsa_key)
        encrypted = cipher.encrypt(text)
        return encrypted.hex()

    def weapi_encrypt(self, data: dict) -> dict:
        """  
        weapi加密方法  
        """  # 4-0)

        text=json.dumps(data, separators=(',', ':'))

        # 生成随机密钥
        secret_key = bytes([ord(self.base62[random.randint(0, 61)]) for _ in range(16)])

        # 第一次AES加密
        first_encrypt = self._aes_encrypt(text.encode(), self.preset_key)

        # 第二次AES加密
        second_encrypt = self._aes_encrypt(base64.b64encode(first_encrypt), secret_key)

        # RSA加密密钥
        enc_sec_key = self._rsa_encrypt(secret_key[::-1])  # 反转密钥

        return {
            'params': base64.b64encode(second_encrypt).decode(),
            'encSecKey': enc_sec_key
        }

    def eapi_encrypt(self, url: str, data: dict) -> dict:
        """  
        eapi加密方法  
        """  # 4-1)

        text=json.dumps(data, separators=(',', ':')) if isinstance(data, dict) else str(data)
        message = f"nobody{url}use{text}md5forencrypt"
        digest = hashlib.md5(message.encode()).hexdigest()
        data_str = f"{url}-36cd479b6b5-{text}-36cd479b6b5-{digest}"

        encrypted = self._aes_encrypt(data_str.encode(), self.eapi_key, mode='ECB')
        return {
            'params': encrypted.hex().upper()
        }


class NetEaseSearcher:
    """网易云音乐搜索类"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36',
            'Origin': 'https://music.163.com',
            'Referer': 'https://music.163.com/'
        })
        self.encryption = NetEaseEncryption()
        self.limit = 30
        self.success_code = 200

    def _format_duration(self, ms: int) -> str:
        """格式化播放时长"""
        seconds = ms // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"

    def _get_singer_names(self, artists: List[dict]) -> str:
        """获取歌手名称""" # 4-2)

        names=[artist.get('name', '') for artist in artists]
        return '、'.join(names)

    def _handle_search_result(self, raw_list: List[dict]) -> List[dict]:
        """
        处理搜索结果
        """
        # 4-3)

        if not raw_list:
            return []

        result_list = []
        for item in raw_list:
            # 获取音质类型
            types = []
            privilege = item.get('privilege', {})

            # 根据权限设置音质
            if privilege.get('maxBrLevel') == 'hires':
                if item.get('hr'):
                    types.append({'type': 'flac24bit', 'size': None})

            max_br = privilege.get('maxbr', 0)
            if max_br >= 999000:
                if item.get('sq'):
                    types.append({'type': 'flac', 'size': None})
            if max_br >= 320000:
                if item.get('h'):
                    types.append({'type': '320k', 'size': None})
            if max_br >= 128000:
                if item.get('l'):
                    types.append({'type': '128k', 'size': None})

            types.reverse()

            # 构建歌曲信息
            song_info = {
                'singer': self._get_singer_names(item.get('ar', [])),
                'name': item.get('name', ''),
                'albumName': item.get('al', {}).get('name', ''),
                'albumId': item.get('al', {}).get('id'),
                'source': 'wy',
                'interval': self._format_duration(item.get('dt', 0)),
                'songmid': item.get('id'),
                'img': item.get('al', {}).get('picUrl', ''),
                'lrc': None,
                'types': types,
                'typeUrl': {}
            }
            result_list.append(song_info)

        return result_list

    def music_search(self, keyword: str, page: int = 1, limit: Optional[int] = None) -> dict:
        """
        音乐搜索方法
        """
        # 4-4)

        if limit is None:
            limit = self.limit

        # 构建搜索参数
        search_data = {
            's': keyword,
            'type': 1,  # 1: 单曲
            'limit': limit,
            'total': page == 1,
            'offset': limit * (page - 1)
        }

        # 使用eapi加密
        encrypted_data = self.encryption.eapi_encrypt('/api/cloudsearch/pc', search_data)

        try:
            # 发送请求 [6](#4-5)

            response = self.session.post(
                'http://interface.music.163.com/eapi/batch',
                data=encrypted_data,
                headers={
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36',
                    'origin': 'https://music.163.com'
                }
            )

            result = response.json()

            if result.get('code') != self.success_code:
                raise Exception(f'搜索失败: {result.get("message", "未知错误")}')

                # 处理搜索结果
            songs = result.get('result', {}).get('songs', [])
            processed_list = self._handle_search_result(songs)

            total = result.get('result', {}).get('songCount', 0)
            all_page = (total + limit - 1) // limit if total > 0 else 1

            return {
                'list': processed_list,
                'allPage': all_page,
                'limit': limit,
                'total': total,
                'source': 'wy'
            }

        except Exception as e:
            print(f"搜索出错: {e}")
            return {
                'list': [],
                'allPage': 1,
                'limit': limit,
                'total': 0,
                'source': 'wy'
            }

    def search(self, keyword: str, page: int = 1, limit: Optional[int] = None, retry_num: int = 0) -> dict:
        """
        搜索方法，包含重试逻辑
        """
        # 4-6)

        if retry_num > 3:
            raise Exception('重试次数超过限制')

        if limit is None:
            limit = self.limit

        try:
            result = self.music_search(keyword, page, limit)

            if not result or not result.get('list'):
                if retry_num < 3:
                    return self.search(keyword, page, limit, retry_num + 1)

            return result

        except Exception as e:
            if retry_num < 3:
                return self.search(keyword, page, limit, retry_num + 1)
            else:
                raise e


class NetEaseSearchManager:
    """搜索管理器"""

    def __init__(self):
        self.searcher = NetEaseSearcher()
        self.search_history = []

    def search_music(self, keyword: str, page: int = 1) -> dict:
        """执行音乐搜索"""
        result = self.searcher.search(keyword, page)

        # 添加到搜索历史
        if keyword and keyword not in self.search_history:
            self.search_history.insert(0, keyword)
            if len(self.search_history) > 10:
                self.search_history.pop()

        return result

    def get_search_history(self) -> List[str]:
        """获取搜索历史"""
        return self.search_history.copy()

    # 使用示例


if __name__ == "__main__":
    # 创建搜索管理器
    wy_search_manager = NetEaseSearchManager()

    # 搜索音乐
    keyword = "lemon"
    print(f"搜索关键词: {keyword}")

    try:
        result = wy_search_manager.search_music(keyword)
        print(f"搜索结果总数: {result['total']}")

        # 显示前10个结果
        for i, song in enumerate(result['list'][:10], 1):
            print(f"{i}. {song['name']} - {song['singer']} ({song['interval']}) {song['songmid']}")

            # 显示搜索历史
        # print(f"\n搜索历史: {search_manager.get_search_history()}")

    except Exception as e:
        print(f"搜索失败: {e}")