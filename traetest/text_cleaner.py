import re
import os
import shutil
from datetime import datetime

def clean_text_file(file_path):
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 '{file_path}' 不存在")
        return False
    
    # 创建备份文件
    backup_path = f"{file_path}.{datetime.now().strftime('%Y%m%d_%H%M%S')}.bak"
    try:
        shutil.copy2(file_path, backup_path)
        print(f"已创建备份文件：{backup_path}")
    except Exception as e:
        print(f"创建备份文件时出错：{str(e)}")
        return False

    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式删除指定内容
        pattern = r"网页版章节内容慢.*?几乎是没有办法清洗干净。"
        cleaned_content = re.sub(pattern, '', content, flags=re.DOTALL)

        # 写入处理后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)

        print("文件处理完成")
        return True

    except Exception as e:
        print(f"处理文件时出错：{str(e)}")
        # 发生错误时恢复备份
        try:
            shutil.copy2(backup_path, file_path)
            print("已恢复原文件")
        except Exception as restore_error:
            print(f"恢复文件时出错：{str(restore_error)}")
        return False

def main():
    file_path = input("请输入要处理的文本文件路径：")
    if clean_text_file(file_path):
        print("文件清理成功！")
    else:
        print("文件清理失败！")

if __name__ == '__main__':
    main()