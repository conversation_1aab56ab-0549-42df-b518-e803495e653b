import json
import os

import requests
from zhi<PERSON>uewang import login_student
def edit_file_to_zhixue(local_dir):
    reslist = []
    for root, dirs, files in os.walk(local_dir):
        # 计算当前目录相对于初始目录的相对路径
        relative_path = os.path.relpath(root, local_dir)
        if relative_path == '.':
            relative_path = ''
        for file in files:
            local_file_path = os.path.join(root, file)
            if not 'service-worker.js' in local_file_path:
                reslist.append(local_file_path.replace("html/.pvz/","").replace("\\","/"))
    print(reslist)
    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
try:
    # student = login_student(input('请输入智学网用户名：'), input('请输入密码：'))
    import re
    # htmlzipurl = "https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/02/12/EDU_1500000100217351485_f59bd141-3423-4b53-987d-4f522412ff13.html"
    # filename_pattern = r'EDU_.*\.html'
    # filepath_pattern = r'middleHomework.*\.html'
    # filename = re.search(filename_pattern, htmlzipurl)[0]
    # filepath = re.search(filepath_pattern, htmlzipurl)[0]
    # print(filename, filepath)
    local_dir = "html/.pvz/"
    print('正在上传...')
    file_url = edit_file_to_zhixue(local_dir)
except Exception as e:
    print('error:', e)