import json
import os

import requests
from zhix<PERSON>wang.account import login_playwright
import base64
import webbrowser

import requests
import json
import zhixuewang
import subprocess
import time
import psutil

def is_process_running(process_name):
    """检查是否有正在运行的进程包含给定的名称。"""
    for proc in psutil.process_iter(['name']):
        try:
            if process_name.lower() in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False

def start_executable():
    process_name = 'geetest_api_packed.exe'

    if not is_process_running(process_name):
        try:
            # 使用 cmdstart 命令启动可执行文件
            command = f'start {process_name}'
            subprocess.Popen(command, shell=True)
            print("等待验证码API启动...")
            time.sleep(3)
            print("可执行文件启动成功。")
        except Exception as e:
            print(f"发生错误：{e}")
    else:
        print(f"{process_name} 已在运行。")

start_executable()




class ZhiXueException(Exception):
    pass


class ZhiXue:
    def __init__(self, user_account: str, password: str):
        self.user_account = user_account
        self.password = password
        self.user_id = None

    def login_student(self) -> requests.Session:
        session = zhixuewang.session.get_basic_session()

        try:
            captcha_data, captcha_id = self._login_step_0(session)
            lt, execution = self._login_step_1(session)
            st = self._login_step_2(session, lt, execution, captcha_data, captcha_id)
            self._login_step_3(session, st)
            print("登录成功.")
            return session
        except ZhiXueException as e:
            print(f"登录失败: {e}")

    def _login_step_0(self, session: requests.Session) -> tuple:
        print("    [-1/6] 初始化登录.")
        for i in range(3):
            try:
                captcha_data = requests.get("http://127.0.0.1:8080/get_geetest", timeout=4).json()["data"]
                if captcha_data["result"] != "success":
                    raise ZhiXueException("登录智学网异常: 验证码获取失败.")
                break
            except Exception as exc:
                print(exc)
        else:
            raise ZhiXueException("登录智学网异常: 验证码获取失败.")

        url = "https://www.zhixue.com/edition/login?from=web_login"
        data = {
            "appId": "zx-container-client",
            "captchaType": "third",
            "thirdCaptchaExtInfo[lot_number]": captcha_data["seccode"]["lot_number"],
            "thirdCaptchaExtInfo[pass_token]": captcha_data["seccode"]["pass_token"],
            "thirdCaptchaExtInfo[gen_time]": captcha_data["seccode"]["gen_time"],
            "thirdCaptchaExtInfo[captcha_output]": captcha_data["seccode"]["captcha_output"],
            "loginName": self.user_account,
            "password": self.password,
        }
        result = session.post(url, params={"from": "web_login"}, data=data).json()
        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['message']}")

        self.user_id = result["data"]["userId"]
        return (captcha_data, result["data"]["captchaId"])

    def _login_step_1(self, session: requests.Session) -> tuple:
        print("    [0/6] 发送登录请求.")
        if "&" in self.password:
            raise ZhiXueException("登录智学网异常: 不支持登录密码包含 & 的账号.")

        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {"service": "https://www.zhixue.com:443/ssoservice.jsp"}
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" in result["data"]:
            raise ZhiXueException("登录智学网异常: 此会话已登录.")

        lt = result["data"]["lt"]
        execution = result["data"]["execution"]
        return (lt, execution)

    def _login_step_2(self, session: requests.Session, lt: str, execution: str, captcha_data: dict, captcha_id: str) -> str:
        print("    [1/6] 发送账号密码.")
        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {
            "service": "https://www.zhixue.com:443/ssoservice.jsp",
            "captchaId": captcha_id,
            "captchaType": "third",
            "thirdCaptchaParam": captcha_data["seccode"],
            "version": "v3",
            "_eventId": "submit",
            "key": "auto",
            "lt": lt,
            "execution": execution,
            "username": self.user_account,
            "password": self.password,
        }
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" not in result["data"]:
            raise ZhiXueException("登录智学网异常: st 未找到.")

        st = result["data"]["st"]
        return st

    def _login_step_3(self, session: requests.Session, st: str) -> None:
        print("    [2/6] 发送登录凭证.")
        url = "https://www.zhixue.com/ssoservice.jsp"
        data = {"ticket": st}
        result = session.post(url, params=data).text
        result = result.split("\n", 1)[0]

        if "<!DOCTYPE HTML" in result:
            raise ZhiXueException("登录智学网异常: 服务器 IP 被智学网封禁.")

        if result != "success":
            raise ZhiXueException(f"登录智学网异常: {result}")

        session.cookies.set("uname", base64.b64encode(self.user_account.encode()).decode())

# 使用示例
zhi_xue = ZhiXue(user_account="310116200805160630", password="56415880wen")
session = zhi_xue.login_student()
student = zhixuewang.account.StudentAccount(session).set_base_info()
def edit_file_to_zhixue(token, local_dir):
    import requests
    import json
    import uuid

    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx",
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')
    oss_base_prefix = 'middleHomework/android/zxzy/2024/06/16/html'
    for root, dirs, files in os.walk(local_dir):
        # 计算当前目录相对于初始目录的相对路径
        relative_path = os.path.relpath(root, local_dir)
        if relative_path == '.':
            relative_path = ''

        # 过滤以.开头的文件夹
        dirs[:] = [d for d in dirs if not d.startswith('.')]

        for file in files:
            # 跳过以.开头的文件
            if file.startswith('.'):
                continue

            local_file_path = os.path.join(root, file)
            # 保持文件在OSS上的路径与本地相对路径一致
            oss_file_path = os.path.join(oss_base_prefix, relative_path, file).replace('\\', '/')
            # 上传文件
            bucket.put_object_from_file(oss_file_path, local_file_path)
            print(f'Uploaded {local_file_path} to {oss_file_path}')
    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/02/12/{local_dir}"
    # HTTP返回码。
    print('http status: {0}'.format(result.status))
    # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    print('request_id: {0}'.format(result.request_id))
    # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    print('ETag: {0}'.format(result.etag))
    # HTTP响应头部。
    print('date: {0}'.format(result.headers['date']))


# 先把html文件夹下所有文件和文件夹打包为zip
try:
    # student = login_student(input('请输入智学网用户名：'), input('请输入密码：'))
    import re
    # htmlzipurl = "https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/02/12/EDU_1500000100217351485_f59bd141-3423-4b53-987d-4f522412ff13.html"
    # filename_pattern = r'EDU_.*\.html'
    # filepath_pattern = r'middleHomework.*\.html'
    # filename = re.search(filename_pattern, htmlzipurl)[0]
    # filepath = re.search(filepath_pattern, htmlzipurl)[0]
    # print(filename, filepath)
    local_dir = "html2/"
    print('正在上传...')
    file_url = edit_file_to_zhixue(student.get_auth_header()['XToken'], local_dir)
    print(file_url)
except Exception as e:
    print('error:', e)