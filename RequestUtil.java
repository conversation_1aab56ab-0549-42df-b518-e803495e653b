//
// Decompiled by Jadx - 1400ms
//
package com.iflytek.fsp.shield.android.sdk.util;

import com.iflytek.fsp.shield.android.sdk.exception.SdkClientException;
import com.iflytek.fsp.shield.android.sdk.http.ApiRequest;
import com.iflytek.fsp.shield.android.sdk.http.MultipartFile;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.UUID;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.internal.http.HttpMethod;
import okio.Buffer;

public class RequestUtil {
    private static final String MULTIPART_CONTENT_HEADER_KEY = "Content-Disposition";
    private static final String MULTIPART_CONTENT_HEADER_STRING_VALUE = "form-data; name=\"%s\"";
    private static final String MULTIPART_CONTENT_HEADER_VALUE = "form-data; name=\"%s\";filename=\"%s\"";
    private static final String TAG = "com.iflytek.fsp.shield.android.sdk.util.RequestUtil";

    private static String getSdkVersion() {
        return "1.0.7";
    }

    public static ApiRequest buildSdkRequest(ApiRequest apiRequest, String str, String str2, String str3) {
        apiRequest.setPath(combinePathParam(apiRequest.getPath(), apiRequest.getPathParams()));
        apiRequest.getHeaders().put("S-Auth-Nonce", UUID.randomUUID().toString());
        apiRequest.getHeaders().put("S-Auth-Version", "1");
        Map headers = apiRequest.getHeaders();
        headers.put("S-Auth-Timestamp", System.currentTimeMillis() + "");
        apiRequest.getHeaders().put("S-Auth-GroupId", apiRequest.getGroupId());
        apiRequest.getHeaders().put("User-Agent", "ShieldAndroidSDK");
        apiRequest.getHeaders().put("S-SDK-Version", getSdkVersion());
        if (Util.isEmptyMap(apiRequest.getFormParams()) && Util.isNotEmptyArray(apiRequest.getBody())) {
            apiRequest.getHeaders().put("S-Content-MD5", SignUtil.md5ThenBase64(apiRequest.getBody()));
        }
        apiRequest.getHeaders().put("S-Auth-Stage", str3);
        apiRequest.getHeaders().put("S-Auth-AppId", str);
        signRequest(apiRequest, str, str2);
        return apiRequest;
    }

    public static Request parseToOkRequest(ApiRequest apiRequest, Object obj) {
        String contentType = getContentType(apiRequest);
        String name = apiRequest.getMethod().getName();
        RequestBody create = HttpMethod.requiresRequestBody(name) ? RequestBody.create(MediaType.parse(contentType), "") : null;
        if (HttpMethod.permitsRequestBody(name)) {
            if (Util.isNotEmptyMap(apiRequest.getFormParams())) {
                create = RequestBody.create(MediaType.parse(contentType), buildParamString(apiRequest.getFormParams()));
            } else if (Util.isNotEmptyArray(apiRequest.getBody())) {
                create = RequestBody.create(MediaType.parse(contentType), apiRequest.getBody());
            } else if (Util.isNotEmptyMap(apiRequest.getMultiFormParams())) {
                create = createMultipartBodyBuilder(apiRequest).build();
            }
        }
        for (Map.Entry entry : apiRequest.getHeaders().entrySet()) {
            if (entry.getValue() != null) {
                entry.setValue(encodeHeader((String) entry.getValue()));
            }
        }
        return new Request.Builder().method(apiRequest.getMethod().getName(), create).url(buildUrl(apiRequest)).headers(Headers.of(apiRequest.getHeaders())).tag(obj).build();
    }

    private static MultipartBody.Builder createMultipartBodyBuilder(ApiRequest apiRequest) {
        MultipartBody.Builder type = new MultipartBody.Builder().setType(MultipartBody.FORM);
        Map multiFormParams = apiRequest.getMultiFormParams();
        for (String str : multiFormParams.keySet()) {
            Object obj = multiFormParams.get(str);
            if (obj instanceof byte[]) {
                type.addPart(Headers.of(new String[]{MULTIPART_CONTENT_HEADER_KEY, String.format(MULTIPART_CONTENT_HEADER_STRING_VALUE, str)}), RequestBody.create(MediaType.parse("application/octet-stream; charset=UTF-8"), (byte[]) obj));
            } else if (obj instanceof MultipartFile) {
                MultipartFile multipartFile = (MultipartFile) obj;
                type.addPart(Headers.of(new String[]{MULTIPART_CONTENT_HEADER_KEY, String.format(MULTIPART_CONTENT_HEADER_VALUE, str, multipartFile.getFileName())}), RequestBody.create(MediaType.parse(multipartFile.getContentType()), multipartFile.getFile()));
            } else {
                throw new SdkClientException("Assemble request body error.");
            }
        }
        return type;
    }

    public static ApiRequest cryptRequest(ApiRequest apiRequest, String str) {
        try {
            String str2 = RandomUtils.generateKeyRadom() + "";
            apiRequest.getHeaders().put("S-Auth-RandomKey", CryptoUtils.rsaEncode(str, str2));
            if (Util.isNotEmptyMap(apiRequest.getFormParams())) {
                String aesEncode = CryptoUtils.aesEncode(str2, buildParamString(apiRequest.getFormParams()).getBytes("UTF-8"));
                apiRequest.setFormParams((Map) null);
                apiRequest.setBody(aesEncode.getBytes("UTF-8"));
            } else if (Util.isNotEmptyArray(apiRequest.getBody())) {
                apiRequest.setBody(CryptoUtils.aesEncode(str2, apiRequest.getBody()).getBytes("UTF-8"));
            } else if (Util.isNotEmptyMap(apiRequest.getMultiFormParams())) {
                MultipartBody build = createMultipartBodyBuilder(apiRequest).build();
                Buffer buffer = new Buffer();
                build.writeTo(buffer);
                String aesEncode2 = CryptoUtils.aesEncode(str2, buffer.readByteArray());
                apiRequest.getHeaders().put("Content-Type", build.contentType().toString());
                apiRequest.setMultiFormParams((Map) null);
                apiRequest.setBody(aesEncode2.getBytes("UTF-8"));
            }
            return apiRequest;
        } catch (Exception e) {
            throw new SdkClientException("加密失败", e);
        }
    }

    private static String combinePathParam(String str, Map<String, String> map) {
        if (map == null) {
            return str;
        }
        for (String str2 : map.keySet()) {
            str = str.replace("[" + str2 + "]", map.get(str2));
        }
        return str;
    }

    private static String buildParamString(Map<String, String> map) {
        StringBuilder sb = new StringBuilder();
        if (map != null && map.size() > 0) {
            boolean z = true;
            try {
                for (String str : map.keySet()) {
                    if (z) {
                        z = false;
                    } else {
                        sb.append("&");
                    }
                    sb.append(str);
                    sb.append("=");
                    sb.append(URLEncoder.encode(map.get(str), "UTF-8"));
                }
            } catch (UnsupportedEncodingException e) {
                throw new SdkClientException(e);
            }
        }
        return encodeSpecialChar(sb.toString());
    }

    private static String encodeSpecialChar(String str) {
        return str.replace("+", "%20");
    }

    private static void signRequest(ApiRequest apiRequest, String str, String str2) {
        String path = apiRequest.getPath();
        String buildParamString = buildParamString(apiRequest.getQuerys());
        if (Util.isNotEmptyString(buildParamString)) {
            path = path + "?" + buildParamString;
        }
        apiRequest.getHeaders().put("S-Auth-Signature", SignUtil.sign(str2, path, (String) apiRequest.getHeaders().get("S-Auth-RandomKey"), (String) apiRequest.getHeaders().get("S-Auth-Nonce"), (String) apiRequest.getHeaders().get("S-Auth-Timestamp"), str, (String) apiRequest.getHeaders().get("S-Content-MD5")));
    }

    private static String buildUrl(ApiRequest apiRequest) {
        StringBuilder sb = new StringBuilder();
        sb.append(getUrlPrefix(apiRequest.getScheme()));
        sb.append(apiRequest.getHost());
        sb.append(":");
        sb.append(apiRequest.getPort());
        sb.append(apiRequest.getPath());
        String buildParamString = buildParamString(apiRequest.getQuerys());
        if (Util.isNotEmptyString(buildParamString)) {
            sb.append("?");
            sb.append(buildParamString);
        }
        return sb.toString();
    }

    private static String getUrlPrefix(String str) {
        return "HTTPS".equals(str) ? "https://" : "http://";
    }

    private static String getContentType(ApiRequest apiRequest) {
        String str = (String) apiRequest.getHeaders().get("Content-Type");
        return str != null ? str : apiRequest.getBody() != null ? "application/octet-stream; charset=UTF-8" : (apiRequest.getFormParams() == null || apiRequest.getFormParams().size() <= 0) ? (apiRequest.getMultiFormParams() == null || apiRequest.getMultiFormParams().size() <= 0) ? "application/json; charset=UTF-8" : "multipart/form-data" : "application/x-www-form-urlencoded; charset=UTF-8";
    }

    private static String encodeHeader(String str) {
        return Native2AsciiUtils.native2Ascii(str);
    }
}
