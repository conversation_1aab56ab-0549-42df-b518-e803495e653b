# coding:utf-8
# -*- coding: utf-8 -*-
import re

import oss2

# 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
import requests
import json

from zhixuewang import login_student

student = login_student('310116200805160630', '56415880wen')
token = student.get_auth_header()['XToken']
# filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
# 将当前日期格式化为"2023/11/24"的格式


import datetime

today = datetime.date.today()
today = today.strftime("%Y/%m/%d/")
# filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
data = {
    "stsTokenQueryList": [
        {
            "appKey": "XXX_ANDROID_ZXZY_STU",
            "chunks": 1,
            "fileName": 'EDU_.html',
            "productKey": ""
        }
    ]
}

headers = {
    'clientType': 'android',
    'epasAppId': 'zhixue_parent',
    'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
    'token': token,
    'Content-Type': 'application/json; charset=utf-8',
    'Content-Length': '163',
    'Host': 'aidp.changyan.com',
    'Connection': 'Keep-Alive',
    'Accept-Encoding': 'gzip',
    'User-Agent': 'okhttp/3.12.12'
}

response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                         data=json.dumps(data))
ossinfo = response.json()

# 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
sts_access_key_id = ossinfo['data'][0]['accessId']
sts_access_key_secret = ossinfo['data'][0]['accessSecret']
# 填写从STS服务获取的安全令牌（SecurityToken）。
security_token = ossinfo['data'][0]['securityToken']
# 使用临时访问凭证中的认证信息初始化StsAuth实例。
auth = oss2.StsAuth(sts_access_key_id,
                    sts_access_key_secret,
                    security_token)
# yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
# 填写Bucket名称。
bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')
# 填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
# 填写LiveChannel名称，例如test-channel。
channel_name = "test-channel"
channel_cfg = oss2.models.LiveChannelInfo(target=oss2.models.LiveChannelInfoTarget())
channel = bucket.create_live_channel(channel_name, channel_cfg)
publish_url = channel.publish_url
# 生成RTMP推流的签名URL，并设置过期时间为3600秒。
signed_publish_url = bucket.sign_rtmp_url(channel_name, "playlist.m3u8", 3600)
# 打印未签名推流地址。
print('publish_url=' + publish_url)
# 打印签名推流地址。
print('signed_publish_url=' + signed_publish_url)
