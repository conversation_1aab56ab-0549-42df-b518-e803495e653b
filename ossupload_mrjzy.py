import base64
import hashlib
import json
import random
import string
import time
import requests
import simplejson as simplejson


def oss_make_file_name(extension, open_id):
    #  var a = "teacher_" + e + "_" + (new Date).valueOf() + "_" + Math.ceil(99999999 * Math.random() + 1e7) + "_" +
    #  ["image", "audio", "video", "file", "file"][t - 1];
    e = open_id.split("_")[-1]
    # print("id", e)
    # 时间戳
    t = int(time.time() * 1000)
    # 随机数 Math.ceil(99999999 * Math.random() + 1e7)
    _r = int(9999999 * random.random() + 1e7)
    _dict = {
        'jpg': 'image',
        'png': 'image',
        'gif': 'image',
        'mp3': 'audio',
        'mp4': 'video',
        'flv': 'video',
    }
    _type = _dict.get(extension)
    if _type is None:
        _type = 'file'
    return f"teacher_{e}_{t}_{_r}_{_type}"


def oss_make_sign(file_name, extension):
    i = {"kind": "1", "keys": f"{file_name}.{extension}"}
    # i = {"kind": "1", "keys": "teacher_u20221017bb6d7454_1666066808716_108179867_file.zip"}
    # i = '{"kind":"1","keys":"teacher_u20221017bb6d7454_1666066808716_108179867_file.zip"}'
    i = simplejson.dumps(i).replace(" ", "")
    # print(i)
    # base64加密i
    n = base64.encodebytes(i.encode("utf-8")).decode("utf-8")
    # print("test", n)
    # 去掉换行符
    new_n = n.replace("\n", "") + "IF75D4U19LKLDAZSMPN5ATQLGBFEJL4VIL2STVDBNJJTO6LNOGB265CR40I4AL13"
    # print("加密之后", new_n)
    # print("md5", hashlib.md5(new_n.encode("utf-8")).hexdigest())
    # md5加密
    return hashlib.md5(new_n.encode("utf-8")).hexdigest()


def get_token(token, sign, file_name, extension, _type, open_id):
    payload = f"kind=1&keys={file_name}.{extension}"
    # print(payload)
    # print("sign", sign)
    # print("token", token)
    headers = {
        "token": token,
        "sign": sign,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.47',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    url = "https://lulu.lulufind.com/mrzy/mrzypc/getQiniuToken"
    response = requests.request("POST", url, headers=headers, data=payload)
    # print("return", response.status_code)
    return response.json()


def sign_in(username, password):
    url = "https://api-prod.lulufind.com/api/v1/auth/smslogin"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.47",
        "Origin": "https://mrzypc.lulufind.com",
        "Referer": "https://mrzypc.lulufind.com/"
    }

    payload = {
        "phone": username,
        "password": password,
    }

    response = requests.request("POST", url, headers=headers, data=payload).json()
    _open_id = response['data']['accounts'][0]["user"]["openId"]
    return response['data']["accounts"][0]['token'], _open_id


def post_file(_token, key, file_path):
    url = "https://upload-z2.qiniup.com/"
    form_data = {"token": _token, "key": key}
    response = requests.post(url, files={"file": open(file_path, 'rb')}, data=form_data)
    return response.json()


token, open_id = sign_in("***********", "12345678aA")

def upload(file_path):
    file_name = file_path.split("\\")[-1]
    extension = file_name.split(".")[-1]
    print(f'正在上传{file_name}')
    # print("token", token)
    # print("openId", open_id)
    file_name = oss_make_file_name(extension, open_id)
    # print("file_name", file_name)
    sign = oss_make_sign(file_name, extension)

    data = get_token(token, sign, file_name, extension, "", open_id)
    # print(data)

    _upload_token = data['data'][file_name + "." + extension]
    # print("upload_token", _upload_token)

    data = post_file(_upload_token, file_name + "." + extension, file_path)
    fileurl = f'https://img2.lulufind.com/{data["key"]}'
    print(fileurl)
    return fileurl

upload("requirements.txt")