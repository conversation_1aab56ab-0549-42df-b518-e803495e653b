from zhixuewang import login_student
student = login_student('310116200805160630', '56415880wen')
# (student.get_auth_header()['XToken'])
homeworks = student.get_homeworks(500,True)
homeworks2 = student.get_homeworks(500)
# print('正在筛选所有的打卡作业...')
# clock_homeworks = []
i = 1
for homework in homeworks2:
    if homework.type.code != 107:
        print(f'{i}.{homework.title} {homework.id}')
        i += 1
for homework in homeworks:
    if homework.type.code == 107:
        print(f'{i}.{homework.title} {homework.id}')
        i += 1
