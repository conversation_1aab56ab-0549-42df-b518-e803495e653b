from typing import Optional

class Message:
    """消息类，用于封装消息相关的属性"""
    
    def __init__(self, from_uid: str, gid: str, mid: Optional[int] = None, content: str = ""):
        """初始化消息对象
        
        Args:
            from_uid: 发送者ID
            gid: 群组ID
            mid: 消息ID，可选
            content: 消息内容，默认为空字符串
        """
        self.from_uid = from_uid
        self.gid = gid
        self.mid = mid
        self.content = content
    
    def __str__(self) -> str:
        """返回消息的字符串表示"""
        return f"Message(from_uid={self.from_uid}, gid={self.gid}, mid={self.mid}, content={self.content})"
    
    def __repr__(self) -> str:
        """返回消息的详细字符串表示"""
        return self.__str__()