import os
import json
import asyncio
import random
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union

from langchain_core.embeddings import Embeddings
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from langgraph.store.memory import InMemoryStore
from langgraph.utils.config import get_store
from langmem import create_manage_memory_tool

from app.api import MessageAPI
from app.database import ConversationDB
from logger import Logger
from .config import Config

# 初始化日志系统
log = Logger()
logger = log.get_logger(filename="roleplay")

class RoleplayService:
    """跑团服务类，负责处理跑团相关功能"""
    
    def __init__(self, message_api: MessageAPI = None, db_path='conversation_history.db'):
        """初始化跑团服务
        
        Args:
            message_api: 消息API实例
            db_path: 数据库文件路径
        """
        self.config = Config()
        self.message_api = message_api or MessageAPI()
        self.db = ConversationDB(db_path)
        self.logger = logger
        
        # 加载用户自定义系统提示词
        self.user_prompts = self._load_user_prompts()
        
        # 初始化跑团代理
        self._init_agents()
        
    def _load_user_prompts(self) -> Dict[str, str]:
        """加载用户自定义系统提示词"""
        prompts_file = "user_rp_prompts.json"
        if os.path.exists(prompts_file):
            try:
                with open(prompts_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载用户提示词失败: {str(e)}")
        return {}
    
    def _save_user_prompts(self):
        """保存用户自定义系统提示词"""
        prompts_file = "user_rp_prompts.json"
        try:
            with open(prompts_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_prompts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存用户提示词失败: {str(e)}")
    
    def _init_agents(self):
        """初始化跑团代理"""
        self.agents = {}
        self.stores = {}
        self.checkpointers = {}
        
        # 从配置中获取API信息
        roleplay_api_key = self.config.get_nested('API.gpt.roleplay_api_key', "")
        roleplay_api_base = self.config.get_nested('API.gpt.api_base', "")
        gpt_api_key = self.config.get_nested('API.gpt.api_key', "")
        gpt_api_base = self.config.get_nested('API.gpt.api_base', "")
        
        # 从配置中获取模型名称
        roleplay_model = self.config.get_nested('MODELS.available.roleplay', "gpt-4.1-2025-04-14")
        embedding_model = self.config.get_nested('MODELS.embedding.model', "text-embedding-3-small")
        embedding_dims = self.config.get_nested('MODELS.embedding.dimensions', 1536)
        
        # 初始化嵌入模型
        self.embedding = OpenAIEmbeddings(
            model=embedding_model,
            api_key=gpt_api_key,
            base_url=gpt_api_base
        )
    
    def _get_agent_for_group(self, gid: str):
        """获取或创建指定群组的代理"""
        if gid not in self.agents:
            # 从配置中获取API信息
            roleplay_api_key = self.config.get_nested('API.gpt.roleplay_api_key', "")
            roleplay_api_base = self.config.get_nested('API.gpt.api_base', "https://api.anthropic.com/v1")
            
            # 从配置中获取模型名称
            roleplay_model = self.config.get_nested('MODELS.available.roleplay', "gpt-4.1-2025-04-14")
            embedding_dims = self.config.get_nested('MODELS.embedding.dimensions', 1536)
            
            # 创建内存存储
            store = InMemoryStore(
                index={
                    "dims": embedding_dims,
                    "embed": self.embedding,
                }
            )
            self.stores[gid] = store
            
            # 创建检查点保存器，添加必要的配置
            checkpointer = MemorySaver()
            self.checkpointers[gid] = checkpointer
            
            # Define prompt_builder function
            def prompt_builder(state):
                """准备LLM的消息"""
                # 获取存储
                store = self.stores[gid]
                # 搜索相关记忆
                memories = store.search(
                    ("memories",),
                    query=state["messages"][-1].content,
                )
                
                # 获取系统提示词
                system_prompt = self.user_prompts.get(gid)
                if not system_prompt:
                    system_prompt = self.config.get_nested(
                        'ROLEPLAY.default_system_prompt', 
                        "你是一个专业的TRPG游戏主持人，负责创建沉浸式的跑团体验。"
                    )
                
                system_msg = f"""{system_prompt}

## 记忆
<memories>
{memories}
</memories>
"""
                # 限制历史消息数量为最近的15条
                messages = state["messages"][-15:] if len(state["messages"]) > 15 else state["messages"]
                
                return [{"role": "system", "content": system_msg}, *messages]
            
            # Create roleplay instance and agent
            roleplay = ChatOpenAI(
                model=roleplay_model,
                api_key=roleplay_api_key,
                base_url=roleplay_api_base
            )

            agent = create_react_agent(
                roleplay,
                prompt=prompt_builder,
                tools=[
                    create_manage_memory_tool(namespace=("memories",)),
                ],
                store=store,
                checkpointer=checkpointer,
            )
            
            self.agents[gid] = agent
        
        return self.agents[gid]

    async def process_message(self, content: str, gid: str, mid: int = None) -> str:
        """处理跑团消息"""
        try:
            # 获取代理
            agent = self._get_agent_for_group(gid)
            
            # 调用代理处理消息，添加必要的配置参数
            response = agent.invoke(
                {"messages": [{"role": "user", "content": content}]},
                config={"configurable": {"thread_id": gid}}
            )
            
            # 提取回复内容
            reply = response["messages"][-1].content
            
            # 发送回复
            if mid is not None:
                await self.message_api.reply_message(mid, reply)
            else:
                await self.message_api.send_message(gid, reply)
            
            return reply
        except Exception as e:
            error_msg = f"处理跑团消息时出错: {str(e)}"
            self.logger.error(error_msg)
            if mid is not None:
                await self.message_api.reply_message(mid, error_msg)
            else:
                await self.message_api.send_message(gid, error_msg)
            return error_msg

    async def reset_memory(self, gid: str) -> str:
        """重置群组的记忆
        
        Args:
            gid: 群组ID
            
        Returns:
            重置结果消息
        """
        try:
            # 删除代理和存储
            if gid in self.agents:
                del self.agents[gid]
            if gid in self.stores:
                del self.stores[gid]
            if gid in self.checkpointers:
                del self.checkpointers[gid]
            
            return "记忆已重置，开始新的冒险！"
        except Exception as e:
            error_msg = f"重置记忆时出错: {str(e)}"
            self.logger.error(error_msg)
            return error_msg

    async def get_system_prompt(self, gid: str) -> str:
        """获取当前系统提示词
        
        Args:
            gid: 群组ID
            
        Returns:
            当前系统提示词
        """
        # 获取用户自定义提示词，如果没有则返回默认提示词
        prompt = self.user_prompts.get(gid)
        if not prompt:
            prompt = self.config.get_nested(
                'ROLEPLAY.default_system_prompt', 
                "你是一个专业的TRPG游戏主持人，负责创建沉浸式的跑团体验。"
            )
        return f"当前系统提示词:\n\n{prompt}"

    async def set_system_prompt(self, gid: str, prompt: str) -> str:
        """设置用户自定义系统提示词
        
        Args:
            gid: 群组ID
            prompt: 系统提示词
            
        Returns:
            设置结果消息
        """
        if not prompt.strip():
            return "系统提示词不能为空"
        
        self.user_prompts[gid] = prompt.strip()
        self._save_user_prompts()
        
        # 如果已经有代理，需要重新初始化
        if gid in self.agents:
            del self.agents[gid]
            if gid in self.stores:
                del self.stores[gid]
            if gid in self.checkpointers:
                del self.checkpointers[gid]
        
        return f"系统提示词已设置为:\n\n{prompt}"
        
    def _parse_dice_expression(self, expression: str) -> Tuple[List[Tuple[int, int]], int, int]:
        """解析骰子表达式
        
        Args:
            expression: 骰子表达式，如 "1d20+5" 或 "2D6*3"
            
        Returns:
            (骰子列表, 修正值, 乘数)
        """
        # 移除所有空格并转换为小写
        expression = expression.replace(" ", "").lower()
        
        # 处理乘法
        multiplier = 1
        if '*' in expression:
            expression, mult = expression.split('*', 1)
            try:
                multiplier = int(mult)
            except ValueError:
                raise ValueError(f"无效的乘数: {mult}")
        
        # 初始化结果
        dice_list = []
        modifier = 0
        
        # 分割表达式
        parts = re.findall(r'([+-]?\d*[dD]\d+|[+-]\d+)', expression)
        
        for part in parts:
            # 处理骰子部分 (如 2d6, 1D20)
            if 'd' in part.lower():
                # 处理带符号的骰子
                sign = 1
                if part.startswith('-'):
                    sign = -1
                    part = part[1:]
                elif part.startswith('+'):
                    part = part[1:]
                
                # 分割数量和面数
                count_str, faces_str = part.lower().split('d')
                count = int(count_str) if count_str else 1
                faces = int(faces_str)
                
                dice_list.append((sign * count, faces))
            # 处理修正值部分
            else:
                modifier += int(part)
        
        return dice_list, modifier, multiplier

    def _roll_dice(self, count: int, faces: int) -> List[int]:
        """掷指定数量和面数的骰子
        
        Args:
            count: 骰子数量
            faces: 骰子面数
            
        Returns:
            骰子结果列表
        """
        # 处理负数骰子数量
        is_negative = count < 0
        abs_count = abs(count)
        
        # 掷骰子
        results = [random.randint(1, faces) for _ in range(abs_count)]
        
        # 如果是负数骰子，返回负数结果
        if is_negative:
            results = [-r for r in results]
            
        return results

    def _format_roll_result(self, dice_results: List[List[int]], modifier: int, multiplier: int, expression: str) -> str:
        """格式化掷骰结果"""
        # 计算总和
        subtotal = sum(sum(dice) for dice in dice_results) + modifier
        total = subtotal * multiplier
        
        # 构建详细结果
        details = []
        individual_results = []
        
        for i, dice in enumerate(dice_results, 1):
            if len(dice) == 1:
                details.append(str(dice[0]))
                individual_results.append(f"骰子{i}: {dice[0]}")
            else:
                details.append(f"{sum(dice)}[{'+'.join(map(str, dice))}]")
                individual_results.append(f"骰子{i}: {sum(dice)}[{'+'.join(map(str, dice))}]")
        
        # 添加修正值和乘数
        if modifier > 0:
            details.append(f"+{modifier}")
        elif modifier < 0:
            details.append(f"{modifier}")
            
        result_str = f"🎲 掷骰结果: {total}"
        if multiplier != 1:
            result_str += f" ({subtotal}×{multiplier})"
        result_str += f" ({' '.join(details)})"
        
        if len(individual_results) > 1:
            result_str += "\n详细结果:\n" + "\n".join(individual_results)
        
        return result_str

    async def roll_advantage(self, expression: str, bonus_count: int = 1) -> str:
        """优势掷骰（掷多次取较高值）
        
        Args:
            expression: 骰子表达式
            bonus_count: 奖励骰数量（默认为1，即总共掷2次）
            
        Returns:
            掷骰结果
        """
        try:
            # 解析表达式
            dice_list, modifier = self._parse_dice_expression(expression)
            
            # 检查是否有有效的骰子
            if not dice_list:
                return "无效的骰子表达式，请使用如 1d20 的格式"
            
            # 限制奖励骰数量
            bonus_count = max(1, min(bonus_count, 10))  # 限制在1-10之间
            
            # 掷多次骰子
            all_results = []
            all_totals = []
            
            for i in range(bonus_count + 1):
                current_results = []
                for count, faces in dice_list:
                    current_results.append(self._roll_dice(count, faces))
                all_results.append(current_results)
                subtotal = sum(sum(dice) for dice in current_results) + modifier
                all_totals.append(subtotal * multiplier)
            
            # 找出最高值的索引
            max_index = all_totals.index(max(all_totals))
            result = self._format_roll_result(all_results[max_index], modifier, multiplier, expression)
            
            # 添加优势信息
            return f"🎲 {bonus_count}个奖励骰: {max(all_totals)} (取最高值: {', '.join(map(str, all_totals))})\n{result}"
        except Exception as e:
            error_msg = f"优势掷骰时出错: {str(e)}"
            self.logger.error(error_msg)
            return error_msg

    async def roll_disadvantage(self, expression: str, penalty_count: int = 1) -> str:
        """劣势掷骰（掷多次取较低值）
        
        Args:
            expression: 骰子表达式
            penalty_count: 惩罚骰数量（默认为1，即总共掷2次）
            
        Returns:
            掷骰结果
        """
        try:
            # 解析表达式
            dice_list, modifier = self._parse_dice_expression(expression)
            
            # 检查是否有有效的骰子
            if not dice_list:
                return "无效的骰子表达式，请使用如 1d20 的格式"
            
            # 限制惩罚骰数量
            penalty_count = max(1, min(penalty_count, 10))  # 限制在1-10之间
            
            # 掷多次骰子
            all_results = []
            all_totals = []
            
            for i in range(penalty_count + 1):
                current_results = []
                for count, faces in dice_list:
                    current_results.append(self._roll_dice(count, faces))
                all_results.append(current_results)
                subtotal = sum(sum(dice) for dice in current_results) + modifier
                all_totals.append(subtotal * multiplier)
            
            # 找出最低值的索引
            min_index = all_totals.index(min(all_totals))
            result = self._format_roll_result(all_results[min_index], modifier, multiplier, expression)
            
            # 添加劣势信息
            return f"🎲 {penalty_count}个惩罚骰: {min(all_totals)} (取最低值: {', '.join(map(str, all_totals))})\n{result}"
        except Exception as e:
            error_msg = f"劣势掷骰时出错: {str(e)}"
            self.logger.error(error_msg)
            return error_msg

    async def roll_dice(self, expression: str, gid: str = None) -> str:
        """普通掷骰
        
        Args:
            expression: 骰子表达式
            gid: 群组ID（可选）
            
        Returns:
            掷骰结果
        """
        try:
            # 解析表达式
            dice_list, modifier, multiplier = self._parse_dice_expression(expression)
            
            # 检查是否有有效的骰子
            if not dice_list:
                return "无效的骰子表达式，请使用如 1d20 或 3D10*3 的格式"
            
            # 掷骰子
            dice_results = []
            for count, faces in dice_list:
                dice_results.append(self._roll_dice(count, faces))
            
            # 格式化结果
            return self._format_roll_result(dice_results, modifier, multiplier, expression)
        except Exception as e:
            error_msg = f"掷骰时出错: {str(e)}"
            self.logger.error(error_msg)
            return error_msg