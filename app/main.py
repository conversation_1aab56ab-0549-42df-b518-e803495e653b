from fastapi import Fast<PERSON><PERSON>
from pydantic import BaseModel
import logging
import asyncio
from datetime import datetime, timedelta
from fastapi_utils.tasks import repeat_every
import os
import re

from app.commands import CommandHandler
from app.database import ConversationDB
from app.api import MessageAPI
from app.models import Message as ModelsMessage

# 初始化FastAPI应用
app = FastAPI()

# 数据库配置
DATABASE = 'conversation_history.db'

# 初始化命令处理器
command_handler = CommandHandler(DATABASE)

# API请求模型定义
class Properties(BaseModel):
    content_type: str | None = None
    height: int | None = None
    width: int | None = None
    name: str | None = None
    size: int | None = None
    mentions: list | None = None

class ReactionDetail(BaseModel):
    content: str | None = None
    content_type: str | None = None
    properties: Properties | None = None
    type: str  # edit, delete, reply
    mid: int | None = None

class Detail(BaseModel):
    content: str
    content_type: str | None = None  # text/plain, text/markdown, vocechat/file
    expires_in: int | None = None
    properties: Properties | None = None
    type: str = "normal"  # normal, reaction
    detail: ReactionDetail | None = None
    mid: int | None = None  # 用于回复消息

class Target(BaseModel):
    gid: int | None = None
    uid: int | None = None

class Message(BaseModel):
    created_at: int
    from_uid: int
    mid: int
    detail: Detail
    target: Target

# API路由
@app.get("/voce/bot")
async def demo_get():
    """健康检查接口"""
    return {"msg": "success"}

@app.post("/voce/bot")
async def demo_post(message: Message):
    """处理接收到的消息"""
    from_uid = message.from_uid
    detail = message.detail
    content = detail.content
    gid = message.target.gid
    mid = message.mid

    # 将接收到的消息转换为models.py中定义的Message类
    models_message = ModelsMessage(from_uid=str(from_uid), gid=str(gid), mid=mid, content=content)

    # 如果命令处理器正在等待webhook数据，则发送数据
    if hasattr(command_handler, 'next_webhook_future') and not command_handler.next_webhook_future.done():
        command_handler.next_webhook_future.set_result(message.json())

    # 调用命令处理器处理消息
    return await command_handler.process_command(models_message)

# 后台任务：检查连续对话模式超时
@app.on_event("startup")
@repeat_every(seconds=60)  # 每分钟检查一次
async def check_continuous_mode():
    """检查连续对话模式超时，超过60分钟无活动则自动关闭"""
    db = ConversationDB(DATABASE)
    message_api = MessageAPI()
    active_conversations = db.get_all_active_conversations()
    now = datetime.now()
    
    for gid, last_active in active_conversations:
        if now - last_active > timedelta(minutes=60):
            # 禁用连续对话模式并清除消息
            db.disable_continuous_mode(gid)
            await message_api.send_message(gid, '连续对话已自动关闭（60分钟无活动）')