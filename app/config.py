import yaml
import os

class Config:
    """配置管理类，负责加载和提供应用配置"""
    
    def __init__(self, config_path='config.yaml'):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception:
            # 返回默认配置
            return {'LOGGING': {'enabled': False, 'level': 'INFO'}}
    
    def get(self, section, key=None, default=None):
        """获取配置项
        
        Args:
            section: 配置节
            key: 配置键
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        if section not in self.config:
            return default
            
        if key is None:
            return self.config[section]
        
        # 正常情况下，直接返回配置值
        return self.config[section].get(key, default)
    
    def get_nested(self, path, default=None):
        """获取嵌套配置项
        
        Args:
            path: 配置路径，例如 'API.vocechat.base_url'
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        keys = path.split('.')
        value = self.config
        
        for key in keys:
            if not isinstance(value, dict) or key not in value:
                return default
            value = value[key]
        
        return value
    
    def reload(self):
        """重新加载配置"""
        self.config = self._load_config()
        return self.config