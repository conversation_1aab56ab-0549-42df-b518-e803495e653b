import sqlite3
import json
from datetime import datetime
import logging
from logger import Logger

# 初始化日志系统
log = Logger()
logger = log.get_logger(filename="database")

class ConversationDB:
    """对话历史数据库管理类"""
    
    def __init__(self, database_path='conversation_history.db'):
        """初始化数据库管理器
        
        Args:
            database_path: 数据库文件路径
        """
        self.database_path = database_path
        self.init_db()
    
    def init_db(self):
        """初始化数据库，创建必要的表结构"""
        conn = sqlite3.connect(self.database_path)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS conversations (
                    gid TEXT PRIMARY KEY,
                    messages TEXT,
                    continuous_mode INTEGER DEFAULT 0,
                    last_active TIMESTAMP
                )''')
        
        # 创建智学网账号映射表
        c.execute('''CREATE TABLE IF NOT EXISTS zhixue_accounts (
                    uid TEXT PRIMARY KEY,
                    account TEXT NOT NULL,
                    password TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )''')
        conn.commit()
        conn.close()
    
    def get_conversation(self, gid):
        """获取指定群组的对话历史记录
        
        Args:
            gid: 群组ID
            
        Returns:
            (messages, continuous_mode, last_active) 元组
        """
        conn = sqlite3.connect(self.database_path)
        c = conn.cursor()
        c.execute('SELECT messages, continuous_mode, last_active FROM conversations WHERE gid=?', (gid,))
        row = c.fetchone()
        conn.close()
        
        if row:
            messages, continuous_mode, last_active = row
            messages = json.loads(messages) if messages else []
            last_active = datetime.strptime(last_active, '%Y-%m-%d %H:%M:%S') if last_active else None
            return messages, continuous_mode, last_active
        else:
            return [], 0, None
    
    def update_conversation(self, gid, messages, continuous_mode, last_active):
        """更新指定群组的对话历史记录
        
        Args:
            gid: 群组ID
            messages: 消息列表
            continuous_mode: 连续对话模式标志
            last_active: 最后活动时间
        """
        conn = sqlite3.connect(self.database_path)
        c = conn.cursor()
        messages_json = json.dumps(messages)
        last_active_str = last_active.strftime('%Y-%m-%d %H:%M:%S')
        c.execute('REPLACE INTO conversations (gid, messages, continuous_mode, last_active) VALUES (?, ?, ?, ?)',
                (gid, messages_json, continuous_mode, last_active_str))
        conn.commit()
        conn.close()
    
    def get_all_active_conversations(self):
        """获取所有启用连续对话模式的会话
        
        Returns:
            包含(gid, last_active)元组的列表
        """
        conn = sqlite3.connect(self.database_path)
        c = conn.cursor()
        c.execute('SELECT gid, last_active FROM conversations WHERE continuous_mode=1')
        rows = c.fetchall()
        conn.close()
        
        result = []
        for row in rows:
            gid, last_active_str = row
            last_active = datetime.strptime(last_active_str, '%Y-%m-%d %H:%M:%S') if last_active_str else None
            result.append((gid, last_active))
        
        return result
    
    def disable_continuous_mode(self, gid):
        """禁用指定群组的连续对话模式
        
        Args:
            gid: 群组ID
        """
        conn = sqlite3.connect(self.database_path)
        c = conn.cursor()
        c.execute('UPDATE conversations SET continuous_mode=0, messages="" WHERE gid=?', (gid,))
        conn.commit()
        conn.close()
    
    def bind_zhixue_account(self, uid: str, account: str, password: str) -> bool:
        """绑定智学网账号
        
        Args:
            uid: 用户ID
            account: 智学网账号
            password: 智学网密码
            
        Returns:
            bool: 是否绑定成功
        """
        try:
            conn = sqlite3.connect(self.database_path)
            c = conn.cursor()
            c.execute('REPLACE INTO zhixue_accounts (uid, account, password) VALUES (?, ?, ?)',
                    (uid, account, password))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"绑定智学网账号失败: {str(e)}")
            return False
    
    def get_zhixue_account(self, uid: str) -> tuple:
        """获取智学网账号信息
        
        Args:
            uid: 用户ID
            
        Returns:
            tuple: (account, password) 或 (None, None)
        """
        try:
            conn = sqlite3.connect(self.database_path)
            c = conn.cursor()
            c.execute('SELECT account, password FROM zhixue_accounts WHERE uid=?', (uid,))
            row = c.fetchone()
            conn.close()
            return row if row else (None, None)
        except Exception as e:
            logger.error(f"获取智学网账号失败: {str(e)}")
            return None, None