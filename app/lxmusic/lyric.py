import json

from app.lxmusic.search import NetEaseEncryption
import requests

class NetEaseLyricFetcher:
    """网易云歌词获取类"""

    def __init__(self):
        self.encryption = NetEaseEncryption()  # 使用之前的加密类
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36',
            'Origin': 'https://music.163.com'
        })

    def get_lyric(self, song_id: str) -> dict:
        """
        获取歌词，基于项目中的实现
        """
        # 构建请求参数
        lyric_data = {
            'id': song_id,
            'cp': False,
            'tv': 0,
            'lv': 0,
            'rv': 0,
            'kv': 0,
            'yv': 0,
            'ytv': 0,
            'yrv': 0
        }

        # 使用eapi加密
        encrypted_data = self.encryption.eapi_encrypt('/api/song/lyric/v1', lyric_data)

        try:
            response = self.session.post(
                'https://interface3.music.163.com/eapi/song/lyric/v1',
                data=encrypted_data
            )

            result = response.json()

            if result.get('code') != 200 or not result.get('lrc', {}).get('lyric'):
                raise Exception('获取歌词失败')

                # 解析歌词数据
            return self._parse_lyric_data(result)

        except Exception as e:
            print(f"获取歌词出错: {e}")
            return {'lyric': '', 'tlyric': '', 'rlyric': '', 'lxlyric': ''}

    def _parse_lyric_data(self, raw_data: dict) -> dict:
        """解析歌词数据"""
        # 修复时间标签格式
        lrc = raw_data.get('lrc', {}).get('lyric', '')
        tlrc = raw_data.get('tlyric', {}).get('lyric', '')
        romalrc = raw_data.get('romalrc', {}).get('lyric', '')

        # 处理逐字歌词
        yrc = raw_data.get('yrc', {}).get('lyric', '')
        ytlrc = raw_data.get('ytlrc', {}).get('lyric', '')
        yromalrc = raw_data.get('yromalrc', {}).get('lyric', '')

        return {
            'lyric': self._parse_word_lyric(lrc),
            'tlyric': self._parse_word_lyric(tlrc),
            'rlyric': self._parse_word_lyric(romalrc),
            'lxlyric': self._parse_word_lyric(yrc) if yrc else ''
        }

    def _parse_word_lyric(self, yrc_content: str) -> str:
        """解析逐字歌词"""
        if not yrc_content:
            return ''

        lines = yrc_content.strip().split('\n')
        lxlyric_lines = []

        for line in lines:
            line = line.strip()
            if line.startswith('{"'):
                try:
                    info = json.loads(line)
                    time_ms = info.get('t', 0)
                    words = info.get('c', [])

                    # 格式化时间标签
                    minutes = time_ms // 60000
                    seconds = (time_ms % 60000) // 1000
                    ms = time_ms % 1000
                    time_tag = f"[{minutes:02d}:{seconds:02d}.{ms:03d}]"

                    # 构建逐字歌词
                    word_content = ""
                    for word in words:
                        word_content += f"<{word.get('tx', '')}>"

                    lxlyric_lines.append(f"{time_tag}{word_content}")
                except:
                    continue
            else:
                if len(line) > 11:
                    lxlyric_lines.append(line)

        return '\n'.join(lxlyric_lines)

    # 使用示例


if __name__ == "__main__":
    lyric_fetcher = NetEaseLyricFetcher()

    # 获取歌词 (示例歌曲ID)
    song_id = "536622304"
    lyric_info = lyric_fetcher.get_lyric(song_id)

    print("标准歌词:")
    print(lyric_info['lyric'])

    if lyric_info['tlyric']:
        print("\n翻译歌词:")
        print(lyric_info['tlyric'])

    if lyric_info['rlyric']:
        print("\n罗马音歌词:")
        print(lyric_info['rlyric'])
