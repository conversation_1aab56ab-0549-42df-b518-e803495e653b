import requests
import base64
import json

API_URL = "https://88.lxmusic.xn--fiqs8s"
API_KEY = "lxmusic"
HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "lx-music-request/3.0",
    "X-Request-Key": API_KEY,
}

def base64_urlsafe(data):
    encoded = base64.b64encode(data.encode("utf-8")).decode("utf-8")
    return encoded.replace("+", "-").replace("/", "_")

def test_get_music_url(source, song_id, quality):
    url = f"{API_URL}/lxmusicv3/url/{source}/{song_id}/{quality}"
    print(f"请求：{url}")
    resp = requests.get(url, headers=HEADERS)
    print("返回：", resp.json())

def test_get_local_pic(song_id):
    # local 封面只支持本地文件
    req_body = {"p": song_id.replace("server_", "")}
    b64 = base64_urlsafe(json.dumps(req_body))
    url = f"{API_URL}/local/p?q={b64}"
    print(f"请求：{url}")
    resp = requests.get(url, headers=HEADERS)
    print("返回：", resp.json())

def test_get_local_lyric(song_id):
    req_body = {"p": song_id.replace("server_", "")}
    b64 = base64_urlsafe(json.dumps(req_body))
    url = f"{API_URL}/local/l?q={b64}"
    print(f"请求：{url}")
    resp = requests.get(url, headers=HEADERS)
    print("返回：", resp.json())

def test_check_update(script_md5):
    url = f"{API_URL}/script?key={API_KEY}&checkUpdate={script_md5}"
    print(f"请求：{url}")
    resp = requests.get(url, headers=HEADERS)
    print("返回：", resp.json())

if __name__ == "__main__":
    # 示例参数，根据实际情况替换
    # 网易云音乐id/哈希等
    sources = ["kw", "kg", "tx", "wy", "mg"]
    test_songmid = "2709812973"  # 真实情况需填真实的hash或songmid
    qualitys = ["128k", "320k", "flac", "flac24bit"]

    print("\n--- 测试获取音乐播放地址 ---")
    test_get_music_url("wy", test_songmid, "128k")



