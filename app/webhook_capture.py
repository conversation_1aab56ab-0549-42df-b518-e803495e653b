import asyncio
from typing import Optional, Dict

class WebhookCapture:
    """Webhook数据捕获类"""
    
    def __init__(self):
        """初始化webhook捕获器"""
        self._next_webhook: Optional[Dict] = None
        self._capture_lock = asyncio.Lock()
        self._capture_event = asyncio.Event()
    
    async def capture_next(self) -> Dict:
        """等待并捕获下一个webhook数据
        
        Returns:
            Dict: webhook的完整JSON数据
        """
        async with self._capture_lock:
            self._capture_event.clear()
            self._next_webhook = None
            await self._capture_event.wait()
            return self._next_webhook
    
    async def store_webhook(self, webhook_data: Dict):
        """存储webhook数据
        
        Args:
            webhook_data: webhook的完整JSON数据
        """
        async with self._capture_lock:
            if not self._capture_event.is_set():
                self._next_webhook = webhook_data
                self._capture_event.set()

# 创建全局实例
webhook_capture = WebhookCapture()