import asyncio
from sys import stderr

from dataharvest.base import DataHarvest
import requests as rq

def query(keyword):
    """
    搜索关键字。
    """

    # 获取搜索结果
    try:
        __header = {
            "Host": "baike.baidu.com",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:75.0) Gecko/20100101 Firefox/75.0",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate",
        }
        # Initial request to the API endpoint
        initial_response = rq.get(
            f"https://baike.baidu.com/lemma/api/entry?word={keyword}&fromModule=lemma_search-box",
            headers=__header,
            timeout=5,
            allow_redirects=False  # Prevent automatic redirection
        )

        # Check for the first 302 redirect
        if initial_response.status_code == 302:
            first_redirect_url = initial_response.headers.get('Location', '')
            if first_redirect_url:
                # Second request to the first redirect URL
                second_response = rq.get(
                    "https:" + first_redirect_url,
                    headers=__header,
                    timeout=5,
                    allow_redirects=False
                )

                # Check for the second 302 redirect
                if second_response.status_code == 302:
                    second_redirect_url = second_response.headers.get('Location', '')
                    if second_redirect_url:
                        # Construct the final URL
                        url = "https://baike.baidu.com" + second_redirect_url
                        print(f"最终URL: {url}")
                        return url
                    else:
                        stderr.write("第二次重定向URL未找到\n")
                        return ""
            else:
                stderr.write("第一次重定向URL未找到\n")
                return ""
        else:
            stderr.write("未收到预期的302状态码\n")
            return ""

    except rq.exceptions.Timeout:
        stderr.write(
            "超时错误:"
            + "https://baike.baidu.com/lemma/api/entry?word="
            + keyword
            + ";"
            + "HTTP状态码:"
            + str(initial_response.status_code)
            + "\n"
        )
        return ""
    except Exception as e:
        stderr.write(f"请求过程中发生错误: {str(e)}\n")
        return ""


url = query("旧日支配者")
if not url:
    url = f"https://baike.baidu.com/item/旧日支配者"
dh = DataHarvest()
tasks = [dh.a_crawl_and_purify(url)]
loop = asyncio.get_event_loop()
docs = loop.run_until_complete(asyncio.gather(*tasks))[0]
md = str(docs.page_content).replace("## 基本信息","<details><summary>展开详情</summary>\n## 基本信息") + "</details>"
print(md)