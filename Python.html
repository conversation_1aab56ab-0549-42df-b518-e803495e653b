<!DOCTYPE html><html class=""><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="referrer" content="always"><title>Python（计算机编程语言）_百度百科</title><meta name="description" content="Python由荷兰国家数学与计算机科学研究中心的吉多·范罗苏姆于1990年代初设计，作为一门叫做ABC语言的替代品。Python提供了高效的高级数据结构，还能简单有效地面向对象编程。Python语法和动态类型，以及解释型语言的本质，使它成为多数平台上写脚本和快速开发应用的编程语言，随着版本的不断更新和语言新功能的添加，逐渐被用于独立的、大型项目的开发。Python在各个编程语言中比较适合新手学习，Python解释器易于扩展，可以使用C、C++或其他可以通过C调用的语言扩展新的功能和数据类型。Python也可用于可定制化软件中的扩展程序语言。Python丰富的标准库，提供了适用于各个主要系统平台的源码或机器码。"><meta name="keywords" content="Python, Python发展历程, Python语言特点, Python基本语法, Python帮助, Python接口, Python应用领域, Python开发工具, Python标准库, Python开发环境, Python著名应用, Python学习网站"><meta itemprop="dateUpdate" content="2024-09-08 10:56:26"><link rel="alternate" hreflang="x-default" href="https://baike.baidu.com/item/Python/407313"><link rel="alternate" hreflang="zh" href="https://baike.baidu.com/item/Python/407313"><link rel="alternate" hreflang="zh-Hans" href="https://baike.baidu.com/item/Python/407313"><link rel="alternate" hreflang="zh-Hant" href="https://baike.baidu.hk/item/Python/407313"><link rel="canonical" href="https://baike.baidu.com/item/Python/407313"><meta name="image" content="https://bkimg.cdn.bcebos.com/pic/b03533fa828ba61ea8d3c8f6227f800a304e241ff39d?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70"><meta property="og:title" content="Python"><meta property="og:description" content="Python由荷兰国家数学与计算机科学研究中心的吉多·范罗苏姆于1990年代初设计，作为一门叫做ABC语言的替代品。Python提供了高效的高级数据结构，还能简单有效地面向对象编程。Python语法和动态类型，以及解释型语言的本质，使它成为多数平台上写脚本和快速开发应用的编程语言，随着版本的不断更新和语言新功能的添加，逐渐被用于独立的、大型项目的开发。Python在各个编程语言中比较适合新手学习，Python解释器易于扩展，可以使用C、C++或其他可以通过C调用的语言扩展新的功能和数据类型。Python也可用于可定制化软件中的扩展程序语言。Python丰富的标准库，提供了适用于各个主要系统平台的源码或机器码。"><meta property="og:image" content="https://bkimg.cdn.bcebos.com/pic/b03533fa828ba61ea8d3c8f6227f800a304e241ff39d?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70"><meta property="og:url" content="https://baike.baidu.hk/item/Python/407313"><meta property="og:site_name" content="百度百科"><meta property="og:type" content="website"><link rel="shortcut icon" href="//baikebcs.bdimg.com/baike-react/common/favicon-baidu.ico" type="image/x-icon"><link rel="icon" sizes="any" mask="true" href="//baikebcs.bdimg.com/cms/static/baike-icon.svg"> <style id=f3d7f4f0-40fc-5c4b-8ffa-fed75b8098db type="text/css">.rc-dialog{margin:10px;position:relative;width:auto}.rc-dialog-wrap{-webkit-overflow-scrolling:touch;bottom:0;left:0;outline:0;overflow:auto;position:fixed;right:0;top:0;z-index:1050}.rc-dialog-title{font-size:14px;font-weight:700;line-height:21px;margin:0}.rc-dialog-content{background-clip:padding-box;background-color:#fff;border:none;border-radius:6px 6px;position:relative}.rc-dialog-close{background:transparent;border:0;color:#000;cursor:pointer;filter:alpha(opacity=20);font-size:21px;font-weight:700;line-height:1;opacity:.2;position:absolute;right:20px;-webkit-text-decoration:none;text-decoration:none;text-shadow:0 1px 0 #fff;top:12px}.rc-dialog-close-x:after{content:"×"}.rc-dialog-close:hover{filter:alpha(opacity=100);opacity:1;-webkit-text-decoration:none;text-decoration:none}.rc-dialog-header{background:#fff;border-bottom:1px solid #e9e9e9;border-radius:5px 5px 0 0;color:#666;padding:13px 20px 14px}.rc-dialog-body{padding:20px}.rc-dialog-footer{border-radius:0 0 5px 5px;border-top:1px solid #e9e9e9;padding:10px 20px;text-align:right}.rc-dialog-zoom-appear,.rc-dialog-zoom-enter{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:cubic-bezier(.08,.82,.17,1);opacity:0}.rc-dialog-zoom-leave{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:cubic-bezier(.6,.04,.98,.34)}.rc-dialog-zoom-appear.rc-dialog-zoom-appear-active,.rc-dialog-zoom-enter.rc-dialog-zoom-enter-active{animation-name:rcDialogZoomIn;animation-play-state:running}.rc-dialog-zoom-leave.rc-dialog-zoom-leave-active{animation-name:rcDialogZoomOut;animation-play-state:running}@keyframes rcDialogZoomIn{0%{opacity:0;transform:scale(0)}to{opacity:1;transform:scale(1)}}@keyframes rcDialogZoomOut{0%{transform:scale(1)}to{opacity:0;transform:scale(0)}}@media (min-width:768px){.rc-dialog{margin:30px auto;width:600px}}.rc-dialog-mask{background-color:#373737;background-color:rgba(55,55,55,.6);bottom:0;filter:alpha(opacity=50);height:100%;left:0;position:fixed;right:0;top:0;z-index:1050}.rc-dialog-mask-hidden{display:none}.rc-dialog-fade-appear,.rc-dialog-fade-enter{opacity:0}.rc-dialog-fade-appear,.rc-dialog-fade-enter,.rc-dialog-fade-leave{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:cubic-bezier(.55,0,.55,.2)}.rc-dialog-fade-appear.rc-dialog-fade-appear-active,.rc-dialog-fade-enter.rc-dialog-fade-enter-active{animation-name:rcDialogFadeIn;animation-play-state:running}.rc-dialog-fade-leave.rc-dialog-fade-leave-active{animation-name:rcDialogFadeOut;animation-play-state:running}@keyframes rcDialogFadeIn{0%{opacity:0}to{opacity:1}}@keyframes rcDialogFadeOut{0%{opacity:1}to{opacity:0}}.bk-dialog{left:50%;margin:0!important;position:absolute!important;top:50%;transform:translate(-50%,-50%)}.bk-dialog .rc-dialog-content{background:#fff;border:1px solid #bbb;border-radius:0}.bk-dialog .rc-dialog-header{background:#fafbfc;border-bottom:1px solid #e5e5e5;padding:10px 20px 9px}.bk-dialog .rc-dialog-title{color:#666;font-size:16px;font-weight:400}.bk-dialog .rc-dialog-close{color:#cbcbcb;font-size:18px;opacity:1}.bk-dialog .rc-dialog-body{padding:26px 50px 35px;text-align:center}.bk-dialog .dialog-title{color:#333;font-size:20px;font-weight:700;line-height:26px;margin-bottom:20px}.bk-dialog .dialog-content{color:#ccc;font-size:16px;line-height:26px}.bk-dialog .dialog-content .alert-content{align-items:center;display:flex;justify-content:center;padding-top:44px;width:378px}.bk-dialog .dialog-content .alert-content.no-icon .msg{max-width:378px}.bk-dialog .dialog-content .alert-content.no-sub-msg .msg h1{line-height:58px}.bk-dialog .dialog-content .alert-content .msg{flex:none;max-width:302px;text-align:center}.bk-dialog .dialog-content .alert-content .msg h1{color:#333;font-size:20px;line-height:32px;text-align:left}.bk-dialog .dialog-content .alert-content .msg pre{color:#888;font-family:arial,tahoma,Microsoft Yahei,"\5b8b\4f53",sans-serif,font-extend;font-size:14px;line-height:22px;text-align:left}.bk-dialog .dialog-content .alert-content .msg .ext-info{color:#888;font-size:12px;margin-top:14px;text-align:left}.bk-dialog .dialog-content .alert-content .msg .ext-info i{color:#52a3f5}.bk-dialog .dialog-content .alert-content .alert-icon{align-items:center;border:2px solid #b4d6f9;border-radius:56px;color:#52a3f5;display:flex;flex:none;height:56px;justify-content:center;margin-right:12px;width:56px}.bk-dialog .dialog-content .alert-content .alert-icon.error-icon{border-color:#fbddd6;color:#f18167}.bk-dialog .dialog-content .alert-content .alert-icon .icon{font-size:30px;line-height:0}.bk-dialog .btn-list{margin-top:35px}.bk-dialog .btn-list .btn-item{background:#52a3f5;border:1px solid #52a3f5;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.05);color:#fff;cursor:pointer;display:inline-block;font-size:16px;line-height:1;margin-right:10px;padding:8px 24px;text-align:center}.bk-dialog .btn-list .btn-item:last-child{margin-right:0}</style><style id=99d2ba84-28cc-5c59-83bf-4fc064d5b617 type="text/css">.popup-content{background:#fff;border:1px solid #d7d7d7;margin:auto;padding:5px;width:50%}[role=tooltip].popup-content{border-radius:5px;box-shadow:0 0 3px rgba(0,0,0,.16);width:200px}.popup-overlay{background:rgba(0,0,0,.5)}[data-popup=tooltip].popup-overlay{background:transparent}.popup-arrow{stroke-width:2px;stroke:#d7d7d7;stroke-dasharray:30px;stroke-dashoffset:-54px;bottom:0;color:#fff;filter:drop-shadow(0 -3px 3px rgba(0,0,0,.16));left:0;right:0;top:0}</style><style id=116fa488-8322-5b02-a80c-36f1131ed2ce type="text/css">.bubble-box{background:#fff;border:1px solid #dfdfdf;border-radius:3px;box-shadow:0 4px 6px -4px rgba(0,0,0,.2);z-index:2000}.bubble-box,.bubble-box .bubble-arrow-place{position:absolute}.bubble-box .bubble-arrow{background:#fff;border:1px solid #dfdfdf;box-shadow:0 4px 6px -4px rgba(0,0,0,.2);height:10px;overflow:hidden;position:absolute;transform:rotate(45deg);width:10px;z-index:1}.bubble-box.top-left{bottom:100%;left:0;margin-bottom:6px}.bubble-box.top-left .bubble-arrow-place{bottom:-8px;height:8px;left:0;width:100%}.bubble-box.top-left .bubble-arrow{left:3px;margin-top:-6px;top:100%}.bubble-box.top-right{bottom:100%;margin-bottom:6px;right:0}.bubble-box.top-right .bubble-arrow-place{bottom:-8px;height:8px;left:0;width:100%}.bubble-box.top-right .bubble-arrow{margin-top:-6px;right:3px;top:100%}.bubble-box.top-center{bottom:100%;left:50%;margin-bottom:6px;transform:translateX(-50%)}.bubble-box.top-center .bubble-arrow-place{bottom:-8px;height:8px;left:0;width:100%}.bubble-box.top-center .bubble-arrow{left:50%;margin-left:-6px;margin-top:-6px;top:100%}.bubble-box.bottom-left{left:0;margin-top:6px;top:100%}.bubble-box.bottom-left .bubble-arrow-place{height:8px;left:0;top:-8px;width:100%}.bubble-box.bottom-left .bubble-arrow{bottom:100%;left:3px;margin-bottom:-6px}.bubble-box.bottom-right{margin-top:6px;right:0;top:100%}.bubble-box.bottom-right .bubble-arrow-place{height:8px;left:0;top:-8px;width:100%}.bubble-box.bottom-right .bubble-arrow{bottom:100%;margin-bottom:-6px;right:3px}.bubble-box.bottom-center{left:50%;margin-top:6px;top:100%;transform:translateX(-50%)}.bubble-box.bottom-center .bubble-arrow-place{height:8px;left:0;top:-8px;width:100%}.bubble-box.bottom-center .bubble-arrow{bottom:100%;left:50%;margin-bottom:-6px;margin-left:-6px}.bubble-box.left-top{margin-right:8px;right:100%;top:0}.bubble-box.left-top .bubble-arrow-place{height:100%;right:-8px;top:0;width:8px}.bubble-box.left-top .bubble-arrow{left:100%;margin-left:-6px;top:3px}.bubble-box.left-bottom{bottom:0;margin-right:8px;right:100%}.bubble-box.left-bottom .bubble-arrow-place{height:100%;right:-8px;top:0;width:8px}.bubble-box.left-bottom .bubble-arrow{bottom:3px;left:100%;margin-left:-6px}.bubble-box.left-center{margin-right:8px;right:100%;top:50%;transform:translateY(-50%)}.bubble-box.left-center .bubble-arrow-place{height:100%;right:-8px;top:0;width:8px}.bubble-box.left-center .bubble-arrow{left:100%;margin-left:-6px;margin-top:-6px;top:50%}.bubble-box.right-top{left:100%;margin-left:8px;top:0}.bubble-box.right-top .bubble-arrow-place{height:100%;left:-8px;top:0;width:8px}.bubble-box.right-top .bubble-arrow{margin-right:-6px;right:100%;top:3px}.bubble-box.right-bottom{bottom:0;left:100%;margin-left:8px}.bubble-box.right-bottom .bubble-arrow-place{height:100%;left:-8px;top:0;width:8px}.bubble-box.right-bottom .bubble-arrow{bottom:3px;margin-right:-6px;right:100%}.bubble-box.right-center{left:100%;margin-left:8px;top:50%;transform:translateY(-50%)}.bubble-box.right-center .bubble-arrow-place{height:100%;left:-8px;top:0;width:8px}.bubble-box.right-center .bubble-arrow{margin-right:-6px;margin-top:-6px;right:100%;top:50%}.bubble-box .bubble-content{background:#fff;border-radius:3px;overflow:hidden;position:relative;z-index:2}</style><style id=489a1e7a-34e5-5c78-bd07-8a6d4f7b173a type="text/css">.user-bar{color:#333;display:flex;font-size:13px}.user-bar.dark-theme,.user-bar.dark-theme .user-bar-item .menu-item a,.user-bar.dark-theme .user-bar-item>a{color:#fff}.user-bar .user-bar-item{flex:none;padding:0 11px;position:relative}.user-bar .user-bar-item>a{color:#333;cursor:pointer;-webkit-text-decoration:none;text-decoration:none}.user-bar .user-bar-item>a:hover{-webkit-text-decoration:underline;text-decoration:underline}.user-bar .user-bar-item>a svg{fill:#c1c1c1;font-size:14px;margin:0 5px 0 3px;vertical-align:-2px}.user-bar .user-bar-item.message{margin-left:11px;margin-right:11px}.user-bar .user-bar-item .menu-item{font-size:12px;white-space:nowrap}.user-bar .user-bar-item .menu-item a{color:#333;cursor:pointer;display:block;height:24px;line-height:24px;padding:0 11px}.user-bar .user-bar-item .menu-item a:hover{background:#38f;color:#fff}.user-bar .user-bar-item .menu-item a:hover .msg-num{color:#fff}.user-bar .user-bar-item .menu-item .msg-num{color:#338de6;font-weight:700;margin-left:3px}.user-bar .user-bar-item .lemma-msg-bubble{background:#ffffda;border-radius:3px;font-size:12px;padding:10px 5px;position:relative;width:260px}.user-bar .user-bar-item .lemma-msg-bubble .lemma-msg-item a{color:#2b66b4;height:24px;line-height:24px;outline:0;padding:0 10px;-webkit-text-decoration:none;text-decoration:none}.user-bar .user-bar-item .lemma-msg-bubble .cancel-alarm{margin-top:10px}.user-bar .user-bar-item .lemma-msg-bubble .close-icon{color:#b0b0b0;cursor:pointer;font-size:12px;position:absolute;right:6px;top:6px}.user-bar.user-login .user-bar-item:last-child{margin-right:9px}.user-bar .triangle-down{border:4px solid transparent;border-top-color:#8b8c8c;position:absolute;right:0;top:7px}</style><style id=87bfe19a-b3d5-598e-8249-5fc660391e25 type="text/css">.clearfix{*zoom:1}.clearfix:after,.clearfix:before{content:" ";display:table}.clearfix:after{clear:both}.ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.z-index-content{z-index:1}.z-index-fixed-content{z-index:100}.z-index-modal-mask{z-index:1000}.z-index-modal-content{z-index:1100}.z-index-popup{z-index:2000}.z-index-toast{z-index:3000}.gray{filter:grayscale(100%);-moz-filter:grayscale(100%);-ms-filter:grayscale(100%);-o-filter:grayscale(100%);filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);-webkit-filter:grayscale(1)}.lemmaSearchBarWrapper{margin:0 auto;position:relative;width:1139px}.lemmaSearchBar{box-sizing:border-box;height:104px;margin:auto;padding-top:25px;width:808px}@media screen and (min-width:1441px){.lemmaSearchBar{width:903px}}.fixedWrapper{background-color:#fff;border-bottom:1px solid #ebebeb;box-shadow:0 0 5px #888;height:66px;left:0;margin:auto;min-width:1140px;opacity:0;position:fixed;top:0;transition:opacity .3s;width:100%;z-index:100}.fixedWrapper,.fixedWrapper *{box-sizing:border-box}.fixedWrapper .fixedBg{height:66px;margin:auto;padding-top:12px;width:1140px}.fixedWrapper.fadeIn{opacity:1}.fixedWrapper.fadeOut{opacity:0}.fixedWrapper.show{z-index:100}.fixedWrapper.hide{z-index:-10}.searchBar{height:47px;margin:auto}.searchBar,.searchBar *{box-sizing:border-box}.searchBar .logoWrapper{display:inline-block;float:left;height:100%;width:154px}.searchBar .logoWrapper .logo{cursor:pointer;height:44px}.searchBar .rightWrapper{display:inline-block;float:left;height:100%;padding-top:7px;position:relative}.searchBar .rightWrapper .inputWrapper{display:inline-block;float:left;height:40px;position:relative}.searchBar .rightWrapper .inputWrapper .placeholder{color:#999;font-size:14px;font-weight:400;left:0;opacity:0;overflow:hidden;padding-left:10px;pointer-events:none;position:absolute;text-overflow:ellipsis;top:50%;transform:translateY(-50%);transition:.5s ease-out;transition-property:opacity;white-space:nowrap;width:calc(100% - 10px);z-index:1000}.searchBar .rightWrapper .inputWrapper .placeholder.showIn{opacity:1;transition:.5s ease-in}.searchBar .rightWrapper .inputWrapper .searchInput{border:1px solid #ccc;border-right:0;display:inline-block;font-size:16px;height:40px;line-height:28px;padding:6px 36px 4px 8px;-webkit-user-select:text;width:440px}.searchBar .rightWrapper .inputWrapper .searchInput::-webkit-input-placeholder{animation:zoomIn 1s ease-in-out;color:#999;font-size:14px;font-weight:400;opacity:var(--placeholder-color);overflow:hidden;text-overflow:ellipsis;-webkit-transition-property:opacity;transition-property:opacity;-webkit-transition:2s ease-out;transition:2s ease-out;white-space:nowrap}.searchBar .rightWrapper .inputWrapper .searchInput:hover{border-color:#999}.searchBar .rightWrapper .inputWrapper .searchInput:focus{border-color:#4791ff}@media screen and (min-width:1441px){.searchBar .rightWrapper .inputWrapper .searchInput{width:535px}}.searchBar .rightWrapper .inputWrapper .closeIcon{color:#9195a3;cursor:pointer;display:grid;font-size:16px;position:absolute;right:10px;top:50%;transform:translateY(-50%)}.searchBar .rightWrapper .resultWrapper{background:#fff;box-shadow:none;box-sizing:content-box;left:0;position:absolute;top:39px;width:100%;z-index:100}.searchBar .rightWrapper .lemmaBtn{-webkit-appearance:none;background:#38f;border:1px solid;border-color:#38f #38f #2d78f4;border-radius:0;color:#fff;cursor:pointer;display:inline-block;float:left;font-family:arial;font-size:16px;height:40px;letter-spacing:1px;outline:medium;width:104px}.searchBar .rightWrapper .lemmaBtn:hover{background:#317ef3;border-color:#317ef3}.searchBar .rightWrapper .siteBtn{-webkit-appearance:none;background-color:#fff;border:1px solid #2e82ff;border-radius:0;color:#38f;cursor:pointer;display:inline-block;float:left;font-family:arial;font-size:16px;height:40px;letter-spacing:1px;margin-left:4px;outline:medium;width:104px}.searchBar .rightWrapper .helpBtn{color:#333;display:inline-block;float:right;font-size:13px;margin-left:14px;margin-top:17px;position:absolute;-webkit-text-decoration:underline;text-decoration:underline;width:26px}.searchBar.top-search{height:38px}.searchBar.top-search .logoWrapper{margin-right:10px;width:100px}.searchBar.top-search .logoWrapper .logo{height:33px}.searchBar.top-search .rightWrapper{padding-top:4px}.searchBar.top-search .rightWrapper .inputWrapper{height:34px}.searchBar.top-search .rightWrapper .inputWrapper .searchInput{font-size:16px;height:34px;line-height:22px;padding:6px 36px 4px 8px;width:400px}.searchBar.top-search .rightWrapper .inputWrapper .resultWrapper{background:#fff;box-shadow:none;position:absolute;top:33px;width:100%;z-index:100}.searchBar.top-search .rightWrapper .lemmaBtn{font-size:14px;height:34px;width:100px}.searchBar.top-search .rightWrapper .helpBtn,.searchBar.top-search .rightWrapper .siteBtn{display:none}.searchBar .splitLine{background-color:#f5f5f5;height:1px;margin:13px 16px 16px;padding:0 8px}.searchBar .hotBox{border:1px solid #ccc;border-top:none;cursor:default;font-size:14px;font-weight:400;line-height:22px;padding:0 15px 16px}.searchBar .hotBox .hotList{display:flex}.searchBar .hotBox .hotList>div{width:calc(50% - 10px)}.searchBar .hotBox .hotList .hotLeftList{margin:7px 10px 0 0}.searchBar .hotBox .hotList .hotRightList{margin-top:7px}.searchBar .hotBox .hotList .hotItem{border:1px solid transparent;border-radius:6px;cursor:pointer;display:flex;margin-top:5px;overflow:hidden;padding:4px 7px}.searchBar .hotBox .hotList .hotItem:hover{border-color:#e0e0e0}.searchBar .hotBox .hotList .hotItem:hover .hotDesc,.searchBar .hotBox .hotList .hotItem:hover .hotTitle{color:#38e!important}.searchBar .hotBox .hotList .hotItem .hotIdx,.searchBar .hotBox .hotList .hotItem img{background:#f5f5f6;border-radius:4px;color:#5b5d66;flex:none;font-size:11px;font-weight:600;line-height:16px;margin-right:4px;position:relative;text-align:center;top:1px}.searchBar .hotBox .hotList .hotItem .hotIdx{height:16px;width:16px}.searchBar .hotBox .hotList .hotItem .hotContent{line-height:1;overflow:hidden}.searchBar .hotBox .hotList .hotItem .hotContent .hotTitle{color:#222;font-size:14px;font-weight:500;line-height:20px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:keep-all}.searchBar .hotBox .hotList .hotItem .hotContent .hotDesc{color:#666;font-size:12px;line-height:1.5;margin-top:1.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:keep-all}.searchBar .historyBox{border:1px solid #ccc;cursor:default;font-size:14px;font-weight:400;line-height:22px;padding:16px 15px 24px}.searchBar .historyBox .historyTitle img{display:inline-block}.searchBar .historyBox .historyTitle .delAllBtn{color:#9195a3;cursor:pointer;float:right;font-size:12px;font-weight:400;line-height:1}.searchBar .historyBox .historyList{cursor:default;font-size:14px;font-weight:400;line-height:0;margin-top:2px}.searchBar .historyBox .historyList .historyItem{background:#f5f5f6;border:1px solid #f5f5f6;border-radius:6px;box-sizing:border-box;cursor:pointer;display:inline-block;line-height:20px;margin-right:10px;margin-top:10px;max-width:calc(50% - 10px);padding:4px 7px;position:relative}.searchBar .historyBox .historyList .historyItem.active{background:0 0;border-color:#e0e0e0;color:#38f}.searchBar .historyBox .historyList .historyItem .historyContent{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:keep-all}.searchBar .historyBox .historyList .historyItem .delItemBtn{background:#ccc;border-radius:50%;box-sizing:content-box;font-size:12px;font-weight:bolder;height:12px;line-height:14px;padding:1px 0;position:absolute;right:-6px;text-align:center;top:-6px;width:14px}.searchBar .historyBox .historyList .historyItem .delItemBtn svg{color:#fff;transform:scale(.66667)}.searchBar .sugWrapper{border:1px solid #ccc}.searchBar .resultBox{height:auto;margin:8px 0;padding:0}.searchBar .resultBox .resultItem{align-items:center;color:#222;cursor:pointer;display:flex;overflow:hidden;padding:8px 16px}.searchBar .resultBox .resultItem.active{background:#f9f9f9;color:#38f}.searchBar .resultBox .resultItem .resultPic{flex:none;margin-right:8px}.searchBar .resultBox .resultItem .resultPic img{border:1px solid #f5f5f5;border-radius:6px}.searchBar .resultBox .resultItem .resultContent{font-size:14px;line-height:20px;overflow:hidden}.searchBar .resultBox .resultItem .resultContent .resultDesc,.searchBar .resultBox .resultItem .resultContent .resultTitle{margin-bottom:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.searchBar .resultBox .resultItem .resultContent .resultDesc{line-height:1.5}.searchBar .resultBox .resultFooter{color:#999;cursor:pointer;font-size:12px;line-height:1.5;margin:5px 16px}.searchBar .resultBox .resultFooter span{color:#38f}.searchBar .resultBox .resultFooter svg{vertical-align:-1px}.searchBar .sugListWrapper{color:#222;line-height:30px;padding:0 0 16px}.searchBar .sugListWrapper .sugList{list-style:none}.searchBar .sugListWrapper .sugList svg{color:#9195a3;font-size:18px;margin-right:2px;vertical-align:6px}.searchBar .sugListWrapper .sugList li:first-child{color:#222;cursor:default;font-size:14px;font-weight:500;line-height:1;margin:16px 16px 6px}.searchBar .sugListWrapper .sugList li:first-child svg{vertical-align:-4px}.searchBar .sugListWrapper .sugList li:not(:first-child){color:#222;cursor:pointer;font-size:14px;height:30px;line-height:30px;list-style:none;padding:0 16px}.searchBar .sugListWrapper .sugList li .sugListItem{word-wrap:normal;display:inline-block;max-width:80%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.searchBar .sugListWrapper .sugList li.active{background:#f9f9f9;color:#38f}.searchBar .sugListWrapper .sugFooter{background-color:#fafafa;color:#666;font-size:12px;font-weight:400;height:26px;line-height:26px;padding:0 10px;text-align:right}.searchBar .sugListWrapper .sugFooter .sugFooterClose{cursor:pointer;display:inline;-webkit-text-decoration:underline;text-decoration:underline}</style><style id=4a1d8c91-8d26-533a-927b-88a34b3457b9 type="text/css">.index-module_topbar__aBAV7{border-bottom:1px solid #ebebeb;display:flex;height:32px;justify-content:flex-end;position:relative;z-index:6}.index-module_topbar__aBAV7 .index-module_websiteList__WqwA2{border-bottom:1px solid #f1f1f1}.index-module_topbar__aBAV7 .index-module_separator__gx7UX{border-right:1px solid #ebebeb;height:12px;margin:10px 13px 10px 25px}.index-module_topbar__aBAV7 .index-module_userBarBox__nGfXC{margin-top:6px}</style><style id=bc60e10a-2593-5f45-8eb4-db5e9e4678b7 type="text/css">.container{font-size:0;height:32px;line-height:32px}.container .tabItem{color:#333;display:inline-block;font-size:13px;font-weight:700;outline:0;text-align:center;-webkit-text-decoration:underline;text-decoration:underline;width:54px}.container .baike{color:#666;-webkit-text-decoration:none;text-decoration:none}</style><style id=0eb099c9-e806-565c-a04a-38cbef0bf4d5 type="text/css">.index-module_navBarWrapper__X0DND{background:#459df5;height:45px;overflow:hidden;position:relative;z-index:5}.index-module_navBarWrapper__X0DND a{-webkit-text-decoration:none;text-decoration:none}.index-module_navBarWrapper__X0DND.index-module_hover__Gsl-V{overflow:visible}.index-module_navBarWrapper__X0DND.index-module_hover__Gsl-V .index-module_navBarBg__jLfYB{background:rgba(36,97,158,.95)}.index-module_navBarWrapper__X0DND.index-module_hover__Gsl-V dd{opacity:1}.index-module_navBarWrapper__X0DND .index-module_navBar__fLItn{background:#459df5;height:45px;min-width:900px;position:relative;z-index:2}.index-module_navBarWrapper__X0DND .index-module_navBarBg__jLfYB{height:276px;left:0;position:absolute;top:0;transition:.3s;width:100%;z-index:1}.index-module_navBarWrapper__X0DND .index-module_navBarList__iL2jR{background:#459df5;border-bottom:1px solid #3b92e9;border-top:1px solid #5895d5;display:flex;height:43px;margin:0 auto;position:relative;width:1140px}.index-module_navBarWrapper__X0DND dl{position:relative}.index-module_navBarWrapper__X0DND dl.index-module_cooperation__ZISm7,.index-module_navBarWrapper__X0DND dl.index-module_guidance__XWoyq,.index-module_navBarWrapper__X0DND dl.index-module_second-know__o9jR2,.index-module_navBarWrapper__X0DND dl.index-module_special__j8K7K,.index-module_navBarWrapper__X0DND dl.index-module_team__Heqwh{width:126px}.index-module_navBarWrapper__X0DND dl.index-module_cooperation__ZISm7 dt,.index-module_navBarWrapper__X0DND dl.index-module_guidance__XWoyq dt,.index-module_navBarWrapper__X0DND dl.index-module_second-know__o9jR2 dt,.index-module_navBarWrapper__X0DND dl.index-module_special__j8K7K dt,.index-module_navBarWrapper__X0DND dl.index-module_team__Heqwh dt{width:124px}.index-module_navBarWrapper__X0DND dl.index-module_cooperation__ZISm7 dd,.index-module_navBarWrapper__X0DND dl.index-module_guidance__XWoyq dd,.index-module_navBarWrapper__X0DND dl.index-module_second-know__o9jR2 dd,.index-module_navBarWrapper__X0DND dl.index-module_special__j8K7K dd,.index-module_navBarWrapper__X0DND dl.index-module_team__Heqwh dd{width:125px}.index-module_navBarWrapper__X0DND dl.index-module_index__Kc2gf,.index-module_navBarWrapper__X0DND dl.index-module_user__Kc93c{width:94px}.index-module_navBarWrapper__X0DND dl.index-module_index__Kc2gf dt,.index-module_navBarWrapper__X0DND dl.index-module_user__Kc93c dt{width:92px}.index-module_navBarWrapper__X0DND dl.index-module_index__Kc2gf dd,.index-module_navBarWrapper__X0DND dl.index-module_user__Kc93c dd{width:93px}.index-module_navBarWrapper__X0DND dl.index-module_knowledge__9fisv dd,.index-module_navBarWrapper__X0DND dl.index-module_knowledge__9fisv dt{width:140px}.index-module_navBarWrapper__X0DND dl:hover dt{background:#338ce6}.index-module_navBarWrapper__X0DND dl:hover dd{background:#19508b;border-color:#184f8b}.index-module_navBarWrapper__X0DND dl:hover+dl dd{border-color:transparent!important;border-color:#184f8b}.index-module_navBarWrapper__X0DND dt{border-left:1px solid transparent!important;border-right:1px solid transparent!important;border:1px solid #59a1e9;border-bottom:0;border-top:0;color:#f5f5f5;cursor:pointer;font-size:16px;height:44px;line-height:44px;text-align:center;transition:background .3s}.index-module_navBarWrapper__X0DND dt a{color:#f5f5f5}.index-module_navBarWrapper__X0DND dt a.index-module_selected__6G8lk{border-bottom:2px solid #fff}.index-module_navBarWrapper__X0DND dd{border-left:1px solid #3a6fa2;height:210px;opacity:0;padding:11px 0 10px;transition:.3s}.index-module_navBarWrapper__X0DND dd .index-module_navBarItem__m7wZ4{font-size:14px;height:30px;line-height:30px;text-align:center}.index-module_navBarWrapper__X0DND dd .index-module_navBarItem__m7wZ4 a{color:#a2c9f0;padding-bottom:3px}.index-module_navBarWrapper__X0DND dd .index-module_navBarItem__m7wZ4 a:hover{border-bottom:2px solid #fff;color:#fff}.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ{align-items:center;display:flex;font-size:16px;height:24px;line-height:24px;margin:10px 0;position:absolute;right:0}.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_appDownload__Bx1Uj,.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_usercenter__brbF2{font-size:16px;height:16px;line-height:1;padding:0 15px}.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_appDownload__Bx1Uj a,.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_usercenter__brbF2 a{color:#fff}.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_appDownload__Bx1Uj path,.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_appDownload__Bx1Uj svg,.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_usercenter__brbF2 path,.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_usercenter__brbF2 svg{fill:#fff}.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_appDownload__Bx1Uj svg{animation:index-module_phone__w9Py1 2s;-webkit-animation:index-module_phone__w9Py1 2s infinite;font-size:18px;margin-right:3px;transform:rotate(0);transform-origin:center;vertical-align:-3px}.index-module_navBarWrapper__X0DND .index-module_rightList__RchPZ .index-module_usercenter__brbF2 svg{font-size:14px;margin-right:6px;vertical-align:-1px}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ{background:#9e1814}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ.index-module_hover__Gsl-V .index-module_navBarBg__jLfYB{background:rgba(124,25,22,.95)}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ .index-module_navBar__fLItn{background:#9e1814}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ .index-module_navBarList__iL2jR{background:#9e1814;border-bottom:1px solid #9e1814;border-top:1px solid #9e1814}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dl:hover dt{background:#8a1f1b}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dl:hover dd{background:#6d100c;border-color:#6d100c}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dl:hover+dl dd{border-color:#8a1f1b}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dt{border:none;color:#f5f5f5}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dt a{color:#f5f5f5}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dt a.index-module_selected__6G8lk{border-bottom:2px solid #fff}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dd{border-left:1px solid #8a1f1b}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dd .index-module_navBarItem__m7wZ4 a{color:#c29391}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ dd .index-module_navBarItem__m7wZ4 a:hover{border-bottom:2px solid #fff;color:#fff}.index-module_navBarWrapper__X0DND.index-module_red__45nyJ .index-module_rightList__RchPZ .index-module_usercenter__brbF2{border-left:1px solid #8a1f1b}@keyframes index-module_phone__w9Py1{0%{transform:rotate(0)}10%{transform:rotate(-15deg)}20%{transform:rotate(15deg)}30%{transform:rotate(-15deg)}40%{transform:rotate(15deg)}50%{transform:rotate(0)}to{transform:rotate(0)}}</style><style id=2b9857cd-80da-5aaa-87ee-a4b681a8122a type="text/css">.index-module_pageHeader__jSG5w{background:#fff}</style><style id=49bec857-be5e-5a08-91f9-142c8deda1d6 type="text/css">.copyright{color:#333;font-size:12px;text-align:center}.copyright a{color:#338de6;-webkit-text-decoration:none;text-decoration:none}.copyright .copyright-img{border:0;display:inline-block;vertical-align:text-bottom}.copyright .recordcode{height:16px;line-height:16px;padding-top:6px;text-align:center}.copyright .recordcode .icon-police{background:url(https://bkssl.bdimg.com/static/wiki-common/widget/component/footer/img/icon-police_5f07082.png) 0 0 no-repeat;display:inline-block;height:16px;margin-right:6px;margin-top:-4px;vertical-align:middle;width:14px}</style><style id=40bd6f5e-a1de-5197-9f33-f71a1b7570eb type="text/css">.page-footer-content{display:flex;font-size:12px;justify-content:center;width:100%}.page-footer-content:after,.page-footer-content:before{content:" ";display:table}.page-footer-content:after{clear:both}.page-footer-content .block-item{box-sizing:content-box;float:left;width:210px}.page-footer-content .block-item.fresh-block{padding:0 35px 0 5px}.page-footer-content .block-item.question-block{padding:0 35px}.page-footer-content .block-item.suggestion-block{padding:0 5px 20px 35px}.page-footer-content .block-item .block-title{color:#333;font-size:20px;line-height:1.5}.page-footer-content .block-item .block-title svg{color:#aaa;margin-right:10px}.page-footer-content .block-item .block-content{padding:5px 0 0 30px;width:210px}.page-footer-content .block-item .block-content>a{color:#666;cursor:pointer;float:left;margin-bottom:5px;position:relative;-webkit-text-decoration:none;text-decoration:none;width:90px}.page-footer-content .block-item .block-content>a:hover{color:#338de6;-webkit-text-decoration:none;text-decoration:none}.page-footer-content .block-item .block-content .block-content-new-icon{height:7px;left:52px;position:absolute;top:3px}</style><style id=8aab5fb5-8cb6-54a2-a460-f07cf71e8bf7 type="text/css">.index-module_drawerWrapper__-faIv{background:#fff;position:fixed;transition:all .2s ease-in}.index-module_drawerWrapper__-faIv.index-module_left__XRH8r{height:100%;top:0}.index-module_drawerWrapper__-faIv.index-module_left__XRH8r.index-module_show__1Cp0N{left:0!important}.index-module_drawerWrapper__-faIv.index-module_right__uKHeL{height:100%;top:0}.index-module_drawerWrapper__-faIv.index-module_right__uKHeL.index-module_show__1Cp0N{right:0!important}.index-module_drawerWrapper__-faIv.index-module_top__hwqPx{left:0;width:100%}.index-module_drawerWrapper__-faIv.index-module_top__hwqPx.index-module_show__1Cp0N{top:0!important}.index-module_drawerWrapper__-faIv.index-module_bottom__i8PPl{left:0;width:100%}.index-module_drawerWrapper__-faIv.index-module_bottom__i8PPl.index-module_show__1Cp0N{bottom:0!important}.index-module_drawerMask__ml9Mu{background-color:rgba(0,0,0,.5);display:none;height:100%;left:0;position:fixed;top:0;width:100%}.index-module_drawerMask__ml9Mu.index-module_show__1Cp0N{display:block}</style><style id=346042a3-757b-5d12-9c98-4d956a14c1f1 type="text/css">.index-module_loading__9u7M4{align-items:center;display:flex;flex-direction:column;height:100%;justify-content:center}.index-module_loading__text__SPpqU{color:#999;font-family:PingFang SC;font-size:14px;margin-top:12px}</style><style id=5ae34cd6-f69e-5a06-9b32-7d8140ddfa12 type="text/css">.index-module_errorContent__lN09c{align-items:center;display:flex;flex-direction:column;height:100%;justify-content:center;width:100%}.index-module_errorContent__img__MD3ok{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/error.png);background-position:50%;background-repeat:no-repeat;background-size:contain;height:90px;margin-bottom:12px;width:90px}.index-module_errorContent__title__9toRc{color:#999;font-family:PingFang SC;font-size:14px;line-height:1}</style><style id=b423d517-fce0-5acc-90ea-16d8aad26987 type="text/css">.index-module_detailBox__F52TQ{align-items:center;background:linear-gradient(90deg,#ffe9eb,#fff9f4 49.64%,#fffae2),linear-gradient(0deg,#fff,#fff);border:1px solid #fff;border-radius:20px;box-sizing:border-box;display:flex;height:92px;justify-content:center;margin-top:250px;position:relative;width:440px}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6{align-items:center;display:flex;flex:none;flex-direction:row;height:100%;position:relative;width:50%}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6:first-child:after{background:#f0f0f0;content:"";height:60px;position:absolute;right:0;top:16px;width:1px}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6 .index-module_icon__ZdL1G{flex:none;margin-left:4px}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6 .index-module_icon__ZdL1G .index-module_iconItem__gN9sm{height:64px;width:64px}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6 .index-module_info__bhEDZ .index-module_name__CI0DC{align-items:center;display:flex;flex-direction:row;font-family:PingFang SC;height:14px;line-height:1;margin-top:4px}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6 .index-module_info__bhEDZ .index-module_name__CI0DC .index-module_nameText__ryqlW{font-size:14px;font-weight:400}.index-module_detailBox__F52TQ .index-module_detailItem__AfoK6 .index-module_info__bhEDZ .index-module_name__CI0DC .index-module_link__TQALY{align-items:center;color:#f56933;cursor:pointer;display:inline-flex;font-size:14px;font-weight:500;margin-left:6px}.index-module_dynamicNumber__p3iYg{font-family:DIN Alternate;font-size:24px;font-weight:700;height:24px;letter-spacing:0;line-height:1;max-width:120px;overflow:hidden;text-align:left;text-overflow:ellipsis;white-space:nowrap}.index-module_dynamicNumber__container__QDKz5{display:inline-block;height:100%;overflow:hidden;position:relative;width:12px}.index-module_dynamicNumber__box__3wkrL{height:auto;left:0;position:absolute;top:0;transform-origin:0 0;transition:top .8s;width:100%;width:13px}.index-module_dynamicNumber__box__number__y0Xac{text-align:center}</style><style id=92b8ac20-2d15-545d-8ca5-6e2b124c7434 type="text/css">.index-module_moduleTitle__Jh7M7{align-items:center;display:flex;line-height:1;margin-top:24px}.index-module_moduleTitle__text__sYALt{-webkit-text-fill-color:transparent;background-clip:text;-webkit-background-clip:text;background-image:linear-gradient(180deg,#fe7d56,#f4483f);font-family:Alibaba PuHuiTi;font-size:22px;font-style:normal;font-weight:700}.index-module_moduleBox__Ch5RF{background-color:#fff;border-radius:20px;box-sizing:border-box;margin-top:12px;min-height:114px;padding:20px 20px 4px;position:relative;width:440px}.index-module_moduleBox__Ch5RF.index-module_recommendTask__HIVma{padding:16px 16px 0}.index-module_moduleBox__Ch5RF .index-module_circleIcon__bAZ-D{font-size:20px;left:12px;position:absolute;top:12px;z-index:0}.index-module_moduleBox__Ch5RF .index-module_boxTitle__JZXzd{color:#333;font-size:18px;font-weight:500;line-height:1;margin-bottom:4px;position:relative}.index-module_moduleTaskItem__lKWn-{box-sizing:border-box;display:flex;height:72px}.index-module_moduleTaskItem__lKWn-.index-module_notLast__pUZsC{border-bottom:1px solid #f0f0f0}.index-module_moduleTaskItem__left__VNkOe{flex:1;padding:18px 0 16px}.index-module_moduleTaskItem__right__0XSWk{align-items:center;display:flex;flex:none;justify-content:flex-end;width:80px}.index-module_moduleTaskItem__title__V2Jvo{align-items:center;color:#333;display:inline-flex;font-family:PingFang SC;font-size:14px;font-weight:500;line-height:1;position:relative}.index-module_moduleTaskItem__title__main__J-57k{line-height:20px;max-width:112px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.index-module_moduleTaskItem__title__allMain__QdaFC{background-color:#fff;border:1px solid #eee;border-radius:10px;box-shadow:0 2px 4px 0 rgba(0,0,0,.1);display:none;left:17px;line-height:18px;max-width:200px;padding:10px;position:absolute;top:21px;z-index:999}.index-module_moduleTaskItem__title__allMain__QdaFC.index-module_show__HViNe{display:block}.index-module_moduleTaskItem__title__experience__WJDDS,.index-module_moduleTaskItem__title__wealth__z00tA{align-items:center;display:inline-flex}.index-module_moduleTaskItem__title__experience__WJDDS.index-module_isRecommend__EBGaf,.index-module_moduleTaskItem__title__wealth__z00tA.index-module_isRecommend__EBGaf{font-size:14px}.index-module_moduleTaskItem__title__experience__WJDDS.index-module_isRecommend__EBGaf .index-module_rewardIcon__VN78Q,.index-module_moduleTaskItem__title__wealth__z00tA.index-module_isRecommend__EBGaf .index-module_rewardIcon__VN78Q{margin-top:0}.index-module_moduleTaskItem__title__wealth__z00tA{color:#fa9306;font-weight:400}.index-module_moduleTaskItem__title__wealth__z00tA .index-module_rewardIcon__VN78Q{height:18px;margin:-2px 2px 0 6px;width:18px}.index-module_moduleTaskItem__title__wealth__z00tA.index-module_noExp__-epC- .index-module_rewardIcon__VN78Q{margin-left:0}.index-module_moduleTaskItem__title__experience__WJDDS{color:#be7e2b;font-weight:400}.index-module_moduleTaskItem__title__experience__WJDDS.index-module_isRecommend__EBGaf .index-module_rewardIcon__VN78Q{margin-left:0}.index-module_moduleTaskItem__title__experience__WJDDS .index-module_rewardIcon__VN78Q{height:20px;margin:0 2px 0 5px;width:20px}.index-module_moduleTaskItem__tip__QTjIo{color:#999;font-size:12px;font-weight:300;line-height:1;margin-top:7px}.module-icon{background-position:50%;background-repeat:no-repeat;background-size:cover}</style><style id=1e9b4811-c412-5d50-90d7-975ac8a2ab40 type="text/css">.index-module_button__9YhOT{align-items:center;border-radius:42px;box-sizing:border-box;cursor:pointer;display:inline-flex;font-family:PingFang SC;font-size:14px;font-weight:500;height:32px;justify-content:center;width:74px}.index-module_button__9YhOT.index-module_normal__irvbe{border:1px solid #f56933;color:#f56933}.index-module_button__9YhOT.index-module_reward__cgnX-{background:linear-gradient(180deg,#fd7f5d,#f64f42);color:#fff}.index-module_button__9YhOT.index-module_lottery__qnJC-{background:linear-gradient(90deg,#ffab38,#fa9306);color:#fff}.index-module_button__9YhOT.index-module_takenBig__BTnJc,.index-module_button__9YhOT.index-module_taken__bf7h8{border:3px solid #f56933;border-radius:50%;box-sizing:border-box;color:#f56933;cursor:default;opacity:.2;position:relative;transform:rotate(-30deg)}.index-module_button__9YhOT.index-module_takenBig__BTnJc:before,.index-module_button__9YhOT.index-module_taken__bf7h8:before{border:1px solid #f56933;border-radius:50%;box-sizing:border-box;content:"";height:54px;left:3px;position:absolute;top:3px;width:54px}.index-module_button__9YhOT.index-module_taken__bf7h8{font-size:12.727px;height:60px;width:60px}.index-module_button__9YhOT.index-module_taken__bf7h8:before{height:48px;width:48px}.index-module_button__9YhOT.index-module_takenBig__BTnJc{height:66px;width:66px}</style><style id=1b957d50-d63e-5cb2-a1c0-07a2dfa1d5d2 type="text/css">.index-module_treasureBox__sd7Ni{background:linear-gradient(180deg,transparent 50%,#fff 0);border-radius:20px;box-sizing:border-box;margin-top:12px;padding:20px;position:relative;width:440px;z-index:1}.index-module_treasureBox__sd7Ni:before{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_box_bg.png);background-position:top;background-repeat:no-repeat;background-size:cover;content:"";height:50%;left:0;position:absolute;top:0;width:100%}.index-module_head__dxc9q{align-items:center;display:flex;line-height:1;position:relative}.index-module_head__dxc9q .index-module_bg__Qa7Y4{background:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_header_bg.png) no-repeat;background-size:contain;height:89px;position:absolute;right:-20px;top:-10px;width:266px;z-index:-1}.index-module_head__dxc9q .index-module_title__-7rBy{-webkit-text-fill-color:transparent;background:linear-gradient(180deg,#fe7d56,#f4483f);background-clip:text;-webkit-background-clip:text;font-family:Alibaba PuHuiTi;font-size:22px;font-style:normal;font-weight:700;width:190px}.index-module_head__dxc9q .index-module_reward__qRGo6{color:#fff;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:600;line-height:18px;overflow:hidden;position:relative;text-align:center;text-overflow:ellipsis;white-space:nowrap;width:158px}.index-module_head__dxc9q .index-module_rewardPic__zH5dS{background-position:50%;background-repeat:no-repeat;background-size:contain;height:64px;position:absolute;right:-16px;top:-20px;width:64px;z-index:-1}</style><style id=15aaf451-d0e6-5210-81aa-f7e3c93ded4e type="text/css">.index-module_background-contain__-fK27{background-position:50%;background-repeat:no-repeat;background-size:contain}.index-module_main__EF2e5{display:flex;height:132px;justify-content:center;position:relative;width:100%}.index-module_main__EF2e5 .index-module_column__OGOvm{background:#f3f3f3;border-radius:8px;height:8px;left:0;margin-top:-4px;position:absolute;top:50%;width:100%}.index-module_main__EF2e5 .index-module_column__OGOvm .index-module_progressColumn__6ne--{background:linear-gradient(270deg,#f54d42,#fd7754 98.45%);border-radius:8px;height:100%;left:0;position:absolute;top:0}.index-module_main__EF2e5 .index-module_boxs__F5zrj{height:100%;position:relative;width:100%;z-index:1}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m{align-items:center;display:inline-flex;flex-direction:column;height:100%;justify-content:flex-end;position:relative;width:20%}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m .index-module_boxLogo__fBcCv{align-items:center;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);background-color:hsla(0,0%,100%,.5);border-radius:50%;box-shadow:0 4px 10px 0 rgba(0,0,0,.05);box-sizing:border-box;cursor:pointer;display:flex;filter:drop-shadow(0 4px 10px rgba(0,0,0,.05));font-size:44px;height:64px;justify-content:center;margin-bottom:12px;position:relative;width:64px}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m .index-module_boxLogo__fBcCv .index-module_boxLogoIcon__785NF{background-position:50%;background-repeat:no-repeat;background-size:contain;height:44px;position:relative;width:44px}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_level-0__nDdg7 .index-module_boxLogoIcon__785NF{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_level_1.png)}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_level-1__2DEVe .index-module_boxLogoIcon__785NF{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_level_2.png)}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_level-2__IpNJy .index-module_boxLogoIcon__785NF{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_level_3.png)}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_level-3__0ItKW .index-module_boxLogoIcon__785NF{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_level_4.png)}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_level-4__NIbUE .index-module_boxLogoIcon__785NF{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_level_5.png)}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_active__2Lepi .index-module_boxLogo__fBcCv{border:2px solid rgba(245,105,51,.5)}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_done__98xZC .index-module_boxLogoIcon__785NF{opacity:.5}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_done__98xZC .index-module_boxStateTag__rLeV4{display:none}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m.index-module_done__98xZC .index-module_boxStateText__xyIbl{color:#999}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m .index-module_boxStateTag__rLeV4{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_pop.png);background-position:50%;background-repeat:no-repeat;background-size:contain;color:#fff;font-size:11px;height:22px;left:50%;line-height:18px;margin-left:-25px;position:absolute;text-align:center;top:12px;width:50px}.index-module_main__EF2e5 .index-module_boxs__F5zrj .index-module_box__vGC2m .index-module_boxStateText__xyIbl{color:#333;font-family:PingFang SC;font-size:14px;line-height:1;margin-bottom:12px}</style><style id=4b5cf905-8e72-5438-8ec3-db1829754f21 type="text/css">.task-drawer-dialog{left:50%;margin:0!important;position:absolute!important;top:50%;transform:translate(-50%,-50%);width:auto}.task-drawer-dialog.reward .rc-dialog-content{background:linear-gradient(180deg,#fff1f1,#fff)}.task-drawer-dialog.reward .rc-dialog-content:before{background:url(https://baikebcs.bdimg.com/baike-common/sdks/reward_bg.png) no-repeat;background-size:contain;content:"";height:200%;left:0;position:absolute;top:0;width:100%}.task-drawer-dialog .rc-dialog-content{border-radius:12px;width:486px}.task-drawer-dialog .rc-dialog-content .rc-dialog-close{font-size:30px;opacity:1;-webkit-text-decoration:none;text-decoration:none}</style><style id=1744ee19-5b88-502b-8381-753c41796597 type="text/css">.index-module_rewardTitle__Mdx5r{color:#333;font-family:Alibaba PuHuiTi;font-size:24px;font-style:normal;font-weight:700;margin:30px 0 0;text-align:center}.index-module_rewardButton__sYo1p{background:linear-gradient(180deg,#fd7f5d,#f64f42);border:none;border-radius:42px;color:#fff;cursor:pointer;font-family:PingFang SC;font-size:14px;font-weight:500;height:32px;line-height:32px;padding:0 16px;text-align:center}.index-module_rewardButton__sYo1p .index-module_normal__5KQj9{background:#f8f8f8;color:#666}.index-module_rewardVirtual__reward__sWf3F{display:flex;justify-content:center;margin-top:47px;position:relative;width:100%}.index-module_rewardVirtual__reward__experience__zuLND,.index-module_rewardVirtual__reward__wealth__e8-30{align-items:center;border-radius:25.909px;display:inline-flex;height:30px;padding-right:12px}.index-module_rewardVirtual__reward__wealth__e8-30{background:rgba(250,147,6,.1);color:#fa9306}.index-module_rewardVirtual__reward__wealth__e8-30 .index-module_rewardIcon__7-nPV{height:24.5px;margin:0 6px 0 2px;width:24.5px}.index-module_rewardVirtual__reward__experience__zuLND{background:rgba(190,126,43,.1);color:#be7e2b;margin-left:14px}.index-module_rewardVirtual__reward__experience__zuLND .index-module_rewardIcon__7-nPV{height:30px;margin:0 2px;width:30px}.index-module_rewardVirtual__tip1__x6hCX,.index-module_rewardVirtual__tip2__J8wHA{color:#333;font-family:PingFang SC;font-size:14px;font-weight:500;line-height:1;text-align:center}.index-module_rewardVirtual__tip1__x6hCX{margin-top:16px}.index-module_rewardVirtual__tip2__J8wHA{font-weight:400;margin-top:8px}.index-module_rewardVirtual__button__DWD-w{display:flex;justify-content:center;margin:46px auto 0;position:relative;width:100%}.index-module_rewardActual__content__zLQH1{display:flex;height:90px;justify-content:center;margin-top:43px;position:relative;width:100%}.index-module_rewardActual__content__rewardImg__C1isq{align-items:center;background-color:#fff;border-radius:10px;box-sizing:border-box;display:flex;flex:none;height:100%;justify-content:center;padding:10px;width:90px}.index-module_rewardActual__content__rewardImg__C1isq img{height:100%;width:100%}.index-module_rewardActual__content__rewardName__igHB0{color:#333;display:flex;flex:none;flex-direction:column;font-family:PingFang SC;font-size:14px;height:100%;justify-content:center;line-height:22px;margin-left:16px;width:253px}.index-module_rewardActual__content__rewardName__name__j7zu6{font-weight:500}.index-module_rewardActual__content__rewardName__link__mxs3h{cursor:pointer}.index-module_rewardActual__address__2nzRg{background:linear-gradient(181deg,rgba(255,244,234,.6) .95%,rgba(255,226,208,.6) 91.36%);border:1px solid #fff3e8;border-radius:8px;box-sizing:border-box;display:flex;flex-direction:column;font-family:PingFang SC;font-size:14px;line-height:1;margin:24px auto 0;min-height:94px;padding:12px;width:358px}.index-module_rewardActual__address__title__69efH{align-items:center;color:#666;display:inline-flex}.index-module_rewardActual__address__titleIcon__jez2B{margin-right:4px}.index-module_rewardActual__address__personInfo__9J0S9{color:#333;font-weight:500;margin-top:13px}.index-module_rewardActual__address__detailAddress__TeBqK{color:#333;font-weight:400;margin-top:8px}.index-module_rewardActual__button__v5DN-{display:flex;justify-content:center;margin:30px auto 0;position:relative;width:100%}.index-module_rewardActual__button__v5DN-.index-module_noAddress__nydQg{margin-top:43px}.index-module_rewardActual__button__v5DN- .index-module_normal__5KQj9{background:#f8f8f8;color:#666;margin-right:20px}.index-module_rewardActual__tip__UqFAe{align-items:center;color:#666;display:inline-flex;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:400;justify-content:center;line-height:1;margin-top:17px;width:100%}.index-module_rewardActual__tip__tipIcon__ffR5L{margin-right:4px}.index-module_rewardAddress__title__pxnml{color:#333;font-family:PingFang SC;font-size:16px;font-weight:600;margin-top:10px}.index-module_rewardAddress__addressForm__rT-EL{margin-right:10px;margin-top:20px}.index-module_rewardAddress__button__vIkPs{display:flex;justify-content:center;margin:30px auto 0;position:relative;width:100%}.index-module_rewardAddress__button__vIkPs .index-module_normal__5KQj9{background:#f8f8f8;color:#666;margin-right:20px}.index-module_errorDialog__header__wrYFL{color:#333;font-family:PingFang SC;font-size:16px;font-weight:500;padding-left:10px}.index-module_errorDialog__title__AZAHz{color:#333;font-family:PingFang SC;font-size:14px;margin-top:30px;text-align:center;width:100%}.index-module_errorDialog__button__ccjbr{display:flex;justify-content:center;margin:30px auto 0;position:relative;width:100%}.index-module_errorDialog__button__ccjbr .index-module_normal__5KQj9{background:#f8f8f8;color:#666;margin-right:20px}</style><style id=57f209a9-1fc6-5953-8910-40aa6efb2ec9 type="text/css">.index-module_toast__tPeew{align-items:center;background:#fff;border-radius:8px;box-shadow:0 9px 28px 8px rgba(0,0,0,.05),0 6px 16px 0 rgba(0,0,0,.08),0 3px 6px -4px rgba(0,0,0,.12);color:rgba(0,0,0,.88);display:inline-flex;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:400;justify-content:center;left:50%;padding:12px;position:fixed;top:50%;transform:translate(-50%,-50%);z-index:2147483647}.index-module_toast__tPeew.index-module_error__xZYRj svg{font-size:13px}.index-module_toast__tPeew svg{margin-right:8px}</style><style id=928cd726-5548-5ca7-aa7a-a893a82fe281 type="text/css">.index-module_treasureBox__p6ur2{background:linear-gradient(180deg,transparent 50%,#fff 0);border-radius:20px;box-sizing:border-box;margin-top:12px;padding:20px;position:relative;width:440px;z-index:1}.index-module_treasureBox__p6ur2:before{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_box_bg.png);background-position:top;background-repeat:no-repeat;background-size:cover;content:"";height:50%;left:0;position:absolute;top:0;width:100%}.index-module_bottom__iZNbx{background:linear-gradient(181deg,rgba(255,244,234,.6) .95%,rgba(255,226,208,.6) 91.36%);border:1px solid #fff3e8;border-radius:10px;box-sizing:border-box;display:flex;margin-top:10px;position:relative;width:100%}.index-module_bottom__iZNbx:before{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_opaque_triangle.png);background-position:50%;background-repeat:no-repeat;background-size:contain;content:"";height:12px;left:0;position:absolute;top:-12px;width:29px;z-index:0}.index-module_bottom__iZNbx.index-module_position-0__OwuAG:before{left:25px}.index-module_bottom__iZNbx.index-module_position-1__MOJ0c:before{left:105px}.index-module_bottom__iZNbx.index-module_position-2__ALRTi:before{left:185px}.index-module_bottom__iZNbx.index-module_position-3__7rwQ2:before{left:265px}.index-module_bottom__iZNbx.index-module_position-4__m9cwO:before{left:345px}.index-module_bottom__iZNbx.index-module_position-5__ddciv:before{left:425px}.index-module_bottom__iZNbx .index-module_left__ZHO5g,.index-module_bottom__iZNbx .index-module_right__ZBLaY{display:flex;flex-direction:column;padding:12px 0;z-index:1}.index-module_bottom__iZNbx .index-module_left__ZHO5g{flex:1;justify-content:center;margin-left:16px}.index-module_bottom__iZNbx .index-module_right__ZBLaY{flex:none;justify-content:center;margin:0 16px}.index-module_taskInfo__SY7OO{display:flex;font-family:PingFang SC;font-size:14px;line-height:22px}.index-module_taskInfo__SY7OO:first-child{margin-bottom:6px}.index-module_taskInfo__SY7OO .index-module_title__8Vbc9{color:#ab7b6c;flex:none;font-weight:500;margin-right:8px}.index-module_taskInfo__SY7OO .index-module_content__rUxx1{color:#333;flex:1}</style><style id=da96db17-1416-5c2a-8c42-68595a57a16d type="text/css">.index-module_treasureBox__Dkb2r{background:linear-gradient(180deg,transparent 50%,#fff 0);border-radius:20px;box-sizing:border-box;margin-top:12px;padding:20px;position:relative;width:440px;z-index:1}.index-module_treasureBox__Dkb2r:before{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_box_bg.png);background-position:top;background-repeat:no-repeat;background-size:cover;content:"";height:50%;left:0;position:absolute;top:0;width:100%}</style><style id=d1e16b63-f39a-5cff-bef2-2541360a6f82 type="text/css">.index-module_beginnerBox__pXSwY{font-family:PingFang SC;position:relative;width:100%}.index-module_taskJoin__title__Jc1oo{align-items:center;display:inline-flex}.index-module_taskJoin__title__link__Zkx-k,.index-module_taskJoin__title__tip__aBLy3{color:#999;font-family:PingFang SC;font-size:12px;font-style:normal;font-weight:400}.index-module_taskJoin__title__tip__aBLy3{margin-left:6px}.index-module_taskJoin__title__link__Zkx-k{cursor:pointer;display:inline-flex;margin-left:34px}.index-module_taskJoin__title__link__Zkx-k svg{font-size:11px}.index-module_taskJoin__reward__UYrOo{align-items:center;background:linear-gradient(90deg,#f8f8f8,hsla(0,0%,97%,0));border-radius:10px;color:#999;display:inline-flex;font-family:PingFang SC;font-size:12px;height:32px;margin-top:12px;padding-left:10px;width:388px}.index-module_taskJoin__reward__btn__4yNeV{align-items:center;display:flex;flex:1;justify-content:flex-end}.index-module_taskJoin__groups__3LHuh{display:flex;justify-content:space-between;margin:16px 0}.index-module_taskJoin__group__CRF9O{background:#f8f8f8;flex-direction:column;height:182px;padding-top:20px;position:relative;width:123px}.index-module_taskJoin__group__CRF9O,.index-module_taskJoin__group__img__oBU-B{align-items:center;border-radius:10px;box-sizing:border-box;display:flex;overflow:hidden}.index-module_taskJoin__group__img__oBU-B{background-color:#fff;flex:none;height:72px;justify-content:center;width:72px}.index-module_taskJoin__group__img__oBU-B img{height:100%;width:100%}.index-module_taskJoin__group__name__zs3Zb{box-sizing:border-box;color:#333;font-family:PingFang SC;font-size:14px;font-weight:500;line-height:1;overflow:hidden;padding:12px 10px 0;text-align:center;text-overflow:ellipsis;white-space:nowrap;width:100%}.index-module_taskJoin__group__init__o6ToB{margin-top:12px}.index-module_taskJoin__group__auditing__G6-QW,.index-module_taskJoin__group__deny__z0GSz{align-items:center;color:#666;display:inline-flex;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:500;margin-top:18px;text-align:center}.index-module_taskJoin__group__auditing__G6-QW svg,.index-module_taskJoin__group__deny__z0GSz svg{margin:1px 4px 0 0}.index-module_taskJoin__group__deny__z0GSz{color:#666}.index-module_taskJoin__group__joined__8cJNM{align-items:center;border:3px solid #f56933;border-radius:50%;bottom:-15px;box-sizing:border-box;color:#f56933;cursor:default;display:inline-flex;font-size:12.727px;font-weight:500;height:60px;justify-content:center;opacity:.2;position:absolute;right:-10px;transform:rotate(-30deg);width:60px}.index-module_taskJoin__group__joined__8cJNM:before{border:1px solid #f56933;border-radius:50%;box-sizing:border-box;content:"";height:48px;left:3px;position:absolute;top:3px;width:48px}</style><style id=ef33a923-3f12-5ed0-9cd0-a477e8efd184 type="text/css">.index-module_recommend__Dv1HU{position:relative}.index-module_recommend__title__pFhJG{align-items:center;display:inline-flex;flex:1;font-family:PingFang SC;font-size:12px;font-style:normal;font-weight:400;justify-content:space-between;margin-left:6px}.index-module_recommend__title__fire__gA0TO{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/hot.png);background-position:top;background-repeat:no-repeat;background-size:cover;height:16px;position:relative;width:35px}.index-module_recommend__title__link__ag5ur{color:#999;cursor:pointer;display:inline-flex}.index-module_recommend__title__link__ag5ur svg{font-size:11px}.index-module_recommend__content__Du-K6{display:flex;font-family:PingFang SC;height:112px;overflow:hidden;padding-bottom:16px}.index-module_recommend__content__left__5vuc3{background-color:#fff;border-radius:12px;flex:none;height:112px;overflow:hidden;width:168px}.index-module_recommend__content__left__5vuc3 img{border-radius:12px;height:112px;-o-object-fit:cover;object-fit:cover;width:168px}.index-module_recommend__content__right__JFpkz{flex:1;margin-left:16px;min-width:1px}.index-module_recommend__content__title__MbLet{color:#333;font-size:16px;font-weight:500;line-height:22px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:keep-all}.index-module_recommend__content__reward__zEcJr{display:inline-flex;margin-top:4px}.index-module_recommend__content__info__JiQCU{color:#999;font-size:12px;line-height:1;margin-top:4px}.index-module_recommend__content__btn__rbjh2{margin-top:14px}</style><style id=97fa1fa3-5eb5-5afb-b7a1-4e5cc7501f4e type="text/css">.index-module_background-contain__8qcMG{background-position:50%;background-repeat:no-repeat;background-size:contain}.index-module_dailySign__Ldl9A{background:linear-gradient(180deg,transparent 50%,#fffcfc 0);border-radius:20px;box-sizing:border-box;margin-top:12px;min-height:288px;padding:20px;position:relative;width:440px;z-index:0}.index-module_dailySign__Ldl9A:before{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/daily_sign_bg.png);background-position:top;background-repeat:no-repeat;background-size:cover;content:"";height:50%;left:0;position:absolute;top:0;width:100%}.index-module_dailySign__logo__r7dg3{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/daily_sign_logo.png);background-repeat:no-repeat;background-size:cover;height:97px;position:absolute;right:20px;top:-32px;width:119px;z-index:-1}.index-module_dailySign__Ldl9A .index-module_circleIcon__-0K--{font-size:20px;left:12px;position:absolute;top:12px}.index-module_dailySign__title__q4ZoV{align-items:center;color:#333;display:inline-flex;font-size:18px;font-weight:500;line-height:1;margin-bottom:4px;position:relative}.index-module_dailySign__signBtn__jOMZ0{align-items:center;color:#333;cursor:pointer;display:inline-flex;font-size:14px;font-weight:400;margin-left:10px}.index-module_dailySign__signBtn__jOMZ0 svg{font-size:12px;margin-left:2px}.index-module_dailySign__cellSigned__sT9ix{background:#fff1e7}.index-module_dailySign__cellSigned__Icon__-W54a{bottom:6px;font-size:9px;left:11.5px;position:absolute}.index-module_dailySign__calendar__3mrCf .ant-picker-panel-container{background-color:#fffcfc;height:339px;width:288px}.index-module_dailySign__calendar__3mrCf .ant-picker-footer{display:none}.index-module_dailySign__calendar__3mrCf .ant-picker-cell-inner{height:38px!important;width:30px}.index-module_dailySign__calendar__3mrCf .ant-picker-cell-disabled:before{height:38px!important}.index-module_dailySign__calendar__3mrCf .ant-picker-cell-today .ant-picker-cell-inner:before{border:none!important}.index-module_dailySign__calendar__3mrCf .ant-picker-cell-selected .ant-picker-cell-inner{background-color:#fff!important;color:#000!important}.index-module_dailySign__itemsWrapper__ddNUy{position:relative}.index-module_dailySign__itemsWrapper__ddNUy .index-module_toggleArrow__GYZJw{cursor:pointer;display:inline-block;font-size:13px;margin-top:-11px;position:absolute;top:50%;vertical-align:middle}.index-module_dailySign__itemsWrapper__ddNUy .index-module_toggleArrow__GYZJw.swiper-button-disabled{cursor:not-allowed}.index-module_dailySign__itemsWrapper__ddNUy .index-module_toggleArrow__GYZJw.swiper-button-disabled svg path{stroke:#d9d9d9}.index-module_dailySign__itemsWrapper__ddNUy .index-module_toggleArrow__GYZJw.index-module_prev__A8ADC{left:-18px}.index-module_dailySign__itemsWrapper__ddNUy .index-module_toggleArrow__GYZJw.index-module_next__XOvJX{right:-18px;transform:rotate(180deg)}.index-module_dailySign__items__-fDWV{display:flex;padding-top:24px;position:relative}.index-module_dailySign__item__kTKeY{align-items:center;background:#fff1e7;border-radius:8px;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:column;height:76px;justify-content:center;margin:0 16px 36px 0;position:relative;width:60px}.index-module_dailySign__item__kTKeY.index-module_active__G00J8{border:2px solid rgba(235,118,70,.5)}.index-module_dailySign__item__kTKeY.index-module_active__G00J8.index-module_taken__oWBUq{border:2px solid #cecece!important}.index-module_dailySign__item__kTKeY.index-module_active__G00J8 .index-module_dailySign__item__boxStateTag__slIyr{top:-18px}.index-module_dailySign__item__kTKeY.index-module_active__G00J8 .index-module_date__RDSOR{bottom:-24px}.index-module_dailySign__item__kTKeY.index-module_taken__oWBUq{background:#f5f5f5}.index-module_dailySign__item__kTKeY.index-module_taken__oWBUq .index-module_date__RDSOR,.index-module_dailySign__item__kTKeY.index-module_taken__oWBUq .index-module_wealth__3D4Yj{color:#999}.index-module_dailySign__item__kTKeY.index-module_big__zXFxs{margin-right:0;width:96px}.index-module_dailySign__item__kTKeY.index-module_big__zXFxs .index-module_goldIcon__43S6g{height:40px;margin-top:-5px;width:40px}.index-module_dailySign__item__kTKeY.index-module_big__zXFxs .index-module_wealth__3D4Yj{margin-top:2px}.index-module_dailySign__item__boxStateTag__slIyr{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_pop.png);background-position:50%;background-repeat:no-repeat;background-size:contain;color:#fff;display:block;font-size:11px;height:22px;left:50%;line-height:18px;margin-left:-25px;position:absolute;text-align:center;top:-16px;width:50px}.index-module_dailySign__item__kTKeY .index-module_goldIcon__43S6g{height:30px;width:30px}.index-module_dailySign__item__kTKeY .index-module_wealth__3D4Yj{color:#eb7646;font-size:14px;font-weight:500;margin-top:8px}.index-module_dailySign__item__kTKeY .index-module_date__RDSOR{bottom:-22px;color:#000;font-size:12px;position:absolute;text-align:center;width:100%}.index-module_dailySign__bottom__GmLFi{background:linear-gradient(181deg,rgba(255,244,234,.6) .95%,rgba(255,226,208,.6) 91.36%);border:1px solid #fff3e8;border-radius:10px;box-sizing:border-box;display:flex;font-family:PingFang SC;justify-content:space-between;margin-top:10px;min-height:80px;position:relative;width:100%}.index-module_dailySign__bottom__taskInfo__-hLBb{flex:none;padding:16px 0 16px 16px;width:182px}.index-module_dailySign__bottom__taskInfo__-hLBb .index-module_taskTitle__i-4Rm{font-size:16px;font-weight:500;line-height:20px}.index-module_dailySign__bottom__taskInfo__-hLBb .index-module_taskTip__dPaNh{color:#333;font-size:14px;font-weight:400;line-height:20px;margin-top:8px}.index-module_dailySign__bottom__btn__8CnXA{align-items:center;color:#666;display:flex;font-size:14px;font-weight:500;padding-right:16px}.index-module_dailySign__bottom__GmLFi:before{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/treasure_opaque_triangle.png);background-position:50%;background-repeat:no-repeat;background-size:contain;content:"";height:12px;left:0;position:absolute;top:-12px;width:29px;z-index:0}.index-module_dailySign__bottom__GmLFi.index-module_position-0__UPRYY:before,.index-module_dailySign__bottom__GmLFi.index-module_position-10__z6l9d:before,.index-module_dailySign__bottom__GmLFi.index-module_position-5__pF2tV:before{left:14px}.index-module_dailySign__bottom__GmLFi.index-module_position-11__u5ymG:before,.index-module_dailySign__bottom__GmLFi.index-module_position-1__9bGcw:before,.index-module_dailySign__bottom__GmLFi.index-module_position-6__9UN0b:before{left:91px}.index-module_dailySign__bottom__GmLFi.index-module_position-12__caOKz:before,.index-module_dailySign__bottom__GmLFi.index-module_position-2__owpcY:before,.index-module_dailySign__bottom__GmLFi.index-module_position-7__M2d9c:before{left:168px}.index-module_dailySign__bottom__GmLFi.index-module_position-13__pJbuL:before,.index-module_dailySign__bottom__GmLFi.index-module_position-3__iil9v:before,.index-module_dailySign__bottom__GmLFi.index-module_position-8__gxFlX:before{left:243px}.index-module_dailySign__bottom__GmLFi.index-module_position-14__PCnG5:before,.index-module_dailySign__bottom__GmLFi.index-module_position-4__Uj0ZK:before,.index-module_dailySign__bottom__GmLFi.index-module_position-9__Tcoov:before{left:337px}.index-module_dailySign__bottom__GmLFi .index-module_left__qCII6,.index-module_dailySign__bottom__GmLFi .index-module_right__fLQ9U{display:flex;flex-direction:column;padding:12px 0;z-index:0}.index-module_dailySign__bottom__GmLFi .index-module_left__qCII6{flex:1;justify-content:center;margin-left:16px}.index-module_dailySign__bottom__GmLFi .index-module_right__fLQ9U{flex:none;justify-content:center;margin:0 16px}</style><style id=fcc7544d-2d09-50ed-87f0-77c68fd149dc type="text/css">.index-module_dailyBox__iUf5Z{font-family:PingFang SC;position:relative;width:100%}</style><style id=1375ccde-2bf9-5cf7-8568-280fa87a4554 type="text/css">@font-face{font-family:Alibaba PuHuiTi;font-style:normal;font-weight:400;src:url(https://baikebcs.bdimg.com/baike-common/sdks/PuHuiTi-Bold.ttf) format("truetype")}.index-module_wrapper__nGohm{-ms-overflow-style:none;background-color:#f9f0f0;box-sizing:border-box;height:100%;overflow-y:auto;padding:20px;position:relative;scrollbar-width:none;width:100%}.index-module_wrapper__nGohm::-webkit-scrollbar{display:none;opacity:0}.index-module_wrapper__nGohm .index-module_wrapperBg__OKK1h{background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/drawer_bg_new.png);background-position:top;background-repeat:no-repeat;background-size:cover;height:871px;left:0;position:absolute;top:0;width:100%}</style><style id=d8909165-a07f-538d-942d-88c5d2d74b73 type="text/css">.index-module_drawerHand__aRhcO{align-items:center;background-image:url(https://baikebcs.bdimg.com/baike-common/sdks/drawer_hand_bg.png);background-position:50%;background-repeat:no-repeat;background-size:contain;cursor:pointer;display:flex;height:72px;justify-content:center;position:fixed;right:0;top:50%;transform:translateY(-36px);transition:all .2s ease-in;width:20px}.index-module_drawerHand__aRhcO .index-module_taskLogo__71Tj5{height:66px;position:absolute;right:30px;top:0;width:66px}.index-module_drawerHand__aRhcO.index-module_open__qMl5O{right:480px}.index-module_reverse__OKr6k{transform:rotate(180deg)}</style><script defer="defer" src="https://bkssl.bdimg.com/resource/lemma/lemma_e94add12.js"></script><link href="https://bkssl.bdimg.com/resource/lemma/lemma_8338f1a0.css" rel="stylesheet"></head><body><div id="root"><div class="pageWrapper_kS4wh" data-type="normal" data-category="normal"><div class="index-module_pageHeader__jSG5w"><div id="J-vars" style="display:none" data-lemmaid="407313" data-lemmatitle="Python" data-lemmacategory="normal"></div><div class="index-module_topbar__aBAV7"><div class="container "><a class="tabItem " href="https://www.baidu.com/s?ie=utf-8&amp;fr=bks0000&amp;wd=Python">网页</a><a class="tabItem " href="http://news.baidu.com/ns?tn=news&amp;cl=2&amp;rn=20&amp;ct=1&amp;fr=bks0000&amp;ie=utf-8&amp;word=Python">新闻</a><a class="tabItem " href="https://tieba.baidu.com/f?ie=utf-8&amp;fr=bks0000&amp;kw=Python">贴吧</a><a class="tabItem " href="https://zhidao.baidu.com/search?pn=0&amp;&amp;rn=10&amp;lm=0&amp;fr=bks0000&amp;word=Python">知道</a><a class="tabItem " href="https://pan.baidu.com/disk/home#/search?from=1027327l&amp;key=Python">网盘</a><a class="tabItem " href="http://image.baidu.com/search/index?tn=baiduimage&amp;ct=201326592&amp;lm=-1&amp;cl=2&amp;nc=1&amp;ie=utf-8&amp;word=Python">图片</a><a class="tabItem " href="https://www.baidu.com/sf/vsearch?pd=video&amp;tn=vsearch&amp;ie=utf-8&amp;rsv_spt=17&amp;wd=Python">视频</a><a class="tabItem " href="http://map.baidu.com/m?ie=utf-8&amp;fr=bks0000&amp;word=Python">地图</a><a class="tabItem " href="https://wenku.baidu.com/search?lm=0&amp;od=0&amp;ie=utf-8&amp;fr=bks0000&amp;word=Python">文库</a><a class="tabItem " href="https://www.baidu.com/s?rtt=1&amp;bsst=1&amp;cl=2&amp;tn=news&amp;fr=baike&amp;word=Python">资讯</a><a class="tabItem " href="https://b2b.baidu.com/s?fr=bks0000&amp;q=Python">采购</a><a class="tabItem baike ">百科</a></div><div class="index-module_separator__gx7UX"></div><div class="index-module_userBarBox__nGfXC"><div class="user-bar  "><div class="user-bar-item"><a href="http://www.baidu.com" target="_blank" rel="noreferrer">百度首页</a></div><div class="user-bar-item user"><a>登录</a></div><div class="user-bar-item user"><a href="https://passport.baidu.com/v2/?reg&amp;regType=1&amp;tpl=wk">注册</a></div></div></div></div><div class="lemmaSearchBarWrapper undefined"><div class="lemmaSearchBar"><div class="searchBar clearfix search-bar"><a class="logoWrapper" href="/#home"><img class="logo" src="https://baikebcs.bdimg.com/baike-react/common/logo-baike.svg" alt="百度百科"/></a><div class="rightWrapper"><div class="inputWrapper"><input class="searchInput" autoComplete="off" autoCorrect="off" value="Python"/><span class="placeholder showIn"></span><span class="closeIcon"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024"><path d="m566.4 512 318.4-318.4c16-16 16-38.4 0-54.4s-38.4-16-54.4 0L512 457.6 192 140.8c-14.4-16-38.4-16-52.8 0s-16 38.4 0 54.4L457.6 512 139.2 830.4c-16 16-16 38.4 0 54.4 8 6.4 16 11.2 27.2 11.2s19.2-3.2 27.2-11.2l320-318.4 320 316.8c8 6.4 19.2 11.2 27.2 11.2s19.2-3.2 27.2-11.2c16-16 16-38.4 0-54.4z"></path></svg></span></div><button class="lemmaBtn">进入词条</button><button class="siteBtn">全站搜索</button><a href="/help" target="_blank" class="helpBtn">帮助</a></div></div></div></div><div class="index-module_navBarWrapper__X0DND"><div class="index-module_navBar__fLItn"><div class="index-module_navBarList__iL2jR"><dl class="index-module_index__Kc2gf"><dt><a class="" href="/">首页</a></dt><dd></dd></dl><dl class="index-module_second-know__o9jR2"><dt><a class="" href="#">秒懂百科</a></dt><dd></dd></dl><dl class="index-module_special__j8K7K"><dt><a class="" href="#">特色百科</a></dt><dd></dd></dl><dl class="index-module_knowledge__9fisv"><dt><a class="" href="#">知识专题</a></dt><dd></dd></dl><dl class="index-module_guidance__XWoyq"><dt><a class="" href="#">加入百科</a></dt><dd></dd></dl><dl class="index-module_team__Heqwh"><dt><a class="" href="#">百科团队</a></dt><dd></dd></dl><dl class="index-module_cooperation__ZISm7"><dt><a class="" href="#">权威合作</a></dt><dd></dd></dl><div class="index-module_rightList__RchPZ"><div class="index-module_usercenter__brbF2"><a href="/usercenter" rel="noreferrer" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 1024 1024"><path d="M726.44 587.028c26.042-40.064 41.464-87.632 41.464-139.04v-191.97C767.906 114.626 653.354 0 511.936 0 370.568 0 255.99 114.626 255.99 256.018v191.97c0 51.406 15.424 98.974 41.462 139.04C148.028 621.784 32.248 743.064 6.412 896.002L0 959.996C0 995.352 28.644 1024 63.996 1024H959.9c35.352 0 63.996-28.646 63.996-64.002l-6.412-63.992C991.748 743.064 875.87 621.784 726.44 587.028M346.73 256.016c0-70.682 79.674-168.934 165.206-168.934 89.334 0 168.958 98.248 168.958 168.934v191.97c0 70.734-83.376 150.356-168.958 150.356-89.29 0-165.206-79.622-165.206-150.356zM119.884 936.962c10.114-122.586 103.258-255.29 250.334-255.29h282.786c151.058 0 242.3 130.698 250.314 255.29z"></path></svg>个人中心</a></div></div></div></div><div class="index-module_navBarBg__jLfYB"></div></div></div><div class="lemmaWrapper_ZCQnB" id="J-lemma-main-wrapper"><div><div class="secondContainer_O7hBj normalHeader__lXN6"><div class="secondContent_vh5t3"><div class="contentTop_Cta5w"><div class="lemmaTitleWrap_NOOPF normalText_v8dYP normal"><div class="lemmaTitleBox__OVrj"><h1 class="lemmaTitle_qmNnR J-lemma-title">Python</h1><div class="btnList_HVoIW"><span><span data-tts-from="lemmaTitle" class="ttsBtn_fORkB btnItem_U4SAt undefined"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><a class="btnItem_U4SAt undefined" href="/planet/talk?lemmaId=407313&amp;fromModule=lemma_right-issue-btn" target="_blank"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M2.52 14.62c-.4 0-.8-.3-1-.7-.1-.2-.1-.4 0-.6l.7-2.4c-1.1-2.5-.6-5.6 1.3-7.6 1.2-1.2 2.9-1.9 4.7-1.9s3.5.7 4.7 1.9c2.5 2.5 2.5 6.7 0 9.2-2 2-5.1 2.5-7.6 1.2l-2.5.8c-.1.1-.2.1-.3.1zm5.7-12.1c-1.5 0-2.9.6-3.9 1.6-1.6 1.7-2 4.3-.9 6.4.1.1.1.3 0 .4l-.7 2.4 2.4-.8c.1-.1.3 0 .5 0 2.1 1.1 4.8.8 6.5-.9 2.1-2.1 2.1-5.5 0-7.6-1.1-.9-2.5-1.5-3.9-1.5zm-4.3 1.2z"></path></svg>讨论</a><a class="btnItem_U4SAt undefined disabled_XyHbm"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M9.1 2.4c1.4 0 2.5 1.1 2.5 2.5l1.1-.8c.2-.2.5-.3.8-.3h.2c.8 0 1.4.6 1.5 1.4v6c0 .3-.1.7-.3 1-.5.6-1.3.8-2 .4l-.1-.1-1.1-.8v.1C11.6 13 10.5 14 9.1 14H3C1.6 14 .5 12.9.5 11.5V5c0-1.3 1-2.4 2.4-2.5h6.2zm0 1.2H2.9c-.8 0-1.3.6-1.3 1.4v6.5c0 .8.6 1.4 1.4 1.4h6.2c.8 0 1.4-.6 1.4-1.4V5c-.1-.8-.7-1.4-1.5-1.4zm4.6 1.3h-.2s-.1 0-.1.1l-1.8 1.4v3.8l1.7 1.4.1.1c.2.1.4.1.5-.1.1-.1.1-.2.1-.3v-6c.1-.3-.1-.4-.3-.4z"></path><path d="M4.5 6.4h1.7c.3 0 .6-.3.6-.6s-.3-.5-.6-.5H4.5c-.3 0-.5.3-.5.6s.2.5.5.5z"></path></svg>上传视频</a></div></div><div id="lemmaDesc" class="lemmaDesc_zXTOt"><div class="lemmaDescText_oDqGX">计算机编程语言</div><div class="polysemantText_n6DKZ J-polysemantText"><span>展开</span>3个同名词条<svg width="1em" height="1em" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="polysemantIcon_oBGb3"><path d="M10 4 6 8 2 4" stroke="#459DF5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div></div></div></div><div class=" contentBottom_ZD9Gg"></div></div></div></div><div class="contentWrapper_EGLeU"><div class="contentBox_Ab6AK"><div class="mainContent_ZCTL5"><div class="contentTab_BGG9i curTab_o6Gql" type="defaultTab"><div class="topToolsWrap_jJXlh"><div class="topToolsBox_LdR3t"><div class="topCollect_tuVwr topToolsBox__item_bFh6_"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" style="enable-background:new 0 0 16 16" xml:space="preserve" width="1em" height="1em" class="topToolsBox__icon_Xup1V"><path d="M8.5 1.1c.*******.5.5l1.6 3.2 3.5.5c.6.1 1 .6.9 1.2 0 .2-.1.5-.3.6l-2.6 2.5.6 3.5c.1.6-.3 1.2-.9 1.3-.2 0-.5 0-.7-.1L8 12.6l-3.1 1.7c-.5.3-1.2.1-1.5-.4-.1-.2-.2-.5-.1-.7l.6-3.5-2.6-2.5c-.4-.4-.4-1.1 0-1.5.2-.2.4-.3.6-.3l3.5-.5L7 1.6c.3-.5.9-.8 1.5-.5zm-.6.9L6.4 5.2l-.3.5-.5.1-3.5.5H2v.1l2.6 *******-.1.5-.6 3.5v.1s.1.1.1 0l3.1-1.7.5-.2.5.2 3.1 1.7h.1l.1-.1-.6-3.5-.2-.5.4-.4L14 6.4s0-.1-.1-.1l-3.5-.5-.5-.1-.3-.5L8.1 2h-.2z" style="fill:#459df5"></path></svg><span>收藏</span><div class="collectTip_jmwIz">查看<a href="/uc/favolemma" target="_blank">我的收藏</a></div></div><div class="topVote_GKwu5 topToolsBox__item_bFh6_"><span class="voteCount_wHjOV"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" style="enable-background:new 0 0 16 16" xml:space="preserve" width="1em" height="1em" class="topToolsBox__icon_Xup1V"><path d="M7.2 1.6c1.5-.4 2.8.8 2.3 3.1 0 .2-.1.4-.1.7v.2h2.9c.5 0 .8.1 1.1.3l.1.1c1.6 1.6.3 7.8-1.9 8.3H3.5c-1 0-1.7-.8-1.7-1.7v-5c0-1 .8-1.7 1.7-1.7h.4c.5 0 .8-.2 1.1-.6.4-.5.5-.8.8-1.6.2-.2.2-.2.3-.7.3-.8.5-1.2 1.1-1.4zm1.4 2.9c.3-1.5-.4-2.1-1.1-1.9-.2 0-.3.1-.4.6-.2.6-.2.6-.2.8 0 0-.1.2-.1.3-.1.2-.2.3-.2.4-.2.4-.4.8-.7 1.2-.2.2-.3.4-.5.5l-.1.1v6.7h6.3c1.3-.5 2.3-5.3 1.4-6.4l-.1-.1c-.1-.1-.3-.2-.5-.2H8.7c-.3 0-.6-.3-.5-.6.1-.3.3-1.1.4-1.4zm-4.4 8.7V6.9H3.5c-.4 0-.7.3-.7.7v4.8c0 .*******.7l.7.1z" style="fill:#459df5"></path></svg><span class="count_PjQlp">0</span></span><span class="voteTip_nlOUg">有用+1</span></div><div class="topShare_rnqA_ topToolsBox__item_bFh6_"><div class="shareBubbleBox_QATw0 fromTopTools_kohBf"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" style="enable-background:new 0 0 16 16" xml:space="preserve" width="1em" height="1em" class="shareIcon_sQFF1"><path d="M8.9 1.6c.3 0 .7.1.9.3l4.8 4.2c.9.8 1 2.3.2 3.2l-.2.2-4.8 4.2c-.6.5-1.4.4-1.9-.1-.2-.3-.3-.6-.3-.9v-1.6h-.3c-2.1.3-4 1.2-5.6 2.4l-.3.3c-.3.3-.8 0-.8-.4.2-4.1 3-7.6 6.9-8.5h.1V3c0-.7.5-1.3 1.2-1.4h.1zm0 1c-.2 0-.4.2-.4.4v2.3c0 .2-.2.5-.4.5-3.3.5-5.8 3.1-6.5 6.4v.2l.1-.1c1.8-1.2 3.8-1.9 5.9-2.2L8 10c.3 0 .5.2.5.5v2.2c0 .1 0 .******* 0 .4 0 .6-.1L14 8.7l.1-.1c.5-.5.4-1.3-.1-1.8L9.2 2.6h-.3z" style="fill:#459df5"></path></svg>0<div class="shareBubble_p82_H"><div class="triangle_m3wRm"></div><div class="iconBox_lcxN5"><svg fill="currentColor" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="weChat_FhuV0"><path d="M693.12 347.264c11.776 0 23.36.896 35.008 2.176-31.36-146.048-187.456-254.528-365.696-254.528C163.2 94.912 0 230.656 0 403.136c0 99.52 54.272 181.248 145.024 244.736L108.8 756.864l126.72-63.488c45.312 8.896 81.664 18.112 126.912 18.112 11.392 0 22.656-.512 33.792-1.344-7.04-24.256-11.2-49.6-11.2-76.032.064-158.336 136-286.848 308.096-286.848zm-194.816-98.24c27.392 0 45.376 17.984 45.376 45.248 0 27.136-17.984 45.312-45.376 45.312-27.072 0-54.336-18.176-54.336-45.312 0-27.328 27.2-45.248 54.336-45.248zm-253.632 90.56c-27.2 0-54.592-18.176-54.592-45.312 0-27.264 27.392-45.248 54.592-45.248s45.248 17.92 45.248 45.248c0 27.136-18.048 45.312-45.248 45.312zM1024 629.76c0-144.896-145.024-262.976-307.904-262.976-172.48 0-308.224 118.144-308.224 262.976 0 145.28 135.808 262.976 308.224 262.976 36.096 0 72.512-9.024 108.736-18.112l99.392 54.528-27.264-90.624C969.728 783.872 1024 711.488 1024 629.76zm-407.872-45.376c-17.984 0-36.224-17.92-36.224-36.224 0-18.048 18.24-36.224 36.224-36.224 27.52 0 45.376 18.176 45.376 36.224 0 18.304-17.856 36.224-45.376 36.224zm199.36 0c-17.856 0-36.032-17.92-36.032-36.224 0-18.048 18.112-36.224 36.032-36.224 27.264 0 45.376 18.176 45.376 36.224 0 18.304-18.112 36.224-45.376 36.224z"></path></svg></div><div class="iconBox_lcxN5"><svg fill="currentColor" viewBox="0 0 1025 1024" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="qZone_phBaA"><path d="M1023.21 395.69c-2.005-6.08-7.38-10.41-13.78-10.943l-347.115-29.803-135.68-320.96c-2.518-5.888-8.235-9.728-14.635-9.728s-12.181 3.861-14.677 9.75L361.707 354.943 14.57 384.747C8.213 385.28 2.773 389.61.789 395.69c-1.941 6.101-.106 12.778 4.715 16.96l263.339 228.138-78.955 339.371c-1.43 6.208 1.003 12.757 6.123 16.512 5.205 3.733 12.117 4.01 17.642.683L512 817.45l298.283 179.946c2.581 1.558 5.418 2.347 8.32 2.347 3.221 0 6.528-1.003 9.344-3.008 5.162-3.84 7.594-10.368 6.122-16.576l-62.442-268.352c26.73-13.483 56.277-36.245 56.277-36.245s-116.864 57.066-536.341 29.205l304.064-220.33s-11.968-18.582-392.982-31.873c-25.408-.917 310.678-63.722 558.934-10.645l-305.28 218.987s122.453 18.197 307.328 16.085l-8.448-36.181 263.36-228.16c4.842-4.182 6.677-10.859 4.672-16.96z"></path></svg></div><div class="iconBox_lcxN5"><svg fill="currentColor" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="qq_TRjDN"><path d="M866.991 707.772s-25.341 69.55-71.877 132.071c0 0 83.05 28.439 76.137 102.196 0 0 2.709 82.331-177.613 76.685 0 0-126.817-9.898-164.88-63.85h-33.53c-38.018 54.006-164.834 63.85-164.834 63.85-180.431 5.7-177.556-76.685-177.556-76.685-7.025-73.756 76.081-102.196 76.081-102.196-46.475-62.519-71.875-132.071-71.875-132.071C44.285 890.913 55.567 682.205 55.567 682.205 76.708 558.71 165.516 477.821 165.516 477.821c-12.677-112.157 33.805-132.078 33.805-132.078C209.001-1.236 505.794 4.856 511.996 5.021c6.249-.163 302.931-6.256 312.72 340.723 0 0 46.538 19.866 33.812 132.078 0 0 88.751 80.89 109.886 204.384.055-.001 11.285 208.707-101.421 25.566z"></path></svg></div><div class="iconBox_lcxN5"><svg fill="currentColor" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="sina_SYs8y"><path d="M751.76 502.938c-38.8-7.542-19.953-28.34-19.953-28.34s37.95-62.515-7.537-108.006c-56.31-56.315-193.147 7.173-193.147 7.173-52.3 16.18-38.436-7.419-31.017-47.554 0-47.31-16.174-127.468-155.443-80.154-138.9 47.56-258.335 214.308-258.335 214.308C3.251 571.167 14.321 656.799 14.321 656.799c20.797 189.01 221.609 240.947 377.902 253.23C556.544 922.92 778.399 853.35 845.66 710.436c67.256-143.155-54.984-199.716-93.9-207.498zM404.377 856.76c-163.226 7.66-295.071-74.199-295.071-183.05 0-108.98 131.845-196.434 295.07-203.98 163.354-7.537 295.44 59.724 295.44 168.58-.123 108.739-132.214 211.026-295.44 218.45zm-32.594-315.264C207.703 560.706 226.683 714.45 226.683 714.45s-1.705 48.655 44.027 73.472c96.087 51.927 194.846 20.546 244.838-44.032 49.869-64.594 20.557-221.491-143.764-202.394zm-41.355 215.767c-30.648 3.65-55.347-13.987-55.347-39.649 0-25.549 21.898-52.3 52.552-55.465 35.149-3.277 58.138 16.911 58.138 42.568 0 25.42-24.812 49.019-55.348 52.546zm96.697-82.467c-10.343 7.787-23.112 6.691-28.585-2.55-5.714-9.124-3.528-23.598 6.932-31.258 12.165-9 24.812-6.451 30.29 2.673 5.468 9.247 1.454 23.111-8.637 31.135zm404.536-238.388c13.26 0 24.453-9.856 26.394-22.502.245-.973.368-1.828.368-2.8 20.07-180.378-147.66-149.361-147.66-149.361-14.843 0-26.88 12.042-26.88 27.125a26.808 26.808 0 0 0 26.88 26.88c120.412-26.639 93.9 93.901 93.9 93.901 0 14.833 12.037 26.757 26.998 26.757zm-19.579-314.537c-58.02-13.619-117.622-1.822-134.4 1.337-1.218.123-2.437 1.341-3.65 1.587-.615.118-.85.727-.85.727-16.42 4.623-28.585 19.947-28.585 37.95 0 21.529 17.392 39.162 39.045 39.162 0 0 21.16-2.795 35.39-8.397 14.233-5.713 134.282-4.25 193.996 95.97 32.604 73.098 14.234 121.994 12.042 129.904 0 0-7.782 18.97-7.782 37.704 0 21.525 17.393 35.149 39.045 35.149 17.997 0 33.203-2.432 37.58-32.963h.247c64.102-213.703-78.208-313.922-182.078-338.125z"></path></svg></div></div></div></div></div></div><div class="lemmaSummary__tEeY J-summary"><a name="lemma-summary" class="lemmaSummaryAnchor_bQD8R"></a><div class="para_YYuCh summary_nfAdr MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6pwz" data-idx=""><span class="text_H038s" data-text="true">Python由</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E8%8D%B7%E5%85%B0%E5%9B%BD%E5%AE%B6%E6%95%B0%E5%AD%A6%E4%B8%8E%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E7%A0%94%E7%A9%B6%E4%B8%AD%E5%BF%83/53889845?fromModule=lemma_inlink" target="_blank" data-from-module="summary">荷兰国家数学与计算机科学研究中心</a></span><span class="text_H038s" data-text="true">的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%90%89%E5%A4%9A%C2%B7%E8%8C%83%E7%BD%97%E8%8B%8F%E5%A7%86/328361?fromModule=lemma_inlink" target="_blank" data-from-module="summary">吉多·范罗苏姆</a></span><span class="text_H038s" data-text="true">于1990年代初设计，作为一门叫做</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/ABC%E8%AF%AD%E8%A8%80/334996?fromModule=lemma_inlink" target="_blank" data-from-module="summary">ABC语言</a></span><span class="text_H038s" data-text="true">的替代品。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [1]<em id="sup-1"></em></sup></span><span class="text_H038s" data-text="true">Python提供了高效的高级</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84/1450?fromModule=lemma_inlink" target="_blank" data-from-module="summary">数据结构</a></span><span class="text_H038s" data-text="true">，还能简单有效地</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E9%9D%A2%E5%90%91%E5%AF%B9%E8%B1%A1/2262089?fromModule=lemma_inlink" target="_blank" data-from-module="summary">面向对象</a></span><span class="text_H038s" data-text="true">编程。Python语法和动态类型，以及</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E8%A7%A3%E9%87%8A%E5%9E%8B%E8%AF%AD%E8%A8%80/8888952?fromModule=lemma_inlink" target="_blank" data-from-module="summary">解释型语言</a></span><span class="text_H038s" data-text="true">的本质，使它成为多数平台上写脚本和快速开发应用的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%BC%96%E7%A8%8B%E8%AF%AD%E8%A8%80/9845131?fromModule=lemma_inlink" target="_blank" data-from-module="summary">编程语言</a></span><span class="text_H038s" data-text="true">，</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [2]<em id="sup-2"></em></sup></span><span class="text_H038s" data-text="true">随着版本的不断更新和语言新功能的添加，逐渐被用于独立的、大型项目的开发。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [3]<em id="sup-3"></em></sup></span></div><div class="para_YYuCh summary_nfAdr MARK_MODULE" data-tag="paragraph" data-uuid="tFetf95K8f1G" data-idx=""><span class="text_H038s" data-text="true">Python在各个编程语言中比较适合新手学习，Python解释器易于扩展，可以使用</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C/7252092?fromModule=lemma_inlink" target="_blank" data-from-module="summary">C</a></span><span class="text_H038s" data-text="true">、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C%2B%2B/99272?fromModule=lemma_inlink" target="_blank" data-from-module="summary">C++</a></span><span class="text_H038s" data-text="true">或其他可以通过C调用的语言扩展新的功能和</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B/10997964?fromModule=lemma_inlink" target="_blank" data-from-module="summary">数据类型</a></span><span class="text_H038s" data-text="true">。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [4]<em id="sup-4"></em></sup></span><span class="text_H038s" data-text="true">Python也可用于可定制化软件中的扩展程序语言。Python丰富的标准库，提供了适用于各个主要系统平台的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%BA%90%E7%A0%81/344212?fromModule=lemma_inlink" target="_blank" data-from-module="summary">源码</a></span><span class="text_H038s" data-text="true">或</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%9C%BA%E5%99%A8%E7%A0%81/86125?fromModule=lemma_inlink" target="_blank" data-from-module="summary">机器码</a></span><span class="text_H038s" data-text="true">。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [4]<em id="sup-4"></em></sup></span></div></div><div class="skeletonWrap_u6PoY"><div class="skeletonContent__ObZd"><div class="skeletonTitle_ZOggi"></div><div class="skeletonList_UERWH"><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div></div></div><div class="skeletonContent__ObZd"><div class="skeletonTitle_ZOggi"></div><div class="skeletonList_UERWH"><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div><div class="skeletonItem_lMzhn"><div class="skeletonItemCover_okbdk"></div><div class="skeletonItemInfo_f1DdT"><div class="infoName_bA4W2"></div><div class="infoDesc_tyV9J"></div></div></div></div></div></div><div class="basicInfo_g5pvC J-basic-info"><dl class="basicInfoBlock_B13bA left"><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">软件名称</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">Python</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">软件平台</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true"> </span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Windows%E6%93%8D%E4%BD%9C%E7%B3%BB%E7%BB%9F/852149?fromModule=lemma_inlink" target="_blank" data-from-module="basicInfo">Windows操作系统</a></span><span class="text_H038s" data-text="true">、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Linux/27050?fromModule=lemma_inlink" target="_blank" data-from-module="basicInfo">Linux</a></span><span class="text_H038s" data-text="true">、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/UNIX/219943?fromModule=lemma_inlink" target="_blank" data-from-module="basicInfo">UNIX</a></span><span class="text_H038s" data-text="true">、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/MacOS/8654551?fromModule=lemma_inlink" target="_blank" data-from-module="basicInfo">MacOS</a></span><span class="text_H038s" data-text="true">等</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">上线时间</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">1991年</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">最近更新时间</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">2023年6月6日</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">软件语言</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">Python</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">开发商</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">Python Software Foundation</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">软件授权</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">Python Software Foundation</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [1]<em id="sup-1"></em></sup></span></dd></div></dl><dl class="basicInfoBlock_B13bA right"><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">软件版本</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">python2.x</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [5]<em id="sup-5"></em></sup></span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [12]<em id="sup-12"></em></sup></span><br/><span class="text_H038s" data-text="true">python3.x</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">软件大小</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">26 至 29 MB</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">是否区分大小写</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">是</span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">创始人</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%90%89%E5%A4%9A%C2%B7%E8%8C%83%E7%BD%97%E8%8B%8F%E5%A7%86/328361?fromModule=lemma_inlink" target="_blank" data-from-module="basicInfo">吉多·范罗苏姆</a></span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">最新正式版本</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">Python 3.12.4</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [14]<em id="sup-14"></em></sup></span></dd></div><div class="itemWrapper_uOa7v"><dt class="basicInfoItem_Qc7Vu itemName_cQEeS">最新测试版本</dt><dd class="basicInfoItem_Qc7Vu itemValue_flBFe"><span class="text_H038s" data-text="true">Python 3.13.0b2</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [13]<em id="sup-13"></em></sup></span></dd></div></dl></div><div class="catalogWrapper_ChTwQ"><div class="catalog_aVRy_"><h2 class="catalogTitle_cm2R3">目录</h2><div class="catalogList_TaGiL column-4_LIoFf"><ol><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">1</span><span class="catalogText_Vwyct"><a href="#1">发展历程</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">2</span><span class="catalogText_Vwyct"><a href="#2">语言特点</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#2-1">优点</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#2-2">缺点</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">3</span><span class="catalogText_Vwyct"><a href="#3">基本语法</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#3-1">控制语句</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#3-2">表达式</a></span></li></ol><ol><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#3-3">函数</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#3-4">对象的方法</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#3-5">数据类型</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#3-6">数学运算</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">4</span><span class="catalogText_Vwyct"><a href="#4">帮助</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">5</span><span class="catalogText_Vwyct"><a href="#5">接口</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#5-1">服务器</a></span></li></ol><ol><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#5-2">程序</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#5-3">环境变量</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">6</span><span class="catalogText_Vwyct"><a href="#6">应用领域</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">7</span><span class="catalogText_Vwyct"><a href="#7">开发工具</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">8</span><span class="catalogText_Vwyct"><a href="#8">标准库</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#8-1">内置库</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#8-2">外部库</a></span></li></ol><ol><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">9</span><span class="catalogText_Vwyct"><a href="#9">开发环境</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#9-1">工具</a></span></li><li class="level2_q53Fw"><span class="catalogIndex_e4ypG">▪</span><span class="catalogText_Vwyct"><a href="#9-2">解释器</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">10</span><span class="catalogText_Vwyct"><a href="#10">著名应用</a></span></li><li class="level1_eWuR7"><span class="catalogIndex_e4ypG">11</span><span class="catalogText_Vwyct"><a href="#11">学习网站</a></span></li></ol></div></div></div><div class="J-lemma-content"><div class="paraTitle_zbAWA level-1_pZduX" data-index="1" data-tag="header" data-uuid="go6t5j6l59" data-level="1"><div class="anchorList_CiJE7"><a name="发展历程"></a><a name="1"></a></div><h2 name="1">发展历程</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="1" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6l72" data-idx="0-1"><span class="text_H038s" data-text="true">自20世纪90年代初Python语言</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E8%AF%9E%E7%94%9F/82555?fromModule=lemma_inlink" target="_blank" data-from-module="">诞生</a></span><span class="text_H038s" data-text="true">至今，它已被逐渐广泛应用于系统管理任务的处理和</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Web/150564?fromModule=lemma_inlink" target="_blank" data-from-module="">Web</a></span><span class="text_H038s" data-text="true">编程。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sI8DfUTi1feQ" data-idx="0-2"><span class="text_H038s" data-text="true">1995年，Guido van Rossum在</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%BC%97%E5%90%89%E5%B0%BC%E4%BA%9A%E5%B7%9E/3439466?fromModule=lemma_inlink" target="_blank" data-from-module="">弗吉尼亚州</a></span><span class="text_H038s" data-text="true">的国家创新研究公司（CNRI）继续他在Python上的工作，并在那里发布了该软件的多个版本。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [1]<em id="sup-1"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sI8DNRXXgD2F" data-idx="0-3"><span class="text_H038s" data-text="true">2000年五月，Guido van Rossum和Python核心开发团队转到BeOpen.com并组建了BeOpen PythonLabs团队。同年十月，BeOpen PythonLabs团队转到Digital Creations（现为Zope Corporation）。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [1]<em id="sup-1"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sI8E4H9Hve4R" data-idx="0-4"><span class="text_H038s" data-text="true">2001年，</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Python%E8%BD%AF%E4%BB%B6%E5%9F%BA%E9%87%91%E4%BC%9A/19301610?fromModule=lemma_inlink" target="_blank" data-from-module="">Python软件基金会</a></span><span class="text_H038s" data-text="true">（PSF）成立，这是一个专为拥有Python相关</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%9F%A5%E8%AF%86%E4%BA%A7%E6%9D%83/85044?fromModule=lemma_inlink" target="_blank" data-from-module="">知识产权</a></span><span class="text_H038s" data-text="true">而创建的非营利组织。Zope Corporation是PSF的赞助成员。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [1]<em id="sup-1"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6l8o" data-idx="0-5"><span class="text_H038s" data-text="true">Python的创始人为荷兰人</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%90%89%E5%A4%9A%C2%B7%E8%8C%83%E7%BD%97%E8%8B%8F%E5%A7%86/328361?fromModule=lemma_inlink" target="_blank" data-from-module="">吉多·范罗苏姆</a></span><span class="text_H038s" data-text="true">（Guido van Rossum）。1989年</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%9C%A3%E8%AF%9E%E8%8A%82/127881?fromModule=lemma_inlink" target="_blank" data-from-module="">圣诞节</a></span><span class="text_H038s" data-text="true">期间，在</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E9%98%BF%E5%A7%86%E6%96%AF%E7%89%B9%E4%B8%B9/2259975?fromModule=lemma_inlink" target="_blank" data-from-module="">阿姆斯特丹</a></span><span class="text_H038s" data-text="true">，Guido为了打发</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%9C%A3%E8%AF%9E%E8%8A%82/127881?fromModule=lemma_inlink" target="_blank" data-from-module="">圣诞节</a></span><span class="text_H038s" data-text="true">的无趣，决心开发一个新的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E8%84%9A%E6%9C%AC/399?fromModule=lemma_inlink" target="_blank" data-from-module="">脚本</a></span><span class="text_H038s" data-text="true">解释程序，作为</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/ABC%E8%AF%AD%E8%A8%80/334996?fromModule=lemma_inlink" target="_blank" data-from-module="">ABC语言</a></span><span class="text_H038s" data-text="true">的一种继承。之所以选中单词Python（意为大蟒蛇）作为该编程语言的名字，是因为英国20世纪70年代首播的电视喜剧《蒙提·派森的飞行马戏团》（Monty Python&#x27;s Flying Circus）。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6la9" data-idx="0-6"><span class="text_H038s" data-text="true">ABC是由Guido参加设计的一种</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%95%99%E5%AD%A6%E8%AF%AD%E8%A8%80/1611713?fromModule=lemma_inlink" target="_blank" data-from-module="">教学语言</a></span><span class="text_H038s" data-text="true">。就Guido本人看来，ABC这种语言非常优美和强大，是专门为非专业程序员设计的。但是ABC语言并没有成功，究其原因，Guido认为是其非开放造成的。Guido决心在Python中避免这一错误。同时，他还想实现在ABC中闪现过但未曾实现的东西。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6lbo" data-idx="0-7"><span class="text_H038s" data-text="true">就这样，Python在Guido手中诞生了。可以说，Python是从ABC发展起来，主要受到了</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Modula-3/17009923?fromModule=lemma_inlink" target="_blank" data-from-module="">Modula-3</a></span><span class="text_H038s" data-text="true">（另一种相当优美且强大的语言，为小型团体所设计的）的影响。并且结合了Unix shell和</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C/7252092?fromModule=lemma_inlink" target="_blank" data-from-module="">C</a></span><span class="text_H038s" data-text="true">的习惯。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6ld9" data-idx="0-8"><span class="text_H038s" data-text="true">Python</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [7]<em id="sup-7"></em></sup></span><span class="text_H038s" data-text="true">已经成为最受欢迎的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%A8%8B%E5%BA%8F%E8%AE%BE%E8%AE%A1%E8%AF%AD%E8%A8%80/2317999?fromModule=lemma_inlink" target="_blank" data-from-module="">程序设计语言</a></span><span class="text_H038s" data-text="true">之一。自从2004年以后，python的使用率呈线性增长。Python 2于2000年10月16日发布，稳定版本是Python 2.7。Python 3于2008年12月3日发布，不完全兼容Python 2。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [6]<em id="sup-6"></em></sup></span><span class="text_H038s" data-text="true">2011年1月，它被TIOBE编程语言</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%8E%92%E8%A1%8C%E6%A6%9C/4895?fromModule=lemma_inlink" target="_blank" data-from-module="">排行榜</a></span><span class="text_H038s" data-text="true">评为2010年度语言。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5j6let" data-idx="0-9"><div class="lemmaPicture_rk0W7 layoutRight_g37lf disable-select" style="float:right;width:220px"><a class="imageLink_MNBEg" style="width:220px;height:118.19607843137256px" href="/pic/Python/407313/0/faedab64034f78f092033e1079310a55b2191ccc?fr=lemma&amp;fromModule=lemma_content-image" target="_blank" title="标识"><img src="https://bkimg.cdn.bcebos.com/pic/faedab64034f78f092033e1079310a55b2191ccc?x-bce-process=image/format,f_auto/resize,m_lfit,limit_1,h_236" class="picture_WQ5sS" width="220" height="118.19607843137256"/></a><span class="titleSpan_k54c3"><span>标识</span></span></div><span class="text_H038s" data-text="true">由于Python语言的简洁性、易读性以及可扩展性，在国外用Python做科学计算的研究机构日益增多，一些知名大学已经采用Python来教授程序设计课程。例如卡耐基梅隆大学的编程基础、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E9%BA%BB%E7%9C%81%E7%90%86%E5%B7%A5%E5%AD%A6%E9%99%A2/117999?fromModule=lemma_inlink" target="_blank" data-from-module="">麻省理工学院</a></span><span class="text_H038s" data-text="true">的计算机科学及编程导论就使用Python语言讲授。众多开源的科学计算软件包都提供了Python的调用接口，例如著名的计算机视觉库OpenCV、三维可视化库VTK、医学图像处理库ITK。而Python专用的科学计算扩展库就更多了，例如如下3个十分经典的科学计算扩展库：NumPy、SciPy和matplotlib，它们分别为Python提供了快速数组处理、数值运算以及绘图功能。因此Python语言及其众多的扩展库所构成的开发环境十分适合工程技术、科研人员处理实验数据、制作图表，甚至开发科学计算</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%BA%94%E7%94%A8%E7%A8%8B%E5%BA%8F/5985445?fromModule=lemma_inlink" target="_blank" data-from-module="">应用程序</a></span><span class="text_H038s" data-text="true">。2018年3月，该语言作者在邮件列表上宣布Python 2.7将于2020年1月1日终止支持。用户如果想要在这个日期之后继续得到与Python 2.7有关的支持，则需要付费给商业供应商。</span></div><div data-tag="module" data-module-type="table" data-uuid="sI8Er6sjXuTH"><div class="moduleTable_SeKUW"><table class="tableBox_p9gKo"><caption>Python版本发展史</caption><tbody class="tableBody_w6yrq"><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6r8JnHr"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rJmIEg" data-idx=""><span class="text_H038s" data-text="true">发布版本</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6r9SeZh"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rvXuxo" data-idx=""><span class="text_H038s" data-text="true">源自</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6r1RFhS"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rukUzM" data-idx=""><span class="text_H038s" data-text="true">年份</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8EMGC357am"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8EMGC67mxf" data-idx=""><span class="text_H038s" data-text="true">所有者</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8ELd7jIHIM"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8ELd7kccGO" data-idx=""><span class="text_H038s" data-text="true">GPL兼容</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6rUdPsP"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rDWHmj" data-idx=""><span class="text_H038s" data-text="true">0.9.0至1.2</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6rZoZGd"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rSHkSf" data-idx=""><span class="text_H038s" data-text="true">n/a</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6rHwVlp"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rQbTrD" data-idx=""><span class="text_H038s" data-text="true">1991-1995</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8EMGCWNERR"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8EMGCZT77h" data-idx=""><span class="text_H038s" data-text="true">CWI</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8ELd8tcZuU"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8ELd8OdUbf" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc9nErt6B"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc9ncWjPR" data-idx=""><span class="text_H038s" data-text="true">1.3至1.5.2</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc9nEVxbI"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc9nisXvh" data-idx=""><span class="text_H038s" data-text="true">1.2</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc9nMCeVQ"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FsWHkvByF" data-idx=""><span class="text_H038s" data-text="true">1995-1999</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc9n4onEc"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc9n7mPOg" data-idx=""><span class="text_H038s" data-text="true">CNRI</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc9ntdaRd"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc9nPJVQy" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc1TEaJdr"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc1ToGwFc" data-idx=""><span class="text_H038s" data-text="true">1.6</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc1TiUGsZ"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc1TFTz7V" data-idx=""><span class="text_H038s" data-text="true">1.5.2</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc1T3Pdpd"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc1TtW8eZ" data-idx=""><span class="text_H038s" data-text="true">2000</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc1TT9gsa"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc1T0ijSj" data-idx=""><span class="text_H038s" data-text="true">CNRI</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fc1Tu0yph"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fc1Tl6Fhb" data-idx=""><span class="text_H038s" data-text="true">否</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbV2I7rxE"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbV2fqjNY" data-idx=""><span class="text_H038s" data-text="true">2.0</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbV2eRVYd"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbV2DcIXI" data-idx=""><span class="text_H038s" data-text="true">1.6</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbV2NA3zv"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbV2SlN2m" data-idx=""><span class="text_H038s" data-text="true">2000</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbV2EpYTP"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FL34jd71F" data-idx=""><span class="text_H038s" data-text="true">BeOpen.com</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbV2k0yuf"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbV24Fwtj" data-idx=""><span class="text_H038s" data-text="true">否</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbLCDUvpz"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbLCfo8Jo" data-idx=""><span class="text_H038s" data-text="true">1.6.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbLCrKekk"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbLChlmhR" data-idx=""><span class="text_H038s" data-text="true">1.6</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbLCnKkvj"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbLCAk9NV" data-idx=""><span class="text_H038s" data-text="true">2001</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbLC9biiC"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbLCdYXiT" data-idx=""><span class="text_H038s" data-text="true">CNRI</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbLCi1qTC"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbLCNKC6e" data-idx=""><span class="text_H038s" data-text="true">否</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbzfoB2Ri"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fbzf2dalD" data-idx=""><span class="text_H038s" data-text="true">2.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fbzft4tiv"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8GaV0RuWiB" data-idx=""><span class="text_H038s" data-text="true">2.0+1.6.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbzfdthmU"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbzfoAtaI" data-idx=""><span class="text_H038s" data-text="true">2001</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fbzf5R5mu"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbzfloO8c" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbzfuEsWc"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbzfLYUrR" data-idx=""><span class="text_H038s" data-text="true">否</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbqMQpyto"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbqMVPNWX" data-idx=""><span class="text_H038s" data-text="true">2.0.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbqMJzTUD"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8GczsVhDqa" data-idx=""><span class="text_H038s" data-text="true">2.0+1.6.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbqMpQQRK"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbqMlgcJo" data-idx=""><span class="text_H038s" data-text="true">2001</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbqMq1Wyz"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbqMGagEM" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbqMyuIxY"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbqMDLC3h" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbiIz4maj"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbiHB6Jyj" data-idx=""><span class="text_H038s" data-text="true">2.1.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbiIwgm8P"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Gyv3OYVgZ" data-idx=""><span class="text_H038s" data-text="true">2.1+2.0.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbiIC7Xiq"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbiIZlMBq" data-idx=""><span class="text_H038s" data-text="true">2001</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbiIwIXzX"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbiIzDniF" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbiIHQALG"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbiIbelMY" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbacTiVvI"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fbac5yx7I" data-idx=""><span class="text_H038s" data-text="true">2.1.2</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbacOWlai"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fbac7WCUJ" data-idx=""><span class="text_H038s" data-text="true">2.1.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Fbac1vpHC"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Fbaclew6E" data-idx=""><span class="text_H038s" data-text="true">2002</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbacrB8MM"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbacuCbHo" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FbacmRLsX"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FbaczrLUI" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FaWob4TZz"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FaWoEinPH" data-idx=""><span class="text_H038s" data-text="true">2.1.3</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FaWoR1wQh"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FaWoEBgzc" data-idx=""><span class="text_H038s" data-text="true">2.1.2</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FaWoy5JWE"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FaWoWosBI" data-idx=""><span class="text_H038s" data-text="true">2002</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FaWocGsrJ"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FaWoj9W6y" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8FaWohkRtw"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8FaWoNNBrR" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6rkOiue"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8GHD3C3kXF" data-idx=""><span class="text_H038s" data-text="true">2.2 至3.0</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6rs2bOB"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rvnntL" data-idx=""><span class="text_H038s" data-text="true">2.1.1</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8Er6rtHbwd"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8Er6rYeOMT" data-idx=""><span class="text_H038s" data-text="true">2001至今</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8EMGCE4Dek"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8EMGCldrzw" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="sI8ELd8HEdWj"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="sI8ELd8wyJAm" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr><tr><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tfOhs8FxUz5N"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tfOhs8FrMbRd" data-idx=""><span class="text_H038s" data-text="true">3.0及更高</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tfOhs8GRLXig"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tfOi2zCXZP2q" data-idx=""><span class="text_H038s" data-text="true">2.6</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tfOhs8G6n1qp"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tfOi2zDQS60S" data-idx=""><span class="text_H038s" data-text="true">2008至今</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tfOhs8GZ2i1z"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tfOi2zDy39kY" data-idx=""><span class="text_H038s" data-text="true">PSF</span></div></td><td width="250" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tfOhs8GBVTVU"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tfOi2zDcwCpv" data-idx=""><span class="text_H038s" data-text="true">是</span></div></td></tr></tbody></table></div></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="2" data-tag="header" data-uuid="go6t5jybc3" data-level="1"><div class="anchorList_CiJE7"><a name="语言特点"></a><a name="2"></a></div><h2 name="2">语言特点</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="2" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="2-1" data-tag="header" data-uuid="thsEIjTByWth" data-level="2"><div class="anchorList_CiJE7"><a name="优点"></a><a name="2-1"></a></div><h3>优点</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkjzpeP" data-idx="1-2"><span class="text_H038s bold_WEzZ7" data-text="true">简单</span><span class="text_H038s" data-text="true">：Python是一种代表</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%AE%80%E5%8D%95%E4%B8%BB%E4%B9%89/6711624?fromModule=lemma_inlink" target="_blank" data-from-module="">简单主义</a></span><span class="text_H038s" data-text="true">思想的语言。阅读一个良好的Python程序就感觉像是在读英语一样。它让使用者能够专注于解决问题而不是去搞明白语言本身。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkjjiP9" data-idx="1-3"><span class="text_H038s bold_WEzZ7" data-text="true">易学</span><span class="text_H038s" data-text="true">：Python极其容易上手，因为Python有极其简单的说明文档</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [8]<em id="sup-8"></em></sup></span><span class="text_H038s" data-text="true">。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpNa67vi3L" data-idx="1-4"><span class="text_H038s bold_WEzZ7" data-text="true">易读、易维护</span><span class="text_H038s" data-text="true">：风格清晰划一、强制缩进</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="thsENlQXEgbS" data-idx="1-5"><span class="text_H038s bold_WEzZ7" data-text="true">用途广泛</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkjeqfv" data-idx="1-6"><span class="text_H038s bold_WEzZ7" data-text="true">速度较快：</span><span class="text_H038s" data-text="true">Python的底层是用C语言写的，很多标准库和第三方库也都是用C写的。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [7]<em id="sup-7"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkr5Bj" data-idx="1-7"><span class="text_H038s bold_WEzZ7" data-text="true">免费、开源</span><span class="text_H038s" data-text="true">：Python是FLOSS（自由/开放源码软件）之一。使用者可以自由地发布这个软件的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%8B%B7%E8%B4%9D/82427?fromModule=lemma_inlink" target="_blank" data-from-module="">拷贝</a></span><span class="text_H038s" data-text="true">、阅读它的源代码、对它做改动、把它的一部分用于新的自由软件中。FLOSS是基于一个团体分享知识的概念。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkXGoq" data-idx="1-8"><span class="text_H038s bold_WEzZ7" data-text="true">高层语言</span><span class="text_H038s" data-text="true">：用Python语言编写程序的时候无需考虑诸如如何管理程序使用的内存一类的底层细节。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkfhXn" data-idx="1-9"><span class="text_H038s bold_WEzZ7" data-text="true">可移植性</span><span class="text_H038s" data-text="true">：由于它的开源本质，Python已经被移植在许多平台上（经过改动使它能够工作在不同平台上）。这些平台包括</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Linux/27050?fromModule=lemma_inlink" target="_blank" data-from-module="">Linux</a></span><span class="text_H038s" data-text="true">、Windows、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/FreeBSD/413712?fromModule=lemma_inlink" target="_blank" data-from-module="">FreeBSD</a></span><span class="text_H038s" data-text="true">、Macintosh、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Solaris/3517?fromModule=lemma_inlink" target="_blank" data-from-module="">Solaris</a></span><span class="text_H038s" data-text="true">、OS/2、Amiga、AROS、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/AS%2F400/10953231?fromModule=lemma_inlink" target="_blank" data-from-module="">AS/400</a></span><span class="text_H038s" data-text="true">、BeOS、OS/390、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/z%2FOS/9064509?fromModule=lemma_inlink" target="_blank" data-from-module="">z/OS</a></span><span class="text_H038s" data-text="true">、Palm OS、QNX、VMS、Psion、Acom RISC OS、VxWorks、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/PlayStation/469344?fromModule=lemma_inlink" target="_blank" data-from-module="">PlayStation</a></span><span class="text_H038s" data-text="true">、Sharp Zaurus、Windows CE、PocketPC、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Symbian/264891?fromModule=lemma_inlink" target="_blank" data-from-module="">Symbian</a></span><span class="text_H038s" data-text="true">以及Google基于linux开发的android平台。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkExBG" data-idx="1-10"><span class="text_H038s bold_WEzZ7" data-text="true">解释性</span><span class="text_H038s" data-text="true">：一个用编译性语言比如C或C++写的程序可以从源文件（即C或C++语言）转换到一个计算机使用的语言（二进制代码，即0和1）。这个过程通过编译器和不同的标记、选项完成。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkKhp1" data-idx="1-11"><span class="text_H038s" data-text="true">运行程序的时候，连接/转载器软件把程序从硬盘复制到内存中并且运行。而Python语言写的程序不需要编译成</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E4%BA%8C%E8%BF%9B%E5%88%B6%E4%BB%A3%E7%A0%81/4879654?fromModule=lemma_inlink" target="_blank" data-from-module="">二进制代码</a></span><span class="text_H038s" data-text="true">。使用者可以直接从源代码运行程序。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkKp4V" data-idx="1-12"><span class="text_H038s" data-text="true">在计算机内部，Python解释器把源代码转换成称为字节码的中间形式，然后再把它翻译成计算机使用的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%9C%BA%E5%99%A8%E8%AF%AD%E8%A8%80/2019225?fromModule=lemma_inlink" target="_blank" data-from-module="">机器语言</a></span><span class="text_H038s" data-text="true">并运行。这使得使用Python更加简单。也使得Python程序更加易于移植。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkk67AX" data-idx="1-13"><span class="text_H038s bold_WEzZ7" data-text="true">面向对象</span><span class="text_H038s" data-text="true">：Python既支持面向过程的编程也支持面向对象的编程。在“面向过程”的语言中，程序是由过程或仅仅是可重用代码的函数构建起来的。在“面向对象”的语言中，程序是由数据和功能组合而成的对象构建起来的。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpQg7M79kc" data-idx="1-14"><span class="text_H038s" data-text="true">Python是完全</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E9%9D%A2%E5%90%91%E5%AF%B9%E8%B1%A1/2262089?fromModule=lemma_inlink" target="_blank" data-from-module="">面向对象</a></span><span class="text_H038s" data-text="true">的语言。</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%87%BD%E6%95%B0/18686609?fromModule=lemma_inlink" target="_blank" data-from-module="">函数</a></span><span class="text_H038s" data-text="true">、模块、数字、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%AD%97%E7%AC%A6%E4%B8%B2/1017763?fromModule=lemma_inlink" target="_blank" data-from-module="">字符串</a></span><span class="text_H038s" data-text="true">都是对象。并且完全支持继承、重载、派生、多继承，有益于增强源代码的复用性。Python支持重载运算符和动态类型。相对于</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Lisp/22083?fromModule=lemma_inlink" target="_blank" data-from-module="">Lisp</a></span><span class="text_H038s" data-text="true">这种传统的函数式编程语言，Python对函数式设计只提供了有限的支持。有两个标准库（functools，itertools）提供了Haskell和Standard ML中久经考验的函数式程序设计工具。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkq6To" data-idx="1-15"><span class="text_H038s bold_WEzZ7" data-text="true">可扩展性、可扩充性</span><span class="text_H038s" data-text="true">：如果需要一段关键代码运行得更快或者希望某些算法不公开，可以部分程序用C或C++编写，然后在Python程序中使用它们。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpQQsHGIXK" data-idx="1-16"><span class="text_H038s" data-text="true">Python本身被设计为可扩充的。并非所有的特性和功能都集成到语言核心。Python提供了丰富的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/API/10154?fromModule=lemma_inlink" target="_blank" data-from-module="">API</a></span><span class="text_H038s" data-text="true">和工具，以便程序员能够轻松地使用</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C%E8%AF%AD%E8%A8%80/105958?fromModule=lemma_inlink" target="_blank" data-from-module="">C语言</a></span><span class="text_H038s" data-text="true">、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C%2B%2B/99272?fromModule=lemma_inlink" target="_blank" data-from-module="">C++</a></span><span class="text_H038s" data-text="true">、Cython来编写扩充模块。Python编译器本身也可以被集成到其它需要脚本语言的程序内。因此，很多人还把Python作为一种“胶水语言”（glue language）使用。使用Python将其他语言编写的程序进行集成和封装。在Google内部的很多项目，例如Google Engine使用C++编写性能要求极高的部分，然后用Python或Java/Go调用相应的模块。《Python技术手册》的作者马特利（Alex Martelli）说：“这很难讲，不过，2004年，Python已在Google内部使用，Google 召募许多 Python 高手，但在这之前就已决定使用Python，他们的目的是 Python where we can，C++ where we must，在操控硬件的场合使用</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C%2B%2B/99272?fromModule=lemma_inlink" target="_blank" data-from-module="">C++</a></span><span class="text_H038s" data-text="true">，在快速开发时候使用Python。”</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkUbm1" data-idx="1-17"><span class="text_H038s bold_WEzZ7" data-text="true">可嵌入性</span><span class="text_H038s" data-text="true">：可以把Python嵌入</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C/7252092?fromModule=lemma_inlink" target="_blank" data-from-module="">C</a></span><span class="text_H038s" data-text="true">/</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C%2B%2B/99272?fromModule=lemma_inlink" target="_blank" data-from-module="">C++</a></span><span class="text_H038s" data-text="true">程序，从而向程序用户提供脚本功能。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkIgSl" data-idx="1-18"><span class="text_H038s bold_WEzZ7" data-text="true">丰富的库</span><span class="text_H038s" data-text="true">：Python标准库确实很庞大。它可以帮助处理各种工作，包括正则表达式、文档生成、单元测试、线程、数据库、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%BD%91%E9%A1%B5%E6%B5%8F%E8%A7%88%E5%99%A8/8309940?fromModule=lemma_inlink" target="_blank" data-from-module="">网页浏览器</a></span><span class="text_H038s" data-text="true">、CGI、FTP、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%94%B5%E5%AD%90%E9%82%AE%E4%BB%B6/111106?fromModule=lemma_inlink" target="_blank" data-from-module="">电子邮件</a></span><span class="text_H038s" data-text="true">、XML、XML-RPC、HTML、WAV文件、密码系统、GUI（图形用户界面）、Tk和其他与系统有关的操作。这被称作Python的“功能齐全”理念。除了标准库以外，还有许多其他高质量的库，如wxPython、Twisted和Python图像库等等。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkHE0b" data-idx="1-19"><span class="text_H038s bold_WEzZ7" data-text="true">规范的代码：</span><span class="text_H038s" data-text="true">Python采用强制缩进的方式使得代码具有较好可读性。而Python语言写的程序不需要编译成二进制代码。Python的作者设计限制性很强的语法，使得不好的编程习惯（例如if语句的下一行不向右缩进）都不能通过编译。其中很重要的一项就是Python的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%BC%A9%E8%BF%9B/7337492?fromModule=lemma_inlink" target="_blank" data-from-module="">缩进</a></span><span class="text_H038s" data-text="true">规则。一个和其他大多数语言（如C）的区别就是，一个模块的界限，完全是由每行的首字符在这一行的位置来决定（而C语言是用一对大括号来明确的定出模块的边界，与字符的位置毫无关系）。通过强制程序员们缩进（包括if，for和函数定义等所有需要使用模块的地方），Python确实使得程序更加清晰和美观。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpQAyh5aFy" data-idx="1-20"><span class="text_H038s bold_WEzZ7" data-text="true">高级动态编程：</span><span class="text_H038s" data-text="true">虽然Python可能被粗略地分类为“脚本语言”（script language），但实际上一些大规模软件开发计划例如Zope、Mnet及</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/BitTorrent/142795?fromModule=lemma_inlink" target="_blank" data-from-module="">BitTorrent</a></span><span class="text_H038s" data-text="true">，</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Google/86964?fromModule=lemma_inlink" target="_blank" data-from-module="">Google</a></span><span class="text_H038s" data-text="true">也广泛地使用它。Python的支持者较喜欢称它为一种高级动态编程语言，原因是“脚本语言”泛指仅作简单程序设计任务的语言，如shellscript、VBScript等只能处理简单任务的编程语言，并不能与Python相提并论。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpMKUnRpdq" data-idx="1-21"><span class="text_H038s bold_WEzZ7" data-text="true">做科学计算优点多：</span><span class="text_H038s" data-text="true">说起科学计算，首先会被提到的可能是MATLAB。除了MATLAB的一些专业性很强的工具箱还无法被替代之外，</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/MATLAB/263035?fromModule=lemma_inlink" target="_blank" data-from-module="">MATLAB</a></span><span class="text_H038s" data-text="true">的大部分常用功能都可以在Python世界中找到相应的扩展库。和MATLAB相比，用Python做科学计算有如下优点：</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpMKUnCKqH" data-idx="1-22"><span class="text_H038s" data-text="true">●首先，</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/MATLAB/263035?fromModule=lemma_inlink" target="_blank" data-from-module="">MATLAB</a></span><span class="text_H038s" data-text="true">是一款商用软件，并且价格不菲。而Python完全免费，众多开源的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%A7%91%E5%AD%A6/10406?fromModule=lemma_inlink" target="_blank" data-from-module="">科学</a></span><span class="text_H038s" data-text="true">计算库都提供了Python的调用接口。用户可以在任何计算机上免费安装Python及其绝大多数扩展库。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpMKUnTJEF" data-idx="1-23"><span class="text_H038s" data-text="true">●其次，与</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/MATLAB/263035?fromModule=lemma_inlink" target="_blank" data-from-module="">MATLAB</a></span><span class="text_H038s" data-text="true">相比，Python是一门更易学、更严谨的程序设计语言。它能让用户编写出更易读、易维护的代码。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpMKUnMDYL" data-idx="1-24"><span class="text_H038s" data-text="true">●最后，</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/MATLAB/263035?fromModule=lemma_inlink" target="_blank" data-from-module="">MATLAB</a></span><span class="text_H038s" data-text="true">主要专注于工程和科学计算。然而即使在计算领域，也经常会遇到文件管理、界面设计、网络通信等各种需求。而Python有着丰富的扩展库，可以轻易完成各种高级任务，开发者可以用Python实现完整应用程序所需的各种功能。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="2-2" data-tag="header" data-uuid="thsEGk5ttKTG" data-level="2"><div class="anchorList_CiJE7"><a name="缺点"></a><a name="2-2"></a></div><h3>缺点</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkk7Hsu" data-idx="1-26"><span class="text_H038s bold_WEzZ7" data-text="true">单行语句和命令行输出问题</span><span class="text_H038s" data-text="true">：很多时候不能将程序连写成一行，如import sys；for i in sys.path：print i。而perl和awk就无此限制，可以较为方便的在shell下完成简单程序，不需要如Python一样，必须将程序写入一个.py文件。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkDzuy" data-idx="1-27"><span class="text_H038s bold_WEzZ7" data-text="true">给初学者带来困惑：</span><span class="text_H038s" data-text="true">独特的语法</span><span class="text_H038s bold_WEzZ7" data-text="true">，</span><span class="text_H038s" data-text="true">这也许不应该被称为局限，但是它用缩进来区分语句关系的方式还是给很多初学者带来了困惑。即便是很有经验的Python程序员，也可能陷入陷阱当中。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpKgkkPQHr" data-idx="1-28"><span class="text_H038s bold_WEzZ7" data-text="true">运行速度慢</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [15<svg viewBox="0 0 12 12" fill="currentColor" width="1em" height="1em"><path d="M11.021 12H.979A.98.98 0 0 1 0 11.021V.979A.98.98 0 0 1 .979 0H11.02a.98.98 0 0 1 .98.979v10.042a.98.98 0 0 1-.979.979zM11 .979.979 1 1 11.021 11 11V.979z"></path><circle cx="4.235" cy="3.655" r="1.1"></circle><path d="M9.537 5.773a.5.5 0 0 0-.707 0L6.667 7.938l-1.66-1.659a.491.491 0 0 0-.391-.139.494.494 0 0 0-.391.139L2.389 8.115a.5.5 0 0 0 .707.707l1.521-1.521L6.315 9a.498.498 0 0 0 .707 0l.008-.012L9.537 6.48a.5.5 0 0 0 0-.707z"></path></svg>]<em id="sup-15"></em></sup></span><span class="text_H038s" data-text="true">：这里是指与C和C++相比。Python开发人员尽量避开不成熟或者不重要的优化。一些针对非重要部位的加快运行速度的补丁通常不会被合并到Python内。所以很多人认为Python很慢。不过，根据</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E4%BA%8C%E5%85%AB%E5%AE%9A%E5%BE%8B/747076?fromModule=lemma_inlink" target="_blank" data-from-module="">二八定律</a></span><span class="text_H038s" data-text="true">，大多数程序对速度要求不高。在某些对运行速度要求很高的情况，Python设计师倾向于使用JIT技术，或者用使用</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C/7252092?fromModule=lemma_inlink" target="_blank" data-from-module="">C</a></span><span class="text_H038s" data-text="true">/</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/C%2B%2B/99272?fromModule=lemma_inlink" target="_blank" data-from-module="">C++</a></span><span class="text_H038s" data-text="true">语言改写这部分程序。可用的JIT技术是</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/PyPy/9780733?fromModule=lemma_inlink" target="_blank" data-from-module="">PyPy</a></span><span class="text_H038s" data-text="true">。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sNZdi3vkFB2w" data-idx="1-29"><span class="text_H038s bold_WEzZ7" data-text="true">和其他语言区别</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpO7zrSl79" data-idx="1-30"><span class="text_H038s bold_WEzZ7" data-text="true">对于一个特定的问题，只要有一种最好的方法来解决</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpONE7JEp6" data-idx="1-31"><span class="text_H038s" data-text="true">这在由Tim Peters写的Python格言（称为The Zen of Python）里面表述为：There should be one-and preferably only one-obvious way to do it。这正好和Perl语言（另一种功能类似的高级动态语言）的中心思想TMTOWTDI（There&#x27;s More Than One Way To Do It）完全相反。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpOH24FaLV" data-idx="1-32"><span class="text_H038s" data-text="true">Python的设计哲学是“优雅”、“明确”、“简单”。因此，</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Perl%E8%AF%AD%E8%A8%80/1346108?fromModule=lemma_inlink" target="_blank" data-from-module="">Perl语言</a></span><span class="text_H038s" data-text="true">中“总是有多种方法来做同一件事”的理念在Python开发者中通常是难以忍受的。Python开发者的哲学是“用一种方法，最好是只有一种方法来做一件事”。在设计Python语言时，如果面临多种选择，Python开发者一般会拒绝花俏的语法，而选择明确的没有或者很少有歧义的语法。由于这种设计观念的差异，Python源代码通常被认为比Perl具备更好的可读性，并且能够支撑大规模的软件开发。这些准则被称为Python格言。在Python解释器内运行import this可以获得完整的列表。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpRgX7DoXQ" data-idx="1-33"><span class="text_H038s bold_WEzZ7" data-text="true">更高级的Virtual Machine</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="2LpRjSgjN0d" data-idx="1-34"><span class="text_H038s" data-text="true">Python在执行时，首先会将.py文件中的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%BA%90%E4%BB%A3%E7%A0%81/3969?fromModule=lemma_inlink" target="_blank" data-from-module="">源代码</a></span><span class="text_H038s" data-text="true">编译成Python的byte code（字节码），然后再由Python Virtual Machine（Python虚拟机）来执行这些编译好的byte code。这种机制的基本思想跟Java，.NET是一致的。然而，Python Virtual Machine与Java或.NET的Virtual Machine不同的是，Python的Virtual Machine是一种更高级的Virtual Machine。这里的高级并不是通常意义上的高级，不是说Python的Virtual Machine比Java或.NET的功能更强大，而是说和Java 或.NET相比，Python的Virtual Machine距离真实机器的距离更远。或者可以这么说，Python的Virtual Machine是一种抽象层次更高的Virtual Machine。基于C的Python编译出的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%AD%97%E8%8A%82%E7%A0%81/9953683?fromModule=lemma_inlink" target="_blank" data-from-module="">字节码</a></span><span class="text_H038s" data-text="true">文件，通常是.pyc格式。除此之外，Python还可以以交互模式运行，比如主流操作系统Unix/</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Linux/27050?fromModule=lemma_inlink" target="_blank" data-from-module="">Linux</a></span><span class="text_H038s" data-text="true">、Mac、Windows都可以直接在命令模式下直接运行Python交互环境。直接下达操作指令即可实现交互操作。</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="3" data-tag="header" data-uuid="go6szw3y7h" data-level="1"><div class="anchorList_CiJE7"><a name="基本语法"></a><a name="3"></a></div><h2 name="3">基本语法</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="3" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rb74" data-idx="2-1"><div class="lemmaPicture_rk0W7 layoutRight_g37lf disable-select" style="float:right;width:220px"><a class="imageLink_MNBEg" style="width:220px;height:119.16666666666667px" href="/pic/Python/407313/0/8b82b9014a90f6032aaa09763512b31bb051eded?fr=lemma&amp;fromModule=lemma_content-image" target="_blank" title="Python"><img src="https://bkimg.cdn.bcebos.com/pic/8b82b9014a90f6032aaa09763512b31bb051eded?x-bce-process=image/format,f_auto/resize,m_lfit,limit_1,h_238" class="picture_WQ5sS" width="220" height="119.16666666666667"/></a><span class="titleSpan_k54c3"><span>Python</span></span></div><span class="text_H038s" data-text="true">Python的设计目标之一是让代码具备高度的可阅读性。它设计时尽量使用其它语言经常使用的标点符号和英文单字，让代码看起来整洁美观。它不像其他的静态语言如C、Pascal那样需要重复书写声明语句，也不像它们的语法那样经常有特殊情况和意外。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvv24" data-idx="2-2"><span class="text_H038s" data-text="true">Python开发者有意让违反了</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%BC%A9%E8%BF%9B/7337492?fromModule=lemma_inlink" target="_blank" data-from-module="">缩进</a></span><span class="text_H038s" data-text="true">规则的程序不能通过编译，以此来强制程序员养成良好的编程习惯。并且Python语言利用缩进表示语句块的开始和退出（Off-side规则），而非使用花括号或者某种关键字。增加缩进表示语句块的开始，而减少缩进则表示语句块的退出。缩进成为了语法的一部分。例如if语句：python3</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zj1y7"><div class="codeWraper_JOX7k">age = int(input(&quot;请输入你的年龄: &quot;))
if age &lt; 21:
  print(&quot;你不能买酒。&quot;)
  print(&quot;不过你能买口香糖。&quot;)
print(&quot;这句话在if语句块的外面。&quot;)</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;age = int(input(\&quot;请输入你的年龄: \&quot;))\nif age &lt; 21:\n  print(\&quot;你不能买酒。\&quot;)\n  print(\&quot;不过你能买口香糖。\&quot;)\nprint(\&quot;这句话在if语句块的外面。\&quot;)&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvv7f" data-idx="2-4"><span class="text_H038s" data-text="true">根据PEP的规定，必须使用4个空格来表示每级缩进。使用Tab字符和其它数目的空格虽然都可以编译通过，但不符合编码规范。支持Tab字符和其它数目的空格仅仅是为兼容很旧的的Python程序和某些有问题的编辑程序。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="3-1" data-tag="header" data-uuid="go6t0jj050" data-level="2"><div class="anchorList_CiJE7"><a name="控制语句"></a><a name="3-1"></a></div><h3>控制语句</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvau" data-idx="2-6"><span class="text_H038s bold_WEzZ7" data-text="true">if语句</span><span class="text_H038s" data-text="true">，当条件成立时运行语句块。经常与else，elif（相当于else if）配合使用，称为if-elif-else语句。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvck" data-idx="2-7"><span class="text_H038s bold_WEzZ7" data-text="true">for语句</span><span class="text_H038s" data-text="true">，遍历列表、字符串、字典、集合等</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E8%BF%AD%E4%BB%A3%E5%99%A8/3803342?fromModule=lemma_inlink" target="_blank" data-from-module="">迭代器</a></span><span class="text_H038s" data-text="true">（容器），依次处理迭代器中的每个元素。有时和else连用，称为for-else语句。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvdy" data-idx="2-8"><span class="text_H038s bold_WEzZ7" data-text="true">while语句</span><span class="text_H038s" data-text="true">，当条件为真时，循环运行语句块。有时和else配合使用，称为while-else语句。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvfo" data-idx="2-9"><span class="text_H038s bold_WEzZ7" data-text="true">try语句</span><span class="text_H038s" data-text="true">，必与except配合使用处理在程序运行中出现的异常情况，称为try-except语句。常与else，finally配合使用，称为try-except-else语句，try-except-finally语句，try-except-else-finally语句</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvhe" data-idx="2-10"><span class="text_H038s bold_WEzZ7" data-text="true">class语句</span><span class="text_H038s" data-text="true">，用于定义</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%B1%BB%E5%9E%8B/6737759?fromModule=lemma_inlink" target="_blank" data-from-module="">类型</a></span><span class="text_H038s" data-text="true">。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvj3" data-idx="2-11"><span class="text_H038s bold_WEzZ7" data-text="true">def语句</span><span class="text_H038s" data-text="true">，用于定义函数和类型的方法。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvks" data-idx="2-12"><span class="text_H038s bold_WEzZ7" data-text="true">pass语句</span><span class="text_H038s" data-text="true">，表示此行为空，不运行任何操作。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvmi" data-idx="2-13"><span class="text_H038s bold_WEzZ7" data-text="true">assert语句</span><span class="text_H038s" data-text="true">，用于程序调试阶段时测试运行条件是否满足。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvo7" data-idx="2-14"><span class="text_H038s bold_WEzZ7" data-text="true">with语句</span><span class="text_H038s" data-text="true">，Python2.6以后定义的语法，在一个场景中运行语句块。比如，运行语句块前加密，然后在语句块运行退出后解密。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvpx" data-idx="2-15"><span class="text_H038s bold_WEzZ7" data-text="true">yield语句</span><span class="text_H038s" data-text="true">，在迭代器函数内使用，用于返回一个元素。自从Python 2.5版本以后。这个语句变成一个运算符。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvrt" data-idx="2-16"><span class="text_H038s bold_WEzZ7" data-text="true">raise语句</span><span class="text_H038s" data-text="true">，制造一个错误。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvth" data-idx="2-17"><span class="text_H038s bold_WEzZ7" data-text="true">import语句</span><span class="text_H038s" data-text="true">，导入一个模块或包。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvv2" data-idx="2-18"><span class="text_H038s bold_WEzZ7" data-text="true">from…import语句</span><span class="text_H038s" data-text="true">，从包导入模块或从模块导入某个对象。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvvr" data-idx="2-19"><span class="text_H038s bold_WEzZ7" data-text="true">import…as语句</span><span class="text_H038s" data-text="true">，将导入的对象赋值给一个变量。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvvxc" data-idx="2-20"><span class="text_H038s bold_WEzZ7" data-text="true">in语句</span><span class="text_H038s" data-text="true">，判断一个对象是否在一个字符串/列表/元组里。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="3-2" data-tag="header" data-uuid="go6syyvvyx" data-level="2"><div class="anchorList_CiJE7"><a name="表达式"></a><a name="3-2"></a></div><h3>表达式</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvw0i" data-idx="2-22"><span class="text_H038s" data-text="true">Python的表达式写法与C、C++类似。只是在某些写法有所差别。</span></div><ul data-tag="list" data-uuid="tOcDMueQLSJC" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcDMueW03BW" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">算数运算符</span></div></li></ul><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t4zj20r" data-idx="2-24"><span class="text_H038s" data-text="true">Python主要的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%AE%97%E6%9C%AF%E8%BF%90%E7%AE%97%E7%AC%A6/9324947?fromModule=lemma_inlink" target="_blank" data-from-module="">算术运算符</a></span><span class="text_H038s" data-text="true">与C、C++类似。</span></div><div data-tag="module" data-module-type="table" data-uuid="tOcDoRk6em6k"><div class="moduleTable_SeKUW"><table class="tableBox_p9gKo"><tbody class="tableBody_w6yrq"><tr><th width="0" align="center" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRkmmY1F" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">算术运算符</span></div></th><th width="0" align="center" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRkIuK9G" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">作用</span></div></th></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRkKYjzg"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRkSRHVN" data-idx=""><span class="text_H038s" data-text="true">+</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRkqZokm"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRktJngC" data-idx=""><span class="text_H038s" data-text="true">加法或取正</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRkQEHJK"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRkI5DGi" data-idx=""><span class="text_H038s" data-text="true">-</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRk7GAVE"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRk8AbVz" data-idx=""><span class="text_H038s" data-text="true">减法或取负</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRk1CNZk"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRk3JwfG" data-idx=""><span class="text_H038s" data-text="true">*</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRk6pKIh"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRkIs6Ne" data-idx=""><span class="text_H038s" data-text="true">乘法</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcE72o26Asm"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcE72ocFrNH" data-idx=""><span class="text_H038s" data-text="true">/</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcE72oTzBPS"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcE72o68pkD" data-idx=""><span class="text_H038s" data-text="true">除法</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcE6ZCnm8YC"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcE6ZBJZpSr" data-idx=""><span class="text_H038s" data-text="true">//</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcE6ZCvTRzj"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcE6ZCIftyL" data-idx=""><span class="text_H038s" data-text="true">整除</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcE6WLW1noJ"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcE6WKaiVnf" data-idx=""><span class="text_H038s" data-text="true">**</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcE6WLONt5N"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcE6WLFHu5x" data-idx=""><span class="text_H038s" data-text="true">乘方</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRkDrxqk"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRkkpUtx" data-idx=""><span class="text_H038s" data-text="true">~</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcDoRkEKFqF"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcDoRk8l4zu" data-idx=""><span class="text_H038s" data-text="true">取补</span></div></td></tr><tr><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcEbeefIOUZ"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcEbeeZWCfZ" data-idx=""><span class="text_H038s" data-text="true">%</span></div></td><td width="0" align="center" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="tOcEbeeivBGc"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="tOcEbeeEUkMl" data-idx=""><span class="text_H038s" data-text="true">取余</span></div></td></tr></tbody></table></div></div><ul data-tag="list" data-uuid="tOcEBxqobC5t" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcEBxqZHtVH" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">逻辑运算符</span></div></li></ul><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tOcEAvoBPBiE" data-idx="2-27"><span class="text_H038s" data-text="true">Python使用and，or，not表示逻辑运算。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvw5b" data-idx="2-28"><span class="text_H038s" data-text="true">is，is not用于比较两个变量是否是同一个对象。in，not in用于判断一个对象是否属于另外一个对象。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvw6y" data-idx="2-29"><span class="text_H038s" data-text="true">Python支持“列表推导式”（list comprehension），比如计算0-9的平方和：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zj22i"><div class="codeWraper_JOX7k">&gt;&gt;&gt; sum(x * x for x in range(10))
285</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;&gt;&gt;&gt; sum(x * x for x in range(10))\n285&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><ul data-tag="list" data-uuid="tOcEGbkeKP4x" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcEGbkOHgIU" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">匿名函数</span></div></li></ul><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tOcEFgxOk2uW" data-idx="2-32"><span class="text_H038s" data-text="true">Python使用</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/lambda/58231763?fromModule=lemma_inlink" target="_blank" data-from-module="">lambda</a></span><span class="text_H038s" data-text="true">表示匿名函数。匿名函数体只能是表达式。比如：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zj245"><div class="codeWraper_JOX7k">&gt;&gt;&gt; add=lambda x, y : x + y
&gt;&gt;&gt; add(3,2)
5</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;&gt;&gt;&gt; add=lambda x, y : x + y\n&gt;&gt;&gt; add(3,2)\n5&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><ul data-tag="list" data-uuid="tOcF6wh62rbN" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcF6wh7mLQa" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">条件表达式</span></div></li></ul><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tOcEW4GkcayX" data-idx="2-35"><span class="text_H038s" data-text="true">Python使用y if cond else x表示条件表达式。意思是当cond为真时，表达式的值为y，否则表达式的值为x。相当于C++和Java里的cond?y：x。</span></div><ul data-tag="list" data-uuid="tOcFaN6nITsk" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcFaN65EsFG" data-idx=""><span class="text_H038s bold_WEzZ7" data-text="true">数据语法</span></div></li></ul><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tOcF7ZYushZd" data-idx="2-37"><span class="text_H038s" data-text="true">Python区分列表（list）和元组（tuple）两种类型。list的写法是[1，2，3]，而tuple的写法是（1，2，3）。可以改变list中的元素，而不能改变tuple。在某些情况下，tuple的括号可以省略。tuple对于赋值语句有特殊的处理。因此，可以同时赋值给多个变量，比如：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zj25u"><div class="codeWraper_JOX7k">&gt;&gt;&gt; x, y=1,2 # 同时给x,y赋值，最终结果：x=1, y=2</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;&gt;&gt;&gt; x, y=1,2 # 同时给x,y赋值，最终结果：x=1, y=2&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvwi5" data-idx="2-39"><span class="text_H038s" data-text="true">特别地，可以使用以下这种形式来交换两个变量的值：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zj27h"><div class="codeWraper_JOX7k">&gt;&gt;&gt; x, y=y, x #最终结果：y=1, x=2</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;&gt;&gt;&gt; x, y=y, x #最终结果：y=1, x=2&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tOcFwiaag5DC" data-idx="2-41"><span class="text_H038s" data-text="true">Python使用&#x27;（单引号）和&quot;（双引号）来表示单行字符串，用&#x27;&#x27;&#x27;（三个连续单引号）和&quot;&quot;&quot;（三个连续双引号）与Perl、Unix Shell语言或者Ruby、Groovy等语言不一样，两种符号作用相同。一般地，如果字符串中出现了双引号，就使用单引号来表示字符串；反之则使用双引号。如果都没有出现，就依个人喜好选择。出现在字符串中的\（反斜杠）被解释为特殊字符，比如\n表示换行符。表达式前加r指示Python不解释字符串中出现的\。这种写法通常用于编写正则表达式或者Windows文件路径。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvwmy" data-idx="2-42"><span class="text_H038s" data-text="true">Python支持列表切割（list slices），可以取得完整列表的一部分。支持切割操作的类型有str，bytes，list，tuple等。它的语法是...[left:right]或者...[left:right:stride]。假定nums变量的值是[1,3,5,7,8,13,20]，那么下面几个语句为真：</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvwnh" data-idx="2-43"><span class="text_H038s" data-text="true">nums[2:5]==[5,7,8]从下标为2的元素切割到下标为5的元素，但不包含下标为5的元素。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t0iymhh" data-idx="2-44"><span class="text_H038s" data-text="true">nums[1:]==[3,5,7,8,13,20]切割到最后一个元素。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvwqo" data-idx="2-45"><span class="text_H038s" data-text="true">nums[:-3]==[1,3,5,7] 从最开始的元素一直切割到倒数第3个元素。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t0iymj8" data-idx="2-46"><span class="text_H038s" data-text="true">nums[:]==[1,3,5,7,8,13,20]返回所有元素。改变新的列表不会影响到nums。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t42waoj" data-idx="2-47"><span class="text_H038s" data-text="true">nums[1:5:2]==[3,7]从下标为1的元素切割到下标为5的元素，且步长为2。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="3-3" data-tag="header" data-uuid="go6syyvwvf" data-level="2"><div class="anchorList_CiJE7"><a name="函数"></a><a name="3-3"></a></div><h3>函数</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvwx0" data-idx="2-49"><span class="text_H038s" data-text="true">Python函数支持递归、默认参数值、可变参数，但不支持函数重载。为了增强代码的可读性，可以在函数后书写“文档字符串”（Documentation Strings，或者简称docstrings），用于解释函数的作用、参数的类型与意义、返回值类型与取值范围等。可以使用内置函数help打印出函数的使用帮助。比如：</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tCZrr1wPV0vJ" data-idx="2-50"><span class="text_H038s" data-text="true">def randint(a,b):</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t4zj2ag" data-idx="2-51"><span class="text_H038s" data-text="true">... &quot;Return random integer in range [a,b]，including both end points.&quot;</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tCZrBgwz52Xf" data-idx="2-52"><span class="text_H038s" data-text="true">help(randint)</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t06ce4d" data-idx="2-53"><span class="text_H038s" data-text="true">Help on function randint in module __main__：</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t06ce5y" data-idx="2-54"><span class="text_H038s" data-text="true">randint(a, b)</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5qh8w8" data-idx="2-55"><span class="text_H038s" data-text="true">Return random integer inrange[a，b]，including both end points.</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="3-4" data-tag="header" data-uuid="go6syyvwyv" data-level="2"><div class="anchorList_CiJE7"><a name="对象的方法"></a><a name="3-4"></a></div><h3>对象的方法</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvx0g" data-idx="2-57"><span class="text_H038s" data-text="true">对象的方法是指绑定到对象的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%87%BD%E6%95%B0/301912?fromModule=lemma_inlink" target="_blank" data-from-module="">函数</a></span><span class="text_H038s" data-text="true">。调用对象方法的语法是instance.method（arguments）。它等价于调用Class.method（instance，arguments）。当定义对象方法时，必须显式地定义第一个参数，一般该参数名都使用self，用于访问对象的内部数据。这里的self相当于C++，Java里面的this变量，但是还可以使用任何其它合法的参数名，比如this 和 mine 等，self与C++，Java里面的this不完全一样，它可以被看作是一个习惯性的用法，传入任何其它的合法名称都行，比如：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zj2dw"><div class="codeWraper_JOX7k">class Fish:
	def eat(self,food):
		if food is not None:
        	self.hungry=False

class User:
      def __init__(myself,name):
          myself.name=name

#构造Fish的实例：
f=Fish()
#以下两种调用形式是等价的：
Fish.eat(f,&quot;earthworm&quot;)
f.eat(&quot;earthworm&quot;)
u=User(&#x27;username&#x27;)
print(u.name)</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;class Fish:\n\tdef eat(self,food):\n\t\tif food is not None:\n        \tself.hungry=False\n\nclass User:\n      def __init__(myself,name):\n          myself.name=name\n\n#构造Fish的实例：\nf=Fish()\n#以下两种调用形式是等价的：\nFish.eat(f,\&quot;earthworm\&quot;)\nf.eat(\&quot;earthworm\&quot;)\nu=User(&#x27;username&#x27;)\nprint(u.name)&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvx3m" data-idx="2-59"><span class="text_H038s" data-text="true">Python认识一些以“__”开始并以“__”结束的特殊方法名，它们用于实现</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E8%BF%90%E7%AE%97%E7%AC%A6%E9%87%8D%E8%BD%BD/2109844?fromModule=lemma_inlink" target="_blank" data-from-module="">运算符重载</a></span><span class="text_H038s" data-text="true">和实现多种特殊功能，叫做魔法方法。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="3-5" data-tag="header" data-uuid="go6syyvx58" data-level="2"><div class="anchorList_CiJE7"><a name="数据类型"></a><a name="3-5"></a></div><h3>数据类型</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvx6u" data-idx="2-61"><span class="text_H038s" data-text="true">Python采用动态类型系统。在编译的时候，Python不会检查对象是否拥有被调用的方法或者属性，而是直至运行时，才做出检查。所以操作对象时可能会抛出异常。不过，虽然Python采用动态类型系统，它同时也是强类型的。Python禁止没有明确定义的操作，比如数字加字符串。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tCZpUlVffWLt" data-idx="2-62"><span class="text_H038s" data-text="true">与其它面向对象语言一样，Python允许程序员定义类型。构造一个对象只需要像函数一样调用类型即可，比如，对于前面定义的Fish类型，使用Fish。类型本身也是特殊类型type的对象（type类型本身也是type对象），这种特殊的设计允许对类型进行反射编程。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyvxa0" data-idx="2-63"><span class="text_H038s" data-text="true">Python内置丰富的数据类型。与Java、C++相比，这些数据类型有效地减少代码的长度。下面这个列表简要地描述了Python内置数据类型（适用于Python 3.x）：</span></div><div data-tag="module" data-module-type="table" data-uuid="go6t49rbmo"><div class="moduleTable_SeKUW"><table class="tableBox_p9gKo"><tbody class="tableBody_w6yrq"><tr><th width="0" align="" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rdh6" data-idx=""><span class="text_H038s" data-text="true">类型</span></div></th><th width="0" align="" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rdlu" data-idx=""><span class="text_H038s" data-text="true">描述</span></div></th><th width="270" align="" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rdrk" data-idx=""><span class="text_H038s" data-text="true">例子</span></div></th><th width="157" align="" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rdxa" data-idx=""><span class="text_H038s" data-text="true">备注</span></div></th></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49re0d"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49re2x" data-idx=""><span class="text_H038s" data-text="true">str（string/字符串）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49re65"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49re8p" data-idx=""><span class="text_H038s" data-text="true">一个由字符组成的不可更改的有序串行。</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49reb0"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49ref7" data-idx=""><span class="text_H038s" data-text="true">&quot;&quot;&quot;Spanning</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rejz" data-idx=""><span class="text_H038s" data-text="true">multiple</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49relk" data-idx=""><span class="text_H038s" data-text="true">lines&quot;&quot;&quot;</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49reou"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49reql" data-idx=""><span class="text_H038s" data-text="true">在Python 3.x里，字符串由Unicode字符组成</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49retv"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rewb" data-idx=""><span class="text_H038s" data-text="true">bytes（字节）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rezm"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rf23" data-idx=""><span class="text_H038s" data-text="true">一个由字节组成的不可更改的有序串行。</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rf4h"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rf7c" data-idx=""><span class="text_H038s" data-text="true">b&#x27;Some ASCII&#x27;</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rf8y" data-idx=""><span class="text_H038s" data-text="true">b&quot;Some ASCII&quot;</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rfcc"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4nap9q" data-idx=""><span class="text_H038s" data-text="true">在Python 2.x里，bytes为str的一种</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rffo"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rfi4" data-idx=""><span class="text_H038s" data-text="true">list（列表）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rflg"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rfnx" data-idx=""><span class="text_H038s" data-text="true">可以包含多种类型的可改变的有序串行</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rfr6"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rftm" data-idx=""><span class="text_H038s" data-text="true">[4.0，&#x27;string&#x27;，True]</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rfw9"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyhwt" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rfzi"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rg1w" data-idx=""><span class="text_H038s" data-text="true">tuple（元组）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rg54"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rg7l" data-idx=""><span class="text_H038s" data-text="true">可以包含多种类型的不可改变的有序串行</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rga4"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rgcj" data-idx=""><span class="text_H038s" data-text="true">（4.0，&#x27;string&#x27;，True）</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rgfq"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyhs0" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rgj2"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rglg" data-idx=""><span class="text_H038s" data-text="true">set，frozenset</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rgo2"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rgqu" data-idx=""><span class="text_H038s" data-text="true">与数学中集合的概念类似。无序的、每个元素都是唯一的。</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rgu4"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rgx1" data-idx=""><span class="text_H038s" data-text="true">{4.0，&#x27;string&#x27;，True}</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rgym" data-idx=""><span class="text_H038s" data-text="true">frozenset（[4.0，&#x27;string&#x27;，True]）</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rh1w"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyhmy" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rh56"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rh7l" data-idx=""><span class="text_H038s" data-text="true">dict（字典）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rhat"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rhd9" data-idx=""><span class="text_H038s" data-text="true">一个可改变的由键值对组成的无序串行。</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rhfr"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rhi7" data-idx=""><span class="text_H038s" data-text="true">{&#x27;key1&#x27;：1.0，3：False}</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rhlg"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyhhj" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rhop"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rhr3" data-idx=""><span class="text_H038s" data-text="true">int（整数）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rhtq"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rhw5" data-idx=""><span class="text_H038s" data-text="true">精度不限的整数</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rhzc"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49ri1q" data-idx=""><span class="text_H038s" data-text="true">42</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49ri4x"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyhcb" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49ri7k"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49ri9y" data-idx=""><span class="text_H038s" data-text="true">float（浮点数）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rid7"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rifm" data-idx=""><span class="text_H038s" data-text="true">浮点数。精度与系统相关。</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49riiw"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rila" data-idx=""><span class="text_H038s" data-text="true">3.1415927</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49riol"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyh7j" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rirv"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49riu8" data-idx=""><span class="text_H038s" data-text="true">complex</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rixg"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rj00" data-idx=""><span class="text_H038s" data-text="true">复数</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rj39"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rj5m" data-idx=""><span class="text_H038s" data-text="true">3+2.7j</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rj8u"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyh37" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rjc3"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rjd8" data-idx=""><span class="text_H038s" data-text="true">bool</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rjgg"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rjiw" data-idx=""><span class="text_H038s" data-text="true">逻辑值。只有两个值：真、假</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rjm4"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rjox" data-idx=""><span class="text_H038s" data-text="true">True</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rjpj" data-idx=""><span class="text_H038s" data-text="true">False</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rjsr"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pygyg" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4napka"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4napn1" data-idx=""><span class="text_H038s" data-text="true">builtin_function_or_method</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4napoc"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4napqw" data-idx=""><span class="text_H038s" data-text="true">自带的函数，不可更改也不可增加</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4napso"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4napve" data-idx=""><span class="text_H038s" data-text="true">print</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4napx1" data-idx=""><span class="text_H038s" data-text="true">input</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4napyu"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pygsy" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naq1c"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naq3r" data-idx=""><span class="text_H038s" data-text="true">type（类型）</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naq5j"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naq81" data-idx=""><span class="text_H038s" data-text="true">显示某个值的类型，用type（x）获得</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naq9u"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naqd2" data-idx=""><span class="text_H038s" data-text="true">type（1）-&gt;int</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naqep" data-idx=""><span class="text_H038s" data-text="true">type（‘1’）-&gt;str</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naqg4"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pygnh" data-idx=""><span class="text_H038s" data-text="true">无</span></div></td></tr><tr><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naqjs"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naqm4" data-idx=""><span class="text_H038s" data-text="true">range</span></div></td><td width="0" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naqnw"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naqqa" data-idx=""><span class="text_H038s" data-text="true">按顺序排列的数</span></div></td><td width="270" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4naqs3"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naqwz" data-idx=""><span class="text_H038s" data-text="true">range（10）</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4naqyn" data-idx=""><span class="text_H038s" data-text="true">......list（range（10））-&gt;[0，1，2，3，4，5，6，7，8，9]</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4nar09" data-idx=""><span class="text_H038s" data-text="true">range（1，10）</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4nar1w" data-idx=""><span class="text_H038s" data-text="true">......list（range（1，10）-&gt;[1，2，3，4，5，6，7，8，9]</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4nar3p" data-idx=""><span class="text_H038s" data-text="true">range（1，10，2）</span></div><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4nar5r" data-idx=""><span class="text_H038s" data-text="true">......list（range（1，10，2））-&gt;[1，3，5，7，9]</span></div></td><td width="157" align="left" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t4nar89"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t4narcx" data-idx=""><span class="text_H038s" data-text="true">在Python 2.x中，range为builtin_function_or_method，获得的数为list</span></div></td></tr></tbody></table></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw1zs" data-idx="2-65"><span class="text_H038s" data-text="true">除了各种数据类型，Python语言还用类型来表示函数、模块、类型本身、对象的方法、编译后的Python代码、运行时信息等等。因此，Python具备很强的动态性。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="3-6" data-tag="header" data-uuid="go6syyw21e" data-level="2"><div class="anchorList_CiJE7"><a name="数学运算"></a><a name="3-6"></a></div><h3>数学运算</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw21y" data-idx="2-67"><span class="text_H038s" data-text="true">Python使用与C、Java类似的运算符，支持整数与浮点数的数学运算。同时还支持复数运算与无穷位数</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tCZrKuqLozrz" data-idx="2-68"><span class="text_H038s" data-text="true">import math</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tCZrL9MiuEPn" data-idx="2-69"><span class="text_H038s" data-text="true">print（math.sin（math.pi/2））</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t06cgr3" data-idx="2-70"><span class="text_H038s" data-text="true">1.0</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw258" data-idx="2-71"><span class="text_H038s" data-text="true">fractions模块用于支持分数运算；decimal模块用于支持高精度的浮点数运算。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tCZrXdH0llaS" data-idx="2-72"><span class="text_H038s" data-text="true">Python定义求余运行a % b的值处于开区间</span><section class="formulaContainer_Oej5V inline_iBbeM"><div style="width:38px;display:inline"><img src="https://bkimg.cdn.bcebos.com/formula/022dc8f7bdc7e45fa5965bbbe1df4ece.svg" style="vertical-align:-0.755ex;width:4.745ex;height:2.634ex"/></div></section><span class="text_H038s" data-text="true">内，如果b是负数，开区间变为</span><section class="formulaContainer_Oej5V inline_iBbeM"><div style="width:38px;display:inline"><img src="https://bkimg.cdn.bcebos.com/formula/429d2089fda6c3f235f0c628e5b7c1c9.svg" style="vertical-align:-0.755ex;width:4.745ex;height:2.634ex"/></div></section><span class="text_H038s" data-text="true">。这是一个很常见的定义方式。不过其实它依赖于整除的定义。为了让方程式：b * ( a // b)+ a % b = a恒真，整除运行需要向负无穷小方向取值。比如7 //3的结果是2，而（-7）//3的结果却是-3。这个算法与其它很多编程语言不一样，需要注意，它们的整除运算会向0的方向取值。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw28e" data-idx="2-73"><span class="text_H038s" data-text="true">Python允许像数学的常用写法那样连着写两个比较运行符。比如a ＜ b ＜ c与a ＜ b and b ＜ c等价。C++的结果与Python不一样，首先它会先计算a ＜ b，根据两者的大小获得0或者1两个值之一，然后再与c进行比较。</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="4" data-tag="header" data-uuid="go6t2s2q82" data-level="1"><div class="anchorList_CiJE7"><a name="帮助"></a><a name="4"></a></div><h2 name="4">帮助</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="4" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sNZdkjRevvcb" data-idx="3-1"><span class="text_H038s bold_WEzZ7" data-text="true">1. 列出模块中的函数</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t4v8w2y" data-idx="3-2"><span class="text_H038s" data-text="true">用import导入模块后，可使用函数dir（m）列出模块的所有函数，import是导入模块的命令，m是模块名。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5qhbe9" data-idx="3-3"><span class="text_H038s" data-text="true">例子：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjalk"><div class="codeWraper_JOX7k">import math    
print(dir(math))
# 输出 [&#x27;__doc__&#x27;, &#x27;__loader__&#x27;, &#x27;__name__&#x27;, &#x27;__package__&#x27;, &#x27;__spec__&#x27;, &#x27;acos&#x27;, &#x27;acosh&#x27;, &#x27;asin&#x27;, &#x27;asinh&#x27;, &#x27;atan&#x27;, &#x27;atan2&#x27;, &#x27;atanh&#x27;, &#x27;ceil&#x27;, &#x27;copysign&#x27;, &#x27;cos&#x27;, &#x27;cosh&#x27;, &#x27;degrees&#x27;, &#x27;e&#x27;, &#x27;erf&#x27;, &#x27;erfc&#x27;, &#x27;exp&#x27;, &#x27;expm1&#x27;, &#x27;fabs&#x27;, &#x27;factorial&#x27;, &#x27;floor&#x27;, &#x27;fmod&#x27;, &#x27;frexp&#x27;, &#x27;fsum&#x27;, &#x27;gamma&#x27;, &#x27;gcd&#x27;, &#x27;hypot&#x27;, &#x27;inf&#x27;, &#x27;isclose&#x27;, &#x27;isfinite&#x27;, &#x27;isinf&#x27;, &#x27;isnan&#x27;, &#x27;ldexp&#x27;, &#x27;lgamma&#x27;, &#x27;log&#x27;, &#x27;log10&#x27;, &#x27;log1p&#x27;, &#x27;log2&#x27;, &#x27;modf&#x27;, &#x27;nan&#x27;, &#x27;pi&#x27;, &#x27;pow&#x27;, &#x27;radians&#x27;, &#x27;sin&#x27;, &#x27;sinh&#x27;, &#x27;sqrt&#x27;, &#x27;tan&#x27;, &#x27;tanh&#x27;, &#x27;tau&#x27;, &#x27;trunc&#x27;]</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;import math    \nprint(dir(math))\n# 输出 [&#x27;__doc__&#x27;, &#x27;__loader__&#x27;, &#x27;__name__&#x27;, &#x27;__package__&#x27;, &#x27;__spec__&#x27;, &#x27;acos&#x27;, &#x27;acosh&#x27;, &#x27;asin&#x27;, &#x27;asinh&#x27;, &#x27;atan&#x27;, &#x27;atan2&#x27;, &#x27;atanh&#x27;, &#x27;ceil&#x27;, &#x27;copysign&#x27;, &#x27;cos&#x27;, &#x27;cosh&#x27;, &#x27;degrees&#x27;, &#x27;e&#x27;, &#x27;erf&#x27;, &#x27;erfc&#x27;, &#x27;exp&#x27;, &#x27;expm1&#x27;, &#x27;fabs&#x27;, &#x27;factorial&#x27;, &#x27;floor&#x27;, &#x27;fmod&#x27;, &#x27;frexp&#x27;, &#x27;fsum&#x27;, &#x27;gamma&#x27;, &#x27;gcd&#x27;, &#x27;hypot&#x27;, &#x27;inf&#x27;, &#x27;isclose&#x27;, &#x27;isfinite&#x27;, &#x27;isinf&#x27;, &#x27;isnan&#x27;, &#x27;ldexp&#x27;, &#x27;lgamma&#x27;, &#x27;log&#x27;, &#x27;log10&#x27;, &#x27;log1p&#x27;, &#x27;log2&#x27;, &#x27;modf&#x27;, &#x27;nan&#x27;, &#x27;pi&#x27;, &#x27;pow&#x27;, &#x27;radians&#x27;, &#x27;sin&#x27;, &#x27;sinh&#x27;, &#x27;sqrt&#x27;, &#x27;tan&#x27;, &#x27;tanh&#x27;, &#x27;tau&#x27;, &#x27;trunc&#x27;]&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t2s2qjy" data-idx="3-5"><span class="text_H038s" data-text="true">这个例子列出math模块的一些函数，以双下划线（ _ _ ）开头的名称用于较复杂的python编程。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sNZdl3cGKr0O" data-idx="3-6"><span class="text_H038s bold_WEzZ7" data-text="true">2.查看完整的python内置函数清单</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5afxbt" data-idx="3-7"><span class="text_H038s" data-text="true">查看完整的python内置函数清单，可在提示符后输入  dir（_ _builtins_ _）。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5qhhvp" data-idx="3-8"><span class="text_H038s" data-text="true">例子：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjaoz"><div class="codeWraper_JOX7k">print(dir(__builtins__))

# 输出 [&#x27;ArithmeticError&#x27;, &#x27;AssertionError&#x27;, &#x27;AttributeError&#x27;, &#x27;BaseException&#x27;, &#x27;BlockingIOError&#x27;, &#x27;BrokenPipeError&#x27;, &#x27;BufferError&#x27;, &#x27;BytesWarning&#x27;, &#x27;ChildProcessError&#x27;, &#x27;ConnectionAbortedError&#x27;, &#x27;ConnectionError&#x27;, &#x27;ConnectionRefusedError&#x27;, &#x27;ConnectionResetError&#x27;, &#x27;DeprecationWarning&#x27;, &#x27;EOFError&#x27;, &#x27;Ellipsis&#x27;, &#x27;EnvironmentError&#x27;, &#x27;Exception&#x27;, &#x27;False&#x27;, &#x27;FileExistsError&#x27;, &#x27;FileNotFoundError&#x27;, &#x27;FloatingPointError&#x27;, &#x27;FutureWarning&#x27;, &#x27;GeneratorExit&#x27;, &#x27;IOError&#x27;, &#x27;ImportError&#x27;, &#x27;ImportWarning&#x27;, &#x27;IndentationError&#x27;, &#x27;IndexError&#x27;, &#x27;InterruptedError&#x27;, &#x27;IsADirectoryError&#x27;, &#x27;KeyError&#x27;, &#x27;KeyboardInterrupt&#x27;, &#x27;LookupError&#x27;, &#x27;MemoryError&#x27;, &#x27;ModuleNotFoundError&#x27;, &#x27;NameError&#x27;, &#x27;None&#x27;, &#x27;NotADirectoryError&#x27;, &#x27;NotImplemented&#x27;, &#x27;NotImplementedError&#x27;, &#x27;OSError&#x27;, &#x27;OverflowError&#x27;, &#x27;PendingDeprecationWarning&#x27;, &#x27;PermissionError&#x27;, &#x27;ProcessLookupError&#x27;, &#x27;RecursionError&#x27;, &#x27;ReferenceError&#x27;, &#x27;ResourceWarning&#x27;, &#x27;RuntimeError&#x27;, &#x27;RuntimeWarning&#x27;, &#x27;StopAsyncIteration&#x27;, &#x27;StopIteration&#x27;, &#x27;SyntaxError&#x27;, &#x27;SyntaxWarning&#x27;, &#x27;SystemError&#x27;, &#x27;SystemExit&#x27;, &#x27;TabError&#x27;, &#x27;TimeoutError&#x27;, &#x27;True&#x27;, &#x27;TypeError&#x27;, &#x27;UnboundLocalError&#x27;, &#x27;UnicodeDecodeError&#x27;, &#x27;UnicodeEncodeError&#x27;, &#x27;UnicodeError&#x27;, &#x27;UnicodeTranslateError&#x27;, &#x27;UnicodeWarning&#x27;, &#x27;UserWarning&#x27;, &#x27;ValueError&#x27;, &#x27;Warning&#x27;, &#x27;WindowsError&#x27;, &#x27;ZeroDivisionError&#x27;, &#x27;_&#x27;, &#x27;__build_class__&#x27;, &#x27;__debug__&#x27;, &#x27;__doc__&#x27;, &#x27;__import__&#x27;, &#x27;__loader__&#x27;, &#x27;__name__&#x27;, &#x27;__package__&#x27;, &#x27;__spec__&#x27;, &#x27;abs&#x27;, &#x27;all&#x27;, &#x27;any&#x27;, &#x27;ascii&#x27;, &#x27;bin&#x27;, &#x27;bool&#x27;, &#x27;bytearray&#x27;, &#x27;bytes&#x27;, &#x27;callable&#x27;, &#x27;chr&#x27;, &#x27;classmethod&#x27;, &#x27;compile&#x27;, &#x27;complex&#x27;, &#x27;copyright&#x27;, &#x27;credits&#x27;, &#x27;delattr&#x27;, &#x27;dict&#x27;, &#x27;dir&#x27;, &#x27;divmod&#x27;, &#x27;enumerate&#x27;, &#x27;eval&#x27;, &#x27;exec&#x27;, &#x27;exit&#x27;, &#x27;filter&#x27;, &#x27;float&#x27;, &#x27;format&#x27;, &#x27;frozenset&#x27;, &#x27;getattr&#x27;, &#x27;globals&#x27;, &#x27;hasattr&#x27;, &#x27;hash&#x27;, &#x27;help&#x27;, &#x27;hex&#x27;, &#x27;id&#x27;, &#x27;input&#x27;, &#x27;int&#x27;, &#x27;isinstance&#x27;, &#x27;issubclass&#x27;, &#x27;iter&#x27;, &#x27;len&#x27;, &#x27;license&#x27;, &#x27;list&#x27;, &#x27;locals&#x27;, &#x27;map&#x27;, &#x27;max&#x27;, &#x27;memoryview&#x27;, &#x27;min&#x27;, &#x27;next&#x27;, &#x27;object&#x27;, &#x27;oct&#x27;, &#x27;open&#x27;, &#x27;ord&#x27;, &#x27;pow&#x27;, &#x27;print&#x27;, &#x27;property&#x27;, &#x27;quit&#x27;, &#x27;range&#x27;, &#x27;repr&#x27;, &#x27;reversed&#x27;, &#x27;round&#x27;, &#x27;set&#x27;, &#x27;setattr&#x27;, &#x27;slice&#x27;, &#x27;sorted&#x27;, &#x27;staticmethod&#x27;, &#x27;str&#x27;, &#x27;sum&#x27;, &#x27;super&#x27;, &#x27;tuple&#x27;, &#x27;type&#x27;, &#x27;vars&#x27;, &#x27;zip&#x27;]</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;print(dir(__builtins__))\n\n# 输出 [&#x27;ArithmeticError&#x27;, &#x27;AssertionError&#x27;, &#x27;AttributeError&#x27;, &#x27;BaseException&#x27;, &#x27;BlockingIOError&#x27;, &#x27;BrokenPipeError&#x27;, &#x27;BufferError&#x27;, &#x27;BytesWarning&#x27;, &#x27;ChildProcessError&#x27;, &#x27;ConnectionAbortedError&#x27;, &#x27;ConnectionError&#x27;, &#x27;ConnectionRefusedError&#x27;, &#x27;ConnectionResetError&#x27;, &#x27;DeprecationWarning&#x27;, &#x27;EOFError&#x27;, &#x27;Ellipsis&#x27;, &#x27;EnvironmentError&#x27;, &#x27;Exception&#x27;, &#x27;False&#x27;, &#x27;FileExistsError&#x27;, &#x27;FileNotFoundError&#x27;, &#x27;FloatingPointError&#x27;, &#x27;FutureWarning&#x27;, &#x27;GeneratorExit&#x27;, &#x27;IOError&#x27;, &#x27;ImportError&#x27;, &#x27;ImportWarning&#x27;, &#x27;IndentationError&#x27;, &#x27;IndexError&#x27;, &#x27;InterruptedError&#x27;, &#x27;IsADirectoryError&#x27;, &#x27;KeyError&#x27;, &#x27;KeyboardInterrupt&#x27;, &#x27;LookupError&#x27;, &#x27;MemoryError&#x27;, &#x27;ModuleNotFoundError&#x27;, &#x27;NameError&#x27;, &#x27;None&#x27;, &#x27;NotADirectoryError&#x27;, &#x27;NotImplemented&#x27;, &#x27;NotImplementedError&#x27;, &#x27;OSError&#x27;, &#x27;OverflowError&#x27;, &#x27;PendingDeprecationWarning&#x27;, &#x27;PermissionError&#x27;, &#x27;ProcessLookupError&#x27;, &#x27;RecursionError&#x27;, &#x27;ReferenceError&#x27;, &#x27;ResourceWarning&#x27;, &#x27;RuntimeError&#x27;, &#x27;RuntimeWarning&#x27;, &#x27;StopAsyncIteration&#x27;, &#x27;StopIteration&#x27;, &#x27;SyntaxError&#x27;, &#x27;SyntaxWarning&#x27;, &#x27;SystemError&#x27;, &#x27;SystemExit&#x27;, &#x27;TabError&#x27;, &#x27;TimeoutError&#x27;, &#x27;True&#x27;, &#x27;TypeError&#x27;, &#x27;UnboundLocalError&#x27;, &#x27;UnicodeDecodeError&#x27;, &#x27;UnicodeEncodeError&#x27;, &#x27;UnicodeError&#x27;, &#x27;UnicodeTranslateError&#x27;, &#x27;UnicodeWarning&#x27;, &#x27;UserWarning&#x27;, &#x27;ValueError&#x27;, &#x27;Warning&#x27;, &#x27;WindowsError&#x27;, &#x27;ZeroDivisionError&#x27;, &#x27;_&#x27;, &#x27;__build_class__&#x27;, &#x27;__debug__&#x27;, &#x27;__doc__&#x27;, &#x27;__import__&#x27;, &#x27;__loader__&#x27;, &#x27;__name__&#x27;, &#x27;__package__&#x27;, &#x27;__spec__&#x27;, &#x27;abs&#x27;, &#x27;all&#x27;, &#x27;any&#x27;, &#x27;ascii&#x27;, &#x27;bin&#x27;, &#x27;bool&#x27;, &#x27;bytearray&#x27;, &#x27;bytes&#x27;, &#x27;callable&#x27;, &#x27;chr&#x27;, &#x27;classmethod&#x27;, &#x27;compile&#x27;, &#x27;complex&#x27;, &#x27;copyright&#x27;, &#x27;credits&#x27;, &#x27;delattr&#x27;, &#x27;dict&#x27;, &#x27;dir&#x27;, &#x27;divmod&#x27;, &#x27;enumerate&#x27;, &#x27;eval&#x27;, &#x27;exec&#x27;, &#x27;exit&#x27;, &#x27;filter&#x27;, &#x27;float&#x27;, &#x27;format&#x27;, &#x27;frozenset&#x27;, &#x27;getattr&#x27;, &#x27;globals&#x27;, &#x27;hasattr&#x27;, &#x27;hash&#x27;, &#x27;help&#x27;, &#x27;hex&#x27;, &#x27;id&#x27;, &#x27;input&#x27;, &#x27;int&#x27;, &#x27;isinstance&#x27;, &#x27;issubclass&#x27;, &#x27;iter&#x27;, &#x27;len&#x27;, &#x27;license&#x27;, &#x27;list&#x27;, &#x27;locals&#x27;, &#x27;map&#x27;, &#x27;max&#x27;, &#x27;memoryview&#x27;, &#x27;min&#x27;, &#x27;next&#x27;, &#x27;object&#x27;, &#x27;oct&#x27;, &#x27;open&#x27;, &#x27;ord&#x27;, &#x27;pow&#x27;, &#x27;print&#x27;, &#x27;property&#x27;, &#x27;quit&#x27;, &#x27;range&#x27;, &#x27;repr&#x27;, &#x27;reversed&#x27;, &#x27;round&#x27;, &#x27;set&#x27;, &#x27;setattr&#x27;, &#x27;slice&#x27;, &#x27;sorted&#x27;, &#x27;staticmethod&#x27;, &#x27;str&#x27;, &#x27;sum&#x27;, &#x27;super&#x27;, &#x27;tuple&#x27;, &#x27;type&#x27;, &#x27;vars&#x27;, &#x27;zip&#x27;]&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="sNZdlLC8yVXU" data-idx="3-10"><span class="text_H038s bold_WEzZ7" data-text="true">3.查看某个函数的文档帮助信息</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t2s2quc" data-idx="3-11"><span class="text_H038s" data-text="true">可以用函数help（函数）来查看某个函数的文档帮助信息。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5qhhz2" data-idx="3-12"><span class="text_H038s" data-text="true">例子：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t5qhbg4"><div class="codeWraper_JOX7k">help(sum)

&quot;&quot;&quot;
输出：

Help on built-in function sum in module builtins:

sum(iterable, start=0, /)   
      
    Return the sum of a &#x27;start&#x27; value (default: 0) plus an iterable of numbers      When the iterable is empty, return the start value.    
      
    This function is intended specifically for use with numeric values and may    reject non-numeric types.

&quot;&quot;&quot;</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;help(sum)\n\n\&quot;\&quot;\&quot;\n输出：\n\nHelp on built-in function sum in module builtins:\n\nsum(iterable, start=0, /)   \n      \n    Return the sum of a &#x27;start&#x27; value (default: 0) plus an iterable of numbers      When the iterable is empty, return the start value.    \n      \n    This function is intended specifically for use with numeric values and may    reject non-numeric types.\n\n\&quot;\&quot;\&quot;&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t2s2qzi" data-idx="3-14"><span class="text_H038s" data-text="true">可以直接在提示符下输入help()，然后输入某个模块或函数名得到详细的帮助信息。</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="5" data-tag="header" data-uuid="go6t5jyikb" data-level="1"><div class="anchorList_CiJE7"><a name="接口"></a><a name="5"></a></div><h2 name="5">接口</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="5" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfwsp" data-idx="4-1"><span class="text_H038s" data-text="true">CGI 目前由NCSA维护，NCSA定义CGI如下：</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [7]<em id="sup-7"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfwuc" data-idx="4-2"><span class="text_H038s" data-text="true">CGI（Common Gateway Interface），</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E9%80%9A%E7%94%A8%E7%BD%91%E5%85%B3%E6%8E%A5%E5%8F%A3/3351606?fromModule=lemma_inlink" target="_blank" data-from-module="">通用网关接口</a></span><span class="text_H038s" data-text="true">，它是一段程序，运行在服务器上如：HTTP服务器，提供同客户端HTML页面的接口。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfwvy" data-idx="4-3"><span class="text_H038s" data-text="true">CGI程序可以是Python脚本、Perl脚本、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Shell%E8%84%9A%E6%9C%AC/572265?fromModule=lemma_inlink" target="_blank" data-from-module="">Shell脚本</a></span><span class="text_H038s" data-text="true">、C或者C++程序等。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="5-1" data-tag="header" data-uuid="go6t1zfwxk" data-level="2"><div class="anchorList_CiJE7"><a name="服务器"></a><a name="5-1"></a></div><h3>服务器</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfwz6" data-idx="4-5"><span class="text_H038s" data-text="true">在进行CGI编程前，请确保Web服务器支持CGI及已经配置了CGI的处理程序。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfx0s" data-idx="4-6"><span class="text_H038s" data-text="true">所有的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/HTTP%E6%9C%8D%E5%8A%A1%E5%99%A8/17368665?fromModule=lemma_inlink" target="_blank" data-from-module="">HTTP服务器</a></span><span class="text_H038s" data-text="true">执行CGI程序都保存在一个预先配置的目录。这个目录被称为CGI目录，并按照惯例，它被命名为/var/www/cgi-bin目录。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfx2e" data-idx="4-7"><span class="text_H038s" data-text="true">CGI文件的扩展名为.cgi，python也可以使用.py扩展名。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfx3g" data-idx="4-8"><span class="text_H038s" data-text="true">默认情况下，Linux服务器配置运行的cgi-bin目录中为/var/www。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfx53" data-idx="4-9"><span class="text_H038s" data-text="true">如果想指定的其他运行CGI脚本的目录，可以修改httpd.conf配置文件，如下所示：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjauj"><div class="codeWraper_JOX7k">&lt;Directory&quot;/var/www/cgi-bin&quot;&gt;
Allow Override None
Options ExecCGI
Order allow,deny
Allow from all
&lt;/Directory&gt;
&lt;Directory&quot;/var/www/cgi-bin&quot;&gt;
Options All
&lt;/Directory&gt;</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;&lt;Directory\&quot;/var/www/cgi-bin\&quot;&gt;\nAllow Override None\nOptions ExecCGI\nOrder allow,deny\nAllow from all\n&lt;/Directory&gt;\n&lt;Directory\&quot;/var/www/cgi-bin\&quot;&gt;\nOptions All\n&lt;/Directory&gt;&quot;,&quot;lang&quot;:&quot;xml&quot;}"></div></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="5-2" data-tag="header" data-uuid="go6t1zfx7b" data-level="2"><div class="anchorList_CiJE7"><a name="程序"></a><a name="5-2"></a></div><h3>程序</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfx8w" data-idx="4-12"><span class="text_H038s" data-text="true">使用Python创建第一个CGI程序，文件名为hello.py，文件位于/var/www/cgi-bin目录中，内容如下，修改文件的权限为755：</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [7]<em id="sup-7"></em></sup></span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjawa"><div class="codeWraper_JOX7k">#!/usr/bin/env python
print(&quot;Content-type:text/html\r\n\r\n&quot;)
print(&quot;&lt;html&gt;&quot;)
print(&quot;&lt;head&gt;&quot;)
print(&quot;&quot;)
print(&quot;&lt;/head&gt;&quot;)
print(&quot;&lt;body&gt;&quot;)
print(&quot;&lt;h2&gt;Hello World! This is my first CGI program&lt;/h2&gt;&quot;)
print(&quot;&lt;/body&gt;&quot;)
print(&quot;&lt;/html&gt;&quot;)</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;#!/usr/bin/env python\nprint(\&quot;Content-type:text/html\\r\\n\\r\\n\&quot;)\nprint(\&quot;&lt;html&gt;\&quot;)\nprint(\&quot;&lt;head&gt;\&quot;)\nprint(\&quot;\&quot;)\nprint(\&quot;&lt;/head&gt;\&quot;)\nprint(\&quot;&lt;body&gt;\&quot;)\nprint(\&quot;&lt;h2&gt;Hello World! This is my first CGI program&lt;/h2&gt;\&quot;)\nprint(\&quot;&lt;/body&gt;\&quot;)\nprint(\&quot;&lt;/html&gt;\&quot;)&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfxc4" data-idx="4-14"><span class="text_H038s" data-text="true">以上程序在浏览器访问显示结果如下：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjaxy"><div class="codeWraper_JOX7k">Hello World! This is my first CGI program</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;Hello World! This is my first CGI program&quot;,&quot;lang&quot;:&quot;null&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rk50" data-idx="4-16"><span class="text_H038s" data-text="true">这个的hello.py脚本是一个简单的Python脚本，脚本第一的输出内容&quot;Content-type：text/html\r\n\r\n&quot;发送到浏览器并告知浏览器显示的内容类型为&quot;text/html&quot;。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="5-3" data-tag="header" data-uuid="go6t1zfxgx" data-level="2"><div class="anchorList_CiJE7"><a name="环境变量"></a><a name="5-3"></a></div><h3>环境变量</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zfxij" data-idx="4-18"><span class="text_H038s" data-text="true">所有的CGI程序都接收以下的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F/1730949?fromModule=lemma_inlink" target="_blank" data-from-module="">环境变量</a></span><span class="text_H038s" data-text="true">，这些变量在CGI程序中发挥了重要的作用：</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [7]<em id="sup-7"></em></sup></span></div><div data-tag="module" data-module-type="table" data-uuid="go6t49rk6t"><div class="moduleTable_SeKUW"><table class="tableBox_p9gKo"><tbody class="tableBody_w6yrq"><tr><th width="103" align="" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rlw7" data-idx=""><span class="text_H038s" data-text="true">变量名</span></div></th><th width="646" align="" rowspan="1" colSpan="1" class="tableTh_R9FWV"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rm0s" data-idx=""><span class="text_H038s" data-text="true">描述</span></div></th></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rm40"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rm6h" data-idx=""><span class="text_H038s" data-text="true">CONTENT_TYPE</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rm9q"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rmcg" data-idx=""><span class="text_H038s" data-text="true">这个环境变量的值指示所传递来的信息的MIME类型。目前，环境变量CONTENT_TYPE一般都是：application/x-www-form-urlencoded，他表示数据来自于HTML表单。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rmfp"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rmi6" data-idx=""><span class="text_H038s" data-text="true">CONTENT_LENGTH</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rmle"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rmo4" data-idx=""><span class="text_H038s" data-text="true">如果服务器与CGI程序信息的传递方式是POST，这个环境变量即使从标准输入STDIN中可以读到的有效数据的字节数。这个环境变量在读取所输入的数据时必须使用。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rmrf"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rmsl" data-idx=""><span class="text_H038s" data-text="true">HTTP_COOKIE</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rmvv"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rmyb" data-idx=""><span class="text_H038s" data-text="true">客户机内的 COOKIE 内容。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rn1l"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rn40" data-idx=""><span class="text_H038s" data-text="true">HTTP_USER_AGENT</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rn79"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rn9u" data-idx=""><span class="text_H038s" data-text="true">提供包含了版本数或其他专有数据的客户浏览器信息。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rnd2"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rnfi" data-idx=""><span class="text_H038s" data-text="true">PATH_INFO</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rniq"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rnke" data-idx=""><span class="text_H038s" data-text="true">这个环境变量的值表示紧接在CGI程序名之后的其他路径信息。它常常作为CGI程序的参数出现。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rnno"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rnq2" data-idx=""><span class="text_H038s" data-text="true">QUERY_STRING</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rntb"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rnw1" data-idx=""><span class="text_H038s" data-text="true">如果服务器与CGI程序信息的传递方式是GET，这个环境变量的值即使所传递的信息。这个信息经跟在CGI程序名的后面，两者中间用一个问号&#x27;?&#x27;分隔。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rnza"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49ro1q" data-idx=""><span class="text_H038s" data-text="true">REMOTE_ADDR</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49ro50"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49ro7s" data-idx=""><span class="text_H038s" data-text="true">这个环境变量的值是发送请求的客户机的IP地址，例如上面的************。这个值总是存在的。而且它是Web客户机需要提供给Web服务器的唯一标识，可以在CGI程序中用它来区分不同的Web客户机。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rob0"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49roc4" data-idx=""><span class="text_H038s" data-text="true">REMOTE_HOST</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rofd"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rohy" data-idx=""><span class="text_H038s" data-text="true">这个环境变量的值包含发送CGI请求的客户机的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E4%B8%BB%E6%9C%BA%E5%90%8D/2836107?fromModule=lemma_inlink" target="_blank" data-from-module="">主机名</a></span><span class="text_H038s" data-text="true">。如果不支持你想查询，则无需定义此环境变量。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rola"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rono" data-idx=""><span class="text_H038s" data-text="true">REQUEST_METHOD</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49roqz"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rotl" data-idx=""><span class="text_H038s" data-text="true">提供脚本被调用的方法。对于使用 HTTP/1.0 协议的脚本，仅 GET 和 POST 有意义。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49roww"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rozb" data-idx=""><span class="text_H038s" data-text="true">SCRIPT_FILENAME</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rp2k"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rp3w" data-idx=""><span class="text_H038s" data-text="true">CGI脚本的完整路径</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rp76"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rp9j" data-idx=""><span class="text_H038s" data-text="true">SCRIPT_NAME</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rpcs"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rpf8" data-idx=""><span class="text_H038s" data-text="true">CGI脚本的的名称</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rpif"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rpjh" data-idx=""><span class="text_H038s" data-text="true">SERVER_NAME</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rpmo"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rpp5" data-idx=""><span class="text_H038s" data-text="true">这是你的 WEB 服务器的主机名、别名或IP地址。</span></div></td></tr><tr><td width="103" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rpse"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rpur" data-idx=""><span class="text_H038s" data-text="true">SERVER_SOFTWARE</span></div></td><td width="646" align="" rowspan="1" colSpan="1" class="tableTd_WqoSX" data-uuid="go6t49rpxb"><div class="para_YYuCh table_MAziG MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rpzw" data-idx=""><span class="text_H038s" data-text="true">这个环境变量的值包含了调用CGI程序的HTTP服务器的名称和版本号。例如，上面的值为Apache/2.2.14（Unix）</span></div></td></tr></tbody></table></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t1zg1ib" data-idx="4-20"><span class="text_H038s" data-text="true">以下是一个简单的CGI脚本输出CGI的环境变量：</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjc8s"><div class="codeWraper_JOX7k">#!/usr/bin/python
import os
print(&quot;Content-type:text/html\r\n\r\n&quot;)
print(&quot;Environment&quot;)
for param in os.environ.keys():
    print&quot;&lt;b&gt;%20s&lt;/b&gt;:%s&lt;\br&gt;&quot; %(param,os.environ[param])</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;#!/usr/bin/python\nimport os\nprint(\&quot;Content-type:text/html\\r\\n\\r\\n\&quot;)\nprint(\&quot;Environment\&quot;)\nfor param in os.environ.keys():\n    print\&quot;&lt;b&gt;%20s&lt;/b&gt;:%s&lt;\\br&gt;\&quot; %(param,os.environ[param])&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="6" data-tag="header" data-uuid="go6t5jyjrh" data-level="1"><div class="anchorList_CiJE7"><a name="应用领域"></a><a name="6"></a></div><h2 name="6">应用领域</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="6" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5jyjt9" data-idx="5-1"><span class="text_H038s" data-text="true">Python是一种解释型脚本语言，可以应用于以下领域：</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [9]<em id="sup-9"></em></sup></span></div><ul data-tag="list" data-uuid="go6t5pyj9v" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjeq" data-idx=""><span class="text_H038s" data-text="true">Web 和 Internet开发</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjgi" data-idx=""><span class="text_H038s" data-text="true">科学计算和统计</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyji6" data-idx=""><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/9180?fromModule=lemma_inlink" target="_blank" data-from-module="">人工智能</a></span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjiv" data-idx=""><span class="text_H038s" data-text="true">桌面界面开发</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjkj" data-idx=""><span class="text_H038s" data-text="true">软件开发</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjm7" data-idx=""><span class="text_H038s" data-text="true">后端开发</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjnv" data-idx=""><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%BD%91%E7%BB%9C%E6%8E%A5%E5%8F%A3/10884833?fromModule=lemma_inlink" target="_blank" data-from-module="">网络接口</a></span><span class="text_H038s" data-text="true">：能方便进行系统维护和管理，Linux下标志性语言之一，是很多系统管理员理想的编程工具。</span></div></li></ul><ul data-tag="list" data-uuid="tOcC5smqBTFB" class="paraList_tevWj unordered_zPIvJ"><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5smyP2Ar" data-idx=""><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E5%9B%BE%E5%BD%A2%E5%A4%84%E7%90%86/20723984?fromModule=lemma_inlink" target="_blank" data-from-module="">图形处理</a></span><span class="text_H038s" data-text="true">：有PIL、Tkinter等图形库支持，能方便进行图形处理。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5snFes7f" data-idx=""><span class="text_H038s" data-text="true">数学处理：NumPy扩展提供大量与许多标准数学库的接口。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5sotjnBT" data-idx=""><span class="text_H038s" data-text="true">文本处理：python提供的re模块能支持正则表达式，还提供SGML，XML分析模块，许多程序员利用python进行XML程序的开发。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5soJhV1m" data-idx=""><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%95%B0%E6%8D%AE%E5%BA%93%E7%BC%96%E7%A8%8B/21334683?fromModule=lemma_inlink" target="_blank" data-from-module="">数据库编程</a></span><span class="text_H038s" data-text="true">：程序员可通过遵循Python DB-API（应用程序编程接口）规范的模块与Microsoft SQL Server，Oracle，Sybase，DB2，MySQL、SQLite等数据库通信。python自带有一个Gadfly模块，提供了一个完整的SQL环境。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5soPS5Hy" data-idx=""><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E7%BD%91%E7%BB%9C%E7%BC%96%E7%A8%8B/9986797?fromModule=lemma_inlink" target="_blank" data-from-module="">网络编程</a></span><span class="text_H038s" data-text="true">：提供丰富的模块支持sockets编程，能方便快速地开发分布式应用程序。很多大规模软件开发计划例如Zope，Mnet 及BitTorrent. Google都在广泛地使用它。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5soUQvwe" data-idx=""><span class="text_H038s" data-text="true">Web编程：应用的开发语言，支持最新的XML技术。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5spU7fHP" data-idx=""><span class="text_H038s" data-text="true">多媒体应用：Python的PyOpenGL模块封装了“OpenGL应用程序编程接口”，能进行二维和三维图像处理。PyGame模块可用于编写游戏软件。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5spIssPQ" data-idx=""><span class="text_H038s" data-text="true">pymo引擎：PYMO全称为python memories off，是一款运行于Symbian S60V3，Symbian3，S60V5，Symbian3，Android系统上的AVG游戏引擎。因其基于python2.0平台开发，并且适用于创建秋之回忆（memories off）风格的AVG游戏，故命名为PYMO。</span></div></li><li><div class="para_YYuCh unorderedList_RGRFl MARK_MODULE" data-tag="paragraph" data-uuid="tOcC5spLbMvl" data-idx=""><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E9%BB%91%E5%AE%A2%E7%BC%96%E7%A8%8B/1125212?fromModule=lemma_inlink" target="_blank" data-from-module="">黑客编程</a></span><span class="text_H038s" data-text="true">：python有一个hack的库，内置了一些函数供调用。</span></div></li></ul><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syy9w63" data-idx="5-4"><span class="text_H038s bold_WEzZ7" data-text="true">用Python写简单爬虫</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syy9w82" data-idx="5-5"><span class="text_H038s" data-text="true">首先，要通过urllib2这个Module获得对应的HTML源码（PS：在python3.3之后urllib2已经不能再用，代之以urllib）。</span></div><div data-tag="module" data-module-type="code" data-uuid="go6t4zjcd2"><div class="codeWraper_JOX7k">import urllib2  #调用urllib2  
url=&#x27;http://www.baidu.com/s?wd=cloga&#x27; #把等号右边的网址赋值给url
html=urllib2.urlopen(url).read()   #html随意取名 等号后面的动作是打开源代码页面，并阅读
print html #打印</div><div style="display:none" class="value-code" data-module-value="{&quot;code&quot;:&quot;import urllib2  #调用urllib2  \nurl=&#x27;http://www.baidu.com/s?wd=cloga&#x27; #把等号右边的网址赋值给url\nhtml=urllib2.urlopen(url).read()   #html随意取名 等号后面的动作是打开源代码页面，并阅读\nprint html #打印&quot;,&quot;lang&quot;:&quot;python&quot;}"></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syy9wcy" data-idx="5-7"><span class="text_H038s" data-text="true">通过上面这三句就可以将URL的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%BA%90%E7%A0%81/344212?fromModule=lemma_inlink" target="_blank" data-from-module="">源码</a></span><span class="text_H038s" data-text="true">存在content变量中，其类型为字符型。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syy9wej" data-idx="5-8"><span class="text_H038s" data-text="true">接下来是要从这堆HTML源码中提取需要的内容。用Chrome查看一下对应的内容的代码（也可以用Firefox的Firebug）。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syy9wg5" data-idx="5-9"><span class="text_H038s" data-text="true">可以看到url的信息存储在</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/span%E6%A0%87%E7%AD%BE/2421838?fromModule=lemma_inlink" target="_blank" data-from-module="">span标签</a></span><span class="text_H038s" data-text="true">中，要获取其中的信息可以用正则表达式。</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="7" data-tag="header" data-uuid="go6t5jykdt" data-level="1"><div class="anchorList_CiJE7"><a name="开发工具"></a><a name="7"></a></div><h2 name="7">开发工具</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="7" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syxvqkj" data-idx="6-1"><span class="text_H038s bold_WEzZ7" data-text="true">Tkinter</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4j7g" data-idx="6-2"><span class="text_H038s" data-text="true">Python默认的图形界面接口。Tkinter是一个和Tk接口的Python模块，Tkinter库提供了对Tk API的接口，它属于Tcl/Tk的GUI工具组。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syxvqm8" data-idx="6-3"><span class="text_H038s bold_WEzZ7" data-text="true">PyGTK</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4j9q" data-idx="6-4"><span class="text_H038s" data-text="true">用于python GUI程序开发的GTK+库。GTK就是用来实现GIMP和Gnome的库。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syxvqnu" data-idx="6-5"><span class="text_H038s bold_WEzZ7" data-text="true">PyQt</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jcw" data-idx="6-6"><span class="text_H038s" data-text="true">用于python的Qt开发库。QT就是实现了KDE环境的那个库，由一系列的模块组成，有qt，qtcanvas，qtgl，qtnetwork，qtsql，qttable，qtui and qtxml，包含有300个类和超过5750个的函数和方法。PyQt还支持一个叫qtext的模块，它包含一个QScintilla库。该库是Scintillar编辑器类的Qt接口。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jeg" data-idx="6-7"><span class="text_H038s bold_WEzZ7" data-text="true">wxPython</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jg1" data-idx="6-8"><span class="text_H038s" data-text="true">GUI编程框架，熟悉MFC的人会非常喜欢，简直是同一架构（对于初学者或者对设计要求不高的用户来说，使用Boa Constructor可以方便迅速的进行wxPython的开发）属于外置库，要先下载。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jhm" data-idx="6-9"><span class="text_H038s bold_WEzZ7" data-text="true">PIL</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jhw" data-idx="6-10"><span class="text_H038s" data-text="true">python提供强大的图形处理的能力，并提供广泛的图形文件格式支持，该库能进行图形格式的转换、打印和显示。还能进行一些图形效果的处理，如图形的放大、缩小和旋转等。是Python用户进行图象处理的强有力工具。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jji" data-idx="6-11"><span class="text_H038s bold_WEzZ7" data-text="true">Psyco</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jl3" data-idx="6-12"><span class="text_H038s" data-text="true">一个Python代码加速度器，可使Python代码的执行速度提高到与编译语言一样的水平。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jmo" data-idx="6-13"><span class="text_H038s bold_WEzZ7" data-text="true">xmpppy</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jo8" data-idx="6-14"><span class="text_H038s" data-text="true">Jabber服务器采用开发的XMPP协议，Google Talk也是采用XMPP协议的IM系统。在Python中有一个xmpppy模块支持该协议。也就是说，可以通过该模块与Jabber服务器通信。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jpt" data-idx="6-15"><span class="text_H038s bold_WEzZ7" data-text="true">PyMedia</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jre" data-idx="6-16"><span class="text_H038s" data-text="true">用于多媒体操作的python模块。它提供了丰富而简单的接口用于多媒体处理（wav，mp3，ogg，avi，divx，dvd，cdda etc）。可在Windows和Linux平台下使用。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jsz" data-idx="6-17"><span class="text_H038s bold_WEzZ7" data-text="true">Pmw</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jul" data-idx="6-18"><span class="text_H038s" data-text="true">Python megawidgets，Python超级GUI组件集，一个在python中利用Tkinter模块构建的高级GUI组件，每个Pmw都合并了一个或多个Tkinter组件，以实现更有用和更复杂的功能。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jw5" data-idx="6-19"><span class="text_H038s bold_WEzZ7" data-text="true">PyXML</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jxr" data-idx="6-20"><span class="text_H038s" data-text="true">用Python解析和处理XML文档的工具包，包中的4DOM是完全相容于W3C DOM规范的。它包含以下内容：</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jzc" data-idx="6-21"><span class="text_H038s" data-text="true">xmlproc：一个符合规范的</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/XML%E8%A7%A3%E6%9E%90%E5%99%A8/2673664?fromModule=lemma_inlink" target="_blank" data-from-module="">XML解析器</a></span><span class="text_H038s" data-text="true">。Expat：一个快速的，非验证的XML解析器。还有其他和他同级别的还有 PyHtml PySGML。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4jzw" data-idx="6-22"><span class="text_H038s bold_WEzZ7" data-text="true">PyGame</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4k1h" data-idx="6-23"><span class="text_H038s" data-text="true">用于多媒体开发和游戏软件开发的模块。可以直接使用pip install pygame下载</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4k33" data-idx="6-24"><span class="text_H038s bold_WEzZ7" data-text="true">PyOpenGL</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4k4o" data-idx="6-25"><span class="text_H038s" data-text="true">模块封装了“OpenGL应用程序编程接口”，通过该模块python程序员可在程序中集成2D和3D的图形。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syxvqq3" data-idx="6-26"><span class="text_H038s bold_WEzZ7" data-text="true">NumPy、NumArray、</span><span class="text_H038s" data-text="true">SAGE</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4k7v" data-idx="6-27"><span class="text_H038s" data-text="true">NumArray是Python的一个扩展库，主要用于处理任意维数的固定类型数组，简单说就是一个矩阵库。它的底层代码使用C来编写，所以速度的优势很明显。SAGE是基于NumPy和其他几个工具所整合成的数学软件包，目标是取代Magma，Maple，Mathematica和Matlab 这类工具。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4k9g" data-idx="6-28"><span class="text_H038s bold_WEzZ7" data-text="true">MySQLdb</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4k9p" data-idx="6-29"><span class="text_H038s" data-text="true">用于连接</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/MySQL%E6%95%B0%E6%8D%AE%E5%BA%93/10991669?fromModule=lemma_inlink" target="_blank" data-from-module="">MySQL数据库</a></span><span class="text_H038s" data-text="true">。还有用于zope的ZMySQLDA模块，通过它就可在zope中连接mysql数据库。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szj4agm" data-idx="6-30"><span class="text_H038s bold_WEzZ7" data-text="true">Sqlite3</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t4v8xyp" data-idx="6-31"><span class="text_H038s" data-text="true">用于连接sqlite数据库。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4kbb" data-idx="6-32"><span class="text_H038s bold_WEzZ7" data-text="true">Python-ldap</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4kcx" data-idx="6-33"><span class="text_H038s" data-text="true">提供一组面向对象的API，可方便地在python中访问ldap目录服务，它基于OpenLDAP2.x。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4kei" data-idx="6-34"><span class="text_H038s bold_WEzZ7" data-text="true">smtplib</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4kg4" data-idx="6-35"><span class="text_H038s" data-text="true">发送电子邮件。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4khq" data-idx="6-36"><span class="text_H038s bold_WEzZ7" data-text="true">ftplib</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syx4kjb" data-idx="6-37"><span class="text_H038s" data-text="true">定义了FTP类和一些方法，用以进行客户端的ftp编程。如果想了解ftp协议的详细内容，请参考RFC959。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t093ph5" data-idx="6-38"><span class="text_H038s bold_WEzZ7" data-text="true">PyOpenCL</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t093pis" data-idx="6-39"><span class="text_H038s" data-text="true">OpenCL的Python接口，通过该模块可以使用GPU实现并行计算。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjtw" data-idx="6-40"><span class="text_H038s bold_WEzZ7" data-text="true">xes-lib</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5pyjvl" data-idx="6-41"><span class="text_H038s" data-text="true">学而思库可用于发送邮件、查看天气等功能。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="ulFWV6sg5MkV" data-idx="6-42"><span class="text_H038s bold_WEzZ7" data-text="true">socket</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="ulFX0Qk7M1bx" data-idx="6-43"><span class="text_H038s" data-text="true">又称‘套接字’，可用于简单的网络通信，低级别的网络服务支持基本的 Socket，它提供了标准的 BSD Sockets API，可以访问底层操作系统Socket接口的全部方法。</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="8" data-tag="header" data-uuid="go6syyw2f2" data-level="1"><div class="anchorList_CiJE7"><a name="标准库"></a><a name="8"></a></div><h2 name="8">标准库</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="8" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2go" data-idx="7-1"><span class="text_H038s" data-text="true">Python拥有一个强大的标准库。Python语言的核心只包含数字、字符串、列表、字典、文件等常见类型和函数，而由Python标准库提供了系统管理、网络通信、文本处理、数据库接口、图形系统、XML处理等额外的功能。Python标准库命名接口清晰、文档良好，很容易学习和使用。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2ib" data-idx="7-2"><span class="text_H038s" data-text="true">Python社区提供了大量的第三方模块，使用方式与标准库类似。它们的功能无所不包，覆盖科学计算、Web开发、</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/%E6%95%B0%E6%8D%AE%E5%BA%93%E6%8E%A5%E5%8F%A3/5897447?fromModule=lemma_inlink" target="_blank" data-from-module="">数据库接口</a></span><span class="text_H038s" data-text="true">、图形系统多个领域，并且大多成熟而稳定。第三方模块可以使用Python或者C语言编写。SWIG，SIP常用于将C语言编写的程序库转化为Python模块。Boost C++ Libraries包含了一组库，Boost.Python，使得以 Python 或 C++ 编写的程序能互相调用。借助于拥有基于标准库的大量工具、能够使用低级语言如C和可以作为其他库接口的C++，Python已成为一种强大的应用于其他语言与工具之间的胶水语言。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2jx" data-idx="7-3"><span class="text_H038s" data-text="true">Python标准库的主要功能有：</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2lj" data-idx="7-4"><span class="text_H038s" data-text="true">文本处理，包含文本格式化、正则表达式匹配、文本差异计算与合并、Unicode支持，二进制数据处理等功能</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2n5" data-idx="7-5"><span class="text_H038s" data-text="true">文件处理，包含文件操作、创建临时文件、文件压缩与归档、操作配置文件等功能</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2os" data-idx="7-6"><span class="text_H038s" data-text="true">操作系统功能，包含线程与进程支持、IO复用、日期与时间处理、调用系统函数、写日记（logging）等功能</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2qe" data-idx="7-7"><span class="text_H038s" data-text="true">网络通信，包含网络套接字，SSL加密通信、异步网络通信等功能</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2s1" data-idx="7-8"><span class="text_H038s" data-text="true">网络协议，支持HTTP，FTP，SMTP，POP，IMAP，NNTP，XMLRPC等多种网络协议，并提供了编写网络服务器的框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2tp" data-idx="7-9"><span class="text_H038s" data-text="true">W3C格式支持，包含HTML，SGML，XML的处理</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw2vc" data-idx="7-10"><span class="text_H038s" data-text="true">其它功能，包括国际化支持、数学运算、HASH、Tkinter等</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="8-1" data-tag="header" data-uuid="go6t5jykjy" data-level="2"><div class="anchorList_CiJE7"><a name="内置库"></a><a name="8-1"></a></div><h3>内置库</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5jyklq" data-idx="7-12"><span class="text_H038s" data-text="true">可以直接使用 import语句导入。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="8-2" data-tag="header" data-uuid="go6t5jyknh" data-level="2"><div class="anchorList_CiJE7"><a name="外部库"></a><a name="8-2"></a></div><h3>外部库</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5jykp9" data-idx="7-14"><span class="text_H038s" data-text="true">需要先下载，再在</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/CMD/1193011?fromModule=lemma_inlink" target="_blank" data-from-module="">CMD</a></span><span class="text_H038s" data-text="true">命令窗口在pip.exe的同级目录下输入 pip install 库名。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5jykpy" data-idx="7-15"><span class="text_H038s" data-text="true">main环境的外部库储存在python安装目录的Lib/site-packages文件夹中。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="tRxhc6EDblEj" data-idx="7-16"><span class="text_H038s" data-text="true">virtualenv的外部库在C:\Users\<USER>\Envs\ENV环境名\Lib\site-packages文件夹中。</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="9" data-tag="header" data-uuid="go6sydqx47" data-level="1"><div class="anchorList_CiJE7"><a name="开发环境"></a><a name="9"></a></div><h2 name="9">开发环境</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="9" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="9-1" data-tag="header" data-uuid="go6t5jykru" data-level="2"><div class="anchorList_CiJE7"><a name="工具"></a><a name="9-1"></a></div><h3>工具</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sygp73d" data-idx="8-2"><span class="text_H038s" data-text="true">●IDLE：Python内置IDE（随python安装包提供）</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t041tfh" data-idx="8-3"><span class="text_H038s" data-text="true">●PyCharm：详见百度百科PyCharm，由著名的JetBrains公司开发，带有一整套可以帮助用户在使用Python语言开发时提高其效率的工具，比如调试、语法高亮、Project管理、代码跳转、智能提示、自动完成、单元测试、版本控制。</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [10]<em id="sup-10"></em></sup></span><span class="text_H038s" data-text="true">此外，该IDE提供了一些高级功能，以用于支持Django框架下的专业Web开发。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3hd" data-idx="8-4"><span class="text_H038s" data-text="true">●Komodo和Komodo Edit：后者是前者的免费精简版</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t2effgf" data-idx="8-5"><span class="text_H038s" data-text="true">●Spyder：安装Anaconda自带的高级IDE</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3j2" data-idx="8-6"><span class="text_H038s" data-text="true">●PythonWin：ActivePython或pywin32均提供该IDE，仅适用于Windows</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t49rqdj" data-idx="8-7"><span class="text_H038s" data-text="true">●SPE（Stani&#x27;s Python Editor）：功能较多的自由软件，基于wxPython</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3mg" data-idx="8-8"><span class="text_H038s" data-text="true">●Ulipad：功能较全的自由软件，基于wxPython；作者是中国Python高手limodou</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3o4" data-idx="8-9"><span class="text_H038s" data-text="true">●WingIDE：可能是功能最全的IDE，但不是自由软件（教育用户和开源用户可以申请免费key）</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3pt" data-idx="8-10"><span class="text_H038s" data-text="true">●Eric：基于PyQt的自由软件，功能强大。全名是：The Eric Python IDE</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syatmyh" data-idx="8-11"><span class="text_H038s" data-text="true">●DrPython</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syl3w0l" data-idx="8-12"><span class="text_H038s" data-text="true">●PyScripter：使用Delphi开发的轻量级的开源Python IDE，支持Python2.6和3.0。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sym6xlx" data-idx="8-13"><span class="text_H038s" data-text="true">●PyPE：一个开源的跨平台的PythonIDE。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syl3w29" data-idx="8-14"><span class="text_H038s" data-text="true">●bpython：类Unix操作系统下使用curses库开发的轻量级的Python解释器。语法提示功能。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3t7" data-idx="8-15"><span class="text_H038s" data-text="true">●eclipse+pydev插件：方便调试程序</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3uw" data-idx="8-16"><span class="text_H038s" data-text="true">●emacs：自带python支持，自动补全、refactor等功能需要插件支持</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3wl" data-idx="8-17"><span class="text_H038s" data-text="true">●Vim：最新7.3版编译时可以加入python支持，提供python代码自动提示支持</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3ya" data-idx="8-18"><span class="text_H038s" data-text="true">●Visual Studio 2003+VisualPython：仅适用Windows，已停止维护，功能较差</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syatn0l" data-idx="8-19"><span class="text_H038s" data-text="true">●SlickEdit</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx3zz" data-idx="8-20"><span class="text_H038s" data-text="true">●Visual Studio 2010+Python Tools for Visual Studio</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sy4phy6" data-idx="8-21"><span class="text_H038s" data-text="true">●TextMate</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sy4phzv" data-idx="8-22"><span class="text_H038s" data-text="true">●Netbeans IDE</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szbveda" data-idx="8-23"><span class="text_H038s" data-text="true">●Sublime</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t2aw8ix" data-idx="8-24"><span class="text_H038s" data-text="true">●ipython</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6sxyx41n" data-idx="8-25"><span class="text_H038s" data-text="true">另外，诸如Notepad++、EditPlus、UltraEdit等通用的程序员文本编辑器软件也能对Python代码编辑提供一定的支持，比如代码自动着色、注释快捷键等，但是否够得上集成开发环境的水平，尚有待评估。</span></div><div class="paraTitle_zbAWA level-2_jB3sN MARK_MODULE" data-index="9-2" data-tag="header" data-uuid="go6szw46vf" data-level="2"><div class="anchorList_CiJE7"><a name="解释器"></a><a name="9-2"></a></div><h3>解释器</h3></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw68l" data-idx="8-27"><span class="text_H038s" data-text="true">Python是一门跨平台的脚本语言，Python规定了一个Python语法规则，实现了Python语法的解释程序就成为了Python的解释器。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6ad" data-idx="8-28"><span class="text_H038s" data-text="true">CPython（</span><span class="text_H038s bold_WEzZ7" data-text="true">C</span><span class="text_H038s" data-text="true">lassic</span><span class="text_H038s bold_WEzZ7" data-text="true">Python</span><span class="text_H038s" data-text="true">，也就是原始的</span><span class="text_H038s bold_WEzZ7" data-text="true">Python</span><span class="text_H038s" data-text="true">实现，需要区别于其他实现的时候才以CPython称呼；或解作</span><span class="text_H038s bold_WEzZ7" data-text="true">C</span><span class="text_H038s" data-text="true">语言实现的</span><span class="text_H038s bold_WEzZ7" data-text="true">Python</span><span class="text_H038s" data-text="true">）。这是最常用的Python版本。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6co" data-idx="8-29"><span class="text_H038s" data-text="true">Jython（原名</span><span class="text_H038s bold_WEzZ7" data-text="true">JPython</span><span class="text_H038s" data-text="true">；</span><span class="text_H038s bold_WEzZ7" data-text="true">J</span><span class="text_H038s" data-text="true">ava语言实现的P</span><span class="text_H038s bold_WEzZ7" data-text="true">ython</span><span class="text_H038s" data-text="true">，现已正式发布）。Jython可以直接调用Java的各种函数库。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6eg" data-idx="8-30"><span class="text_H038s" data-text="true">PyPy（使用</span><span class="text_H038s bold_WEzZ7" data-text="true">Py</span><span class="text_H038s" data-text="true">thon语言写的</span><span class="text_H038s bold_WEzZ7" data-text="true">Py</span><span class="text_H038s" data-text="true">thon）</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6g8" data-idx="8-31"><span class="text_H038s" data-text="true">IronPython（面向.NET和ECMA CLI的Python实现）。IronPython能够直接调用.net平台的各种函数库。可以将Python程序编译成.net程序。</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6hz" data-idx="8-32"><span class="text_H038s" data-text="true">ZhPy（周蟒）（支持使用繁/简中文语句编写程序的</span><span class="text_H038s bold_WEzZ7" data-text="true">Py</span><span class="text_H038s" data-text="true">thon语言）</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="10" data-tag="header" data-uuid="go6syyw6jq" data-level="1"><div class="anchorList_CiJE7"><a name="著名应用"></a><a name="10"></a></div><h2 name="10">著名应用</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="10" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t40pe1z" data-idx="9-1"><span class="text_H038s" data-text="true">Digwebs-</span><span class="text_H038s" data-text="true"><a class="innerLink_UtiNv" href="/item/Web%E5%BA%94%E7%94%A8%E6%A1%86%E6%9E%B6/4262233?fromModule=lemma_inlink" target="_blank" data-from-module="">Web应用框架</a></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6li" data-idx="9-2"><span class="text_H038s" data-text="true">Pylons-Web应用框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6n9" data-idx="9-3"><span class="text_H038s" data-text="true">Zope-应用服务器</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6op" data-idx="9-4"><span class="text_H038s" data-text="true">Plone-内容管理系统</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6qi" data-idx="9-5"><span class="text_H038s" data-text="true">Django-鼓励快速开发的Web应用框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szmmb6y" data-idx="9-6"><span class="text_H038s" data-text="true">Uliweb-国人开发的轻量级Web框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6sa" data-idx="9-7"><span class="text_H038s" data-text="true">TurboGears-另一个Web应用快速开发框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6u1" data-idx="9-8"><span class="text_H038s" data-text="true">Twisted-Python的网络应用程序框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw6yl" data-idx="9-9"><span class="text_H038s" data-text="true">flask-Python 微Web框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t08nx2c" data-idx="9-10"><span class="text_H038s" data-text="true">tornado-非阻塞式服务器</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw70d" data-idx="9-11"><span class="text_H038s" data-text="true">Webpy-Python 微Web框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw725" data-idx="9-12"><span class="text_H038s" data-text="true">Bottle-Python 微Web框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6syyw73x" data-idx="9-13"><span class="text_H038s" data-text="true">EVE-网络游戏EVE大量使用Python进行开发</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5ea" data-idx="9-14"><span class="text_H038s" data-text="true">Reddit-社交分享网站</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5g0" data-idx="9-15"><span class="text_H038s" data-text="true">Dropbox-文件分享服务</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5jykza" data-idx="9-16"><span class="text_H038s" data-text="true">TurboGears-另一个Web应用快速开发框架</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5l1" data-idx="9-17"><span class="text_H038s" data-text="true">Fabric-用于管理成百上千台Linux主机的程序库</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5mp" data-idx="9-18"><span class="text_H038s" data-text="true">Trac-使用Python编写的BUG管理系统</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5o3" data-idx="9-19"><span class="text_H038s" data-text="true">Mailman-使用Python编写的邮件列表软件</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5pq" data-idx="9-20"><span class="text_H038s" data-text="true">Mezzanine-基于Django编写的内容管理系统</span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6szrz5re" data-idx="9-21"><span class="text_H038s" data-text="true">Blender-以C与Python开发的开源3D绘图软件</span></div><div class="paraTitle_zbAWA level-1_pZduX" data-index="11" data-tag="header" data-uuid="go6sz0rbt5" data-level="1"><div class="anchorList_CiJE7"><a name="学习网站"></a><a name="11"></a></div><h2 name="11">学习网站</h2><div class="titleLine_h5uq3"></div><span><span data-tts-catalog="11" data-tts-from="paragraph" class="ttsBtn_fORkB paragraph_HRm71"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="M7.878 14.133c-.3 0-.7-.1-.9-.3l-2.8-2.5h-1.6c-.8 0-1.4-.7-1.4-1.4v-3.8c0-.8.6-1.4 1.4-1.4h1.6l2.7-2.4c.6-.5 1.4-.5 1.9.1.3.2.4.5.4.9v9.5c0 .7-.6 1.3-1.3 1.3zm-5.3-8.2c-.1 0-.2.1-.2.2v3.8c0 .1.1.2.2.2h1.4c.3 0 .6.1.9.3l2.8 2.5c.1.1.4 0 .4-.2v-9.4c0-.1 0-.1-.1-.2s-.2-.1-.3 0l-2.7 2.4c-.3.3-.6.4-.9.4h-1.5zm10.3 6.8c-.1 0-.3 0-.4-.2-.2-.2-.2-.6 0-.8.9-1 1.4-2.3 1.4-3.7 0-1.4-.5-2.7-1.4-3.7-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0 1.1 1.2 1.7 2.8 1.7 4.4s-.6 3.3-1.7 4.4c-.1.3-.2.4-.4.4z"></path><path d="M11.378 10.733c-.1 0-.2 0-.3-.1-.3-.2-.3-.6-.2-.8.4-.5.6-1.1.6-1.8s-.2-1.3-.6-1.8c-.1-.2-.1-.6.2-.8.2-.2.6-.1.8.2.5.7.8 1.5.8 2.4 0 .9-.3 1.7-.8 2.4-.1.2-.3.3-.5.3z"></path></svg><span>播报</span></span></span><div class="editLemma_NeO8B"><svg viewBox="0 0 16 16" fill="currentColor" width="1em" height="1em"><path d="m14 3.8-1.9-1.9c-.3-.3-.7-.4-1.1-.4-.4 0-.8.2-1.1.4l-8.4 8.4c-.3.3-.4.7-.4 1.1v1.9c0 .8.7 1.5 1.5 1.5h1.9c.4 0 .8-.2 1.1-.4L14 5.9c.6-.6.6-1.6 0-2.1zm-9.1 9.7c-.1.1-.2.1-.3.1H2.7c-.2 0-.4-.2-.4-.4v-1.9c0-.1 0-.2.1-.3l6.1-6 2.4 2.4-6 6.1zm8.3-8.4-1.5 1.5-2.4-2.4 1.5-1.5c.1-.1.2-.1.3-.1s.2 0 .3.1l1.9 1.9c.1.1.1.3-.1.5z"></path></svg><span class="text_ERm4v">编辑</span></div></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t4hqeax" data-idx="10-1"><span class="text_H038s" data-text="true">Python官方文档</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [11]<em id="sup-11"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="go6t5jyl2p" data-idx="10-2"><span class="text_H038s" data-text="true">Python官网</span><span class="supWrap_C9i5o J-supWrap"><sup data-tag="ref"> [9]<em id="sup-9"></em></sup></span></div><div class="para_YYuCh content_uczdI MARK_MODULE" data-tag="paragraph" data-uuid="ulFUU53dN19l" data-idx="10-3"><span class="text_H038s" data-text="true">菜鸟教程</span></div><div class="J-pgc-after-content"></div></div></div></div></div></div></div><div style="background:#f5f5f5;padding:40px 0 10px"><div class="page-footer-content"><div class="fresh-block block-item"><div class="block-title"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024"><path d="M512 32C247.04 32 32 247.04 32 512s215.04 480 480 480 480-215.04 480-480S776.96 32 512 32m0 853.12c-206.08 0-373.12-167.68-373.12-373.12S305.92 138.88 512 138.88a373.12 373.12 0 1 1 0 746.24"></path><path d="M518.4 260.48c-49.28 0-88.96 14.72-118.4 42.88-30.08 28.8-44.8 68.48-44.8 117.76 0 1.92 0 21.76 14.08 37.12 6.4 7.04 18.56 14.72 38.4 14.72 38.4 0 54.4-30.08 55.68-51.2 0-21.12 3.84-37.12 11.52-47.36 4.48-6.4 13.44-15.36 39.68-15.36 15.36 0 26.24 3.84 33.28 10.24 7.68 7.68 11.52 19.2 11.52 32.64 0 10.24-3.84 20.48-11.52 29.44l-5.12 6.4c-42.24 38.4-65.92 64.64-74.88 82.56-9.6 18.56-14.08 40.32-14.08 67.84v8.32c0 17.28 14.08 42.88 53.12 42.88 40.32 0 53.76-26.88 55.68-42.88v-8.32c0-10.88 1.92-20.48 7.04-30.08 4.48-8.96 10.88-16.64 19.2-23.68 26.88-23.68 46.08-40.96 54.4-50.56 17.28-23.04 26.24-51.84 26.24-85.76 0-42.24-14.08-76.16-42.24-101.12-28.16-23.68-64-36.48-108.8-36.48M453.12 711.68a55.68 55.68 0 1 0 111.36 0 55.68 55.68 0 1 0-111.36 0"></path></svg><span>新手上路</span></div><div class="block-content"><a target="_blank" href="/usercenter/tasks#guide">成长任务</a><a target="_blank" href="/help#main01">编辑入门</a><a target="_blank" href="/help#main06">编辑规则</a><a target="_blank" href="/item/百度百科：本人词条编辑服务/22442459?bk_fr=pcFooter">本人编辑<img class="block-content-new-icon" src="https://baikebcs.bdimg.com/baike-react/common/new.png" alt="new"/></a></div></div><div class="question-block block-item"><div class="block-title"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024"><path d="M841.322 87.089h-3.9L541.113 167.9v-.031h-57.292L186.577 86.803h-3.898c-47.329 0-85.833 38.504-85.833 85.834v595.568c0 22.92 8.948 43.111 25.875 58.393 12.931 11.674 30.025 20.311 52.204 26.391 1.558.43 156.07 43.113 307.962 83.951l.956.258h55.15l2.121-.559c154.5-40.689 306.48-82.951 308.012-83.379l1.078-.322c21.422-6.861 37.787-15.387 50.029-26.064 17.613-15.363 26.922-35.551 26.922-58.381v-595.57c-.001-47.328-38.505-85.834-85.833-85.834M190.375 796.848c-35.303-9.676-35.303-22.967-35.303-28.643V172.637c0-14.134 10.678-25.822 24.39-27.421l303.424 82.752v648.673c-146.655-39.502-290.952-79.362-292.511-79.793m678.553-28.356c0 5.154 0 17.191-35.969 28.826-9.916 2.756-147.693 41.006-291.846 79.1V228.254l303.424-82.751c13.713 1.599 24.391 13.287 24.391 27.421z"></path></svg><span>我有疑问</span></div><div class="block-content"><a>内容质疑</a><a target="_blank" href="https://ufosdk.baidu.com/bailingPC/getEntryPath/aKo-PBP84zlnBbgvj2STxzZNwlrmWT8XVd-PzIQ3C-c=" rel="noreferrer">在线客服</a><a target="_blank" href="http://tieba.baidu.com/f?ie=utf-8&amp;fr=bks0000&amp;kw=%E7%99%BE%E5%BA%A6%E7%99%BE%E7%A7%91" rel="noreferrer">官方贴吧</a><a>意见反馈</a></div></div><div class="suggestion-block block-item"><div class="block-title"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024"><path d="M874 80.5H150.7c-47.4 0-86 38.6-86 86v550c0 1.2.1 2.5.2 3.7l-.2 192.2c0 14.5 8.7 27.7 22.2 33.3 4.5 1.9 9.2 2.8 13.8 2.8 9.3 0 18.5-3.6 25.4-10.5l136.2-135.4H874c47.4 0 86-38.6 86-86v-550c0-47.5-38.6-86.1-86-86.1m14 636c0 7.7-6.3 14-14 14H249.5c-9.8-.5-19.8 2.9-27.4 10.4l-85.4 84.8.1-109.2c0-1.3-.1-2.5-.2-3.7V166.5c0-7.7 6.3-14 14-14H874c7.7 0 14 6.3 14 14z"></path><path d="M750.3 297h-476c-19.9 0-36 16.1-36 36s16.1 36 36 36h475.9c19.9 0 36-16.1 36-36s-16.1-36-35.9-36M505.5 513.4H274.3c-19.9 0-36 16.1-36 36s16.1 36 36 36h231.2c19.9 0 36-16.1 36-36s-16.1-36-36-36"></path></svg><span>投诉建议</span></div><div class="block-content"><a target="_blank" href="http://help.baidu.com/newadd?prod_id=10&amp;category=1" rel="noreferrer">举报不良信息</a><a target="_blank" href="http://help.baidu.com/newadd?prod_id=10&amp;category=2" rel="noreferrer">未通过词条申诉</a><a target="_blank" href="http://help.baidu.com/newadd?prod_id=10&amp;category=6" rel="noreferrer">投诉侵权信息</a><a target="_blank" href="http://help.baidu.com/newadd?prod_id=10&amp;category=5" rel="noreferrer">封禁查询与解封</a></div></div></div><div class="copyright"><p>©2024 Baidu <a href="http://www.baidu.com/duty/" target="_blank" rel="noreferrer">使用百度前必读</a> | <a href="http://help.baidu.com/question?prod_en=baike&amp;class=89&amp;id=1637" target="_blank" rel="noreferrer">百科协议</a> | <a href="http://help.baidu.com/question?prod_id=10&amp;class=690&amp;id=1001779" target="_blank" rel="noreferrer">隐私政策</a> | <a href="/operation/cooperation" target="_blank">百度百科合作平台</a> | <span>京ICP证030173号 </span><img class="copyright-img" width="13" height="16" src="https://ss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/copy_rignt_24.png"/></p><p class="recordcode"><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11000002000001" target="_blank" rel="noreferrer"><i class="icon-police"></i>京公网安备11000002000001号</a></p></div></div></div></div><script>window.PAGE_DATA= {"lemmaId":407313,"lemmaTitle":"Python","lemmaDesc":"计算机编程语言","lemmaType":0,"uid":4903591,"uname":"馥红","createTime":1145633749,"updateTime":1725970113,"isDefault":1,"isDeleted":0,"rank":1,"extData":{"LemmaId":21087,"audit_comment_category":390,"categoryTag":"开发工具,付费软件,电脑软件,Windows软件/MacOS软件","classify":[{"id":1033565,"idPath":"1033565/1033588/1033740","leafId":1033740,"leafName":"软件","mainCategory":1,"name":"应用科学","path":"应用科学/计算机科学/软件"}],"discussion":{"relateInfo":[{"relateId":32990,"relateType":1},{"relateId":33010,"relateType":1},{"relateId":33068,"relateType":1},{"relateId":33347,"relateType":1}]},"helloEarth":1,"isBaikeV6":1,"isFrontV6":1,"isSapp":0,"isWaitForPersonal":false,"is_locked":0,"kpdClassify":[{"id":337499,"name":"软件"}],"lemmaId":21087,"lemma_id":21087,"newEditor":1,"pass_count":504,"personalWhiteListLemma":1,"subLemmaId":21087,"uip":"************","updateTime":1725970114,"wapEdit":0},"extFlag":0,"versionId":606990855,"versionUid":5468858911,"versionUname":"520XuYueming","versionCreateTime":1725764186,"catalog":[{"level":1,"title":"发展历程","index":"1","uuid":"go6t5j6l59","height":28},{"level":1,"title":"语言特点","index":"2","uuid":"go6t5jybc3","height":28},{"level":2,"title":"优点","index":"2-1","uuid":"thsEIjTByWth","height":21},{"level":2,"title":"缺点","index":"2-2","uuid":"thsEGk5ttKTG","height":21},{"level":1,"title":"基本语法","index":"3","uuid":"go6szw3y7h","height":28},{"level":2,"title":"控制语句","index":"3-1","uuid":"go6t0jj050","height":21},{"level":2,"title":"表达式","index":"3-2","uuid":"go6syyvvyx","height":21},{"level":2,"title":"函数","index":"3-3","uuid":"go6syyvwvf","height":21},{"level":2,"title":"对象的方法","index":"3-4","uuid":"go6syyvwyv","height":21},{"level":2,"title":"数据类型","index":"3-5","uuid":"go6syyvx58","height":21},{"level":2,"title":"数学运算","index":"3-6","uuid":"go6syyw21e","height":21},{"level":1,"title":"帮助","index":"4","uuid":"go6t2s2q82","height":28},{"level":1,"title":"接口","index":"5","uuid":"go6t5jyikb","height":28},{"level":2,"title":"服务器","index":"5-1","uuid":"go6t1zfwxk","height":21},{"level":2,"title":"程序","index":"5-2","uuid":"go6t1zfx7b","height":21},{"level":2,"title":"环境变量","index":"5-3","uuid":"go6t1zfxgx","height":21},{"level":1,"title":"应用领域","index":"6","uuid":"go6t5jyjrh","height":28},{"level":1,"title":"开发工具","index":"7","uuid":"go6t5jykdt","height":28},{"level":1,"title":"标准库","index":"8","uuid":"go6syyw2f2","height":28},{"level":2,"title":"内置库","index":"8-1","uuid":"go6t5jykjy","height":21},{"level":2,"title":"外部库","index":"8-2","uuid":"go6t5jyknh","height":21},{"level":1,"title":"开发环境","index":"9","uuid":"go6sydqx47","height":28},{"level":2,"title":"工具","index":"9-1","uuid":"go6t5jykru","height":21},{"level":2,"title":"解释器","index":"9-2","uuid":"go6szw46vf","height":21},{"level":1,"title":"著名应用","index":"10","uuid":"go6syyw6jq","height":28},{"level":1,"title":"学习网站","index":"11","uuid":"go6sz0rbt5","height":28}],"abstract":[],"abstractAlbum":{"content":[{"albumId":1,"src":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","desc":"Python logo","width":2000,"height":2000,"uuid":"fdojo4ubsb"}],"desc":"Python的概述图","total":1,"coverPic":{"albumId":1,"url":"https://bkimg.cdn.bcebos.com/pic/b03533fa828ba61ea8d3c8f6227f800a304e241ff39d?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70","src":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","origSrc":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","desc":"Python logo","width":2000,"height":2000,"uuid":"fdojo4ubsb"},"wapPic":{"src":"71cf3bc79f3df8dcd100fc48ae5a658b4710b812829d","origSrc":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","width":2003,"height":1334,"uuid":"wap2829d"},"sharePic":{"src":"fd039245d688d43f8794f72b1e55c51b0ef41ad58d9d","origSrc":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","width":2000,"height":2000,"uuid":"share58d9d"}},"reference":[{"type":1,"title":"历史和许可证 — Python 3.9.1 文档","site":"Python官方","publishDate":"2020-01-05","refDate":"2020-12-28","index":1,"uuid":"go6t5htbdg","encodeUrl":"533aYdO6cr3_z3kATKCIzK_yMi7EZIj4t-aHB7FzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FRccvaJOMrgrGvF_wQ"},{"type":1,"title":"Python 教程 — Python 3.9.1 文档","site":"Python官方","publishDate":"2020-10-05","refDate":"2020-12-28","index":2,"uuid":"go6t4n43vd","encodeUrl":"533aYdO6cr3_z3kATPzezK3yYX7CYIiv7LHQUORzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FQ8AqqhSKLxpXex8ySO3TWuGlOI"},{"type":1,"title":"Python 3破冰人工智能 从入门到实战 (豆瓣)","site":"豆瓣","publishDate":"2019-05-01","refDate":"2020-12-22","index":3,"uuid":"go6t2c9b1x","encodeUrl":"533aYdO6cr3_z3kATKaKy_r1N3mQY96lvLzVA7JzzqIPmGapB4zqVYN85Ngq_PZpWgjEvddxddQfk-u-FUhG6vcYduo3XQ"},{"type":1,"title":"Python 教程 — Python 3.9.1 文档","site":"Python官方","publishDate":"2020-01-05","refDate":"2021-01-16","index":4,"uuid":"go6t5jt00k","encodeUrl":"533aYdO6cr3_z3kATPzezK3yYX7CYIiv7LHQUORzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FQ8AqqhSKLxpXex8ySO3TWuGlOI"},{"type":1,"title":"Python 3.9 有什么新变化 — Python 3.9.1 文档","site":"Python 官方","publishDate":"2020-12-28","refDate":"2020-12-28","index":5,"uuid":"go6t4878nx","encodeUrl":"533aYdO6cr3_z3kATPCIyKn3YXzHMdT57LSBBLNzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FQwdv7NTL7hyXbY8lGinF26e"},{"type":2,"title":"自学Python：编程基础、科学计算及数据分析","author":"李金","publisher":"机械工业出版社","place":"北京","publishYear":"2018","index":6,"uuid":"go6t3vraej"},{"type":1,"title":"Python CGI 编程","site":"自强学堂","refDate":"2014-01-22","index":7,"uuid":"go6t30r9ba","encodeUrl":"533aYdO6cr3_z3kATKGIyf7yYHnHNdSruLyBBrVzzqIP0XOpX5nyFJI78d4-8PB_AQ7fsZZlLtUam6e6Qw8dsakPMaRxGup8gCWoCi2ajeO6"},{"type":1,"title":"python tutorial中文","site":"python中文文档","refDate":"2013-02-20","index":8,"uuid":"go6t5j07pf","encodeUrl":"533aYdO6cr3_z3kATPOIzq_yOnrAYNWsvrOBBrJzzqIP0XOpX5nyFJgr9N8w8PNoF0XIv5U"},{"type":1,"title":"官网首页","site":"python官网","refDate":"2018-01-5","index":9,"uuid":"go6szcundo","encodeUrl":"533aYdO6cr3_z3kATKaJmailYC7NNYyp7LzUBLBzzqIPmGapB5nyTcYi-cM38fkpGxnM"},{"type":1,"title":"PyCharm产品官网","site":"JetBrains的PyCharm产品官网","refDate":"2013-09-10","index":10,"uuid":"go6t041b4c","encodeUrl":"533aYdO6cr3_z3kATP2Kya3zYHuVZNn_t7zUUbRzzqIP0XOpX5nyFII39NUt__5pB0XIv5UtcM8Wnum4V1Q"},{"type":1,"title":"Python3 官方文档中文版","site":"Python3 官方文档","refDate":"2019-12-9","index":11,"uuid":"go6t4hjybj","encodeUrl":"533aYdO6cr3_z3kATKXfzPijYC-VNd__ueHQVuBzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FQ"},{"type":1,"title":"Changelog — Python 3.9.2 documentation","site":"Python.org","refDate":"2021-03-13","index":12,"uuid":"2JvHHXAjI3w","encodeUrl":"533aYdO6cr3_z3kATPKPnqn4NS7CY92k7bLTUrZzzqIPmGapB4rqWZt88M4r9vhpWgTZt9dwZdoQl_uvFUhb5-kSbqptE_FhwyO4TGCamOCxqoVuzc8c59cfS_5P1q-m4xijhQ"},{"type":1,"title":"Python Release Python 3.13.0a4 | Python.org","site":"python官网","publishDate":"2024-02-15","refDate":"2023-02-24","index":13,"uuid":"tqZ20BRMJDua","encodeUrl":"533aYdO6cr3_z3kATPyPyPzxZC-SM9-q7-eGUOBzzqIPmGapB5nyTcYi-cM38fkpGxnM_5xtd9gZmemuSVQHu6tFIK5gXfVr2S6gDS7ByL3mrdsu"},{"type":1,"title":"Python Release Python 3.13.0a4 | Python.org","site":"python官网","publishDate":"2024-02-06","refDate":"2023-02-24","index":14,"uuid":"tqZ2dOLBrhSz","encodeUrl":"533aYdO6cr3_z3kATPyJnfv3ZCvHNIuk7eKAVrRzzqIPmGapB5nyTcYi-cM38fkpGxnM_5xtd9gZmemuSVQHu6tFIK5gXfVr2S6gDS7ByLzi4A"},{"type":3,"text":"王道平，沐嘉慧编. 大数据管理与应用系列教材 数据科学与大数据技术导论[M]. 北京：机械工业出版社, 2021.07.148","isPublic":"1","index":15,"uuid":"ucusNGfosA8m","imgs":[{"url":"0b55b319ebc4b74543a9e37f4ca409178a82b901bf18","viewUrl":"https://bkimg.cdn.bcebos.com/pic/0b55b319ebc4b74543a9e37f4ca409178a82b901bf18?x-bce-process=image/watermark,text_55m-5bqm55m-56eRCjBiNTViMzE5ZWJjNGI3NDU0M2E5ZTM,g_3,x_10,y_10,type_RlpLYWk=,a_-30,size_28,color_00000040,skc_ffffff40,skw_1,layout_tile","previewUrl":"https://bkimg.cdn.bcebos.com/pic/0b55b319ebc4b74543a9e37f4ca409178a82b901bf18?x-bce-process=image/resize,m_lfit,w_235,h_235,limit_1"}]}],"modules":{"ability":["business_deny_feed_native_video","second_deny_upload_video"],"adManager":{"data":{"discussionHelpEdit":0,"pcFengchaoGuess":0,"pcWapDireactAd":0,"secondKnowUploadBList":0,"wapRelatedBusiness":1}},"discussion":{"showHelpEdit":1},"globalConfig":{"data":{"serviceAdLemmaCategory":[337597,337474]}},"paper":{"switch":false},"second":{"head":[{"secondId":56563315,"type":1,"form":1,"style":1,"source":1,"sourceId":"4759456910754957111","authorType":3,"platform":1,"lemma":{"relType":1,"relValue":[],"te":1717068619,"tr":["秒懂精品"],"ts":1685532770,"tw":1,"type":1,"weight":0},"score":3,"createTime":1649818813,"publishTime":1649819414,"isPay":0,"collectionId":0}]},"starMapMount":{"data":{"isShow":true,"manualInfo":{"starmapIds":[210632,210642]},"tags":{"themeId":210642,"themeName":"常见的脚本语言"}}},"pinzhuan":{}},"albums":[{"albumId":1,"desc":"Python logo","total":1,"coverPic":{"albumId":1,"src":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","url":"https://bkimg.cdn.bcebos.com/pic/b03533fa828ba61ea8d3c8f6227f800a304e241ff39d?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70","width":2000,"height":2000,"desc":"Python logo","uuid":"fdojo4ubsb"},"content":[{"albumId":1,"src":"b03533fa828ba61ea8d3c8f6227f800a304e241ff39d","url":"","width":2000,"height":2000,"desc":"Python logo","uuid":"fdojo4ubsb"}]},{"albumId":0,"desc":"词条图片","total":2,"coverPic":{"albumId":0,"tag":"image","type":"normal","src":"faedab64034f78f092033e1079310a55b2191ccc","url":"https://bkimg.cdn.bcebos.com/pic/faedab64034f78f092033e1079310a55b2191ccc?x-bce-process=image/resize,m_lfit,w_220,h_220,limit_1","width":255,"height":137,"cropWidth":220,"cropHeight":118.19608,"owner":"Domesticater","title":"标识","layout":"right","style":"float: right;","uuid":"4uxjykyfk0f"},"content":[{"albumId":0,"tag":"image","type":"normal","src":"faedab64034f78f092033e1079310a55b2191ccc","url":"https://bkimg.cdn.bcebos.com/pic/faedab64034f78f092033e1079310a55b2191ccc?x-bce-process=image/resize,m_lfit,w_220,h_220,limit_1","width":255,"height":137,"cropWidth":220,"cropHeight":118.19608,"owner":"Domesticater","title":"标识","layout":"right","style":"float: right;","uuid":"4uxjykyfk0f"},{"albumId":0,"tag":"image","type":"normal","src":"8b82b9014a90f6032aaa09763512b31bb051eded","url":"https://bkimg.cdn.bcebos.com/pic/8b82b9014a90f6032aaa09763512b31bb051eded?x-bce-process=image/resize,m_lfit,w_220,h_220,limit_1","width":480,"height":260,"cropWidth":220,"cropHeight":119.16667,"owner":"Domesticater","title":"Python","layout":"right","style":"float: right;","uuid":"ea646418k52"}]},{"albumId":11,"desc":"参考资料图册","total":1,"coverPic":{"albumId":11,"src":"0b55b319ebc4b74543a9e37f4ca409178a82b901bf18","url":"https://bkimg.cdn.bcebos.com/pic/0b55b319ebc4b74543a9e37f4ca409178a82b901bf18?x-bce-process=image/watermark,text_55m-5bqm55m-56eRCjBiNTViMzE5ZWJjNGI3NDU0M2E5ZTM,g_3,x_10,y_10,type_RlpLYWk=,a_-30,size_28,color_00000040,skc_ffffff40,skw_1,layout_tile","width":0,"height":0,"uuid":"ucusNGfosA8m"},"content":[{"albumId":11,"src":"0b55b319ebc4b74543a9e37f4ca409178a82b901bf18","url":"https://bkimg.cdn.bcebos.com/pic/0b55b319ebc4b74543a9e37f4ca409178a82b901bf18?x-bce-process=image/watermark,text_55m-5bqm55m-56eRCjBiNTViMzE5ZWJjNGI3NDU0M2E5ZTM,g_3,x_10,y_10,type_RlpLYWk=,a_-30,size_28,color_00000040,skc_ffffff40,skw_1,layout_tile","width":0,"height":0,"uuid":"ucusNGfosA8m"}]}],"card":{"type":140,"left":[{"key":"name","title":"软件名称","isArray":false,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":"Python"}]}]},{"key":"platform","title":"软件平台","isArray":true,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":" "},{"lemmaId":852149,"tag":"innerlink","text":"Windows操作系统"},{"tag":"text","text":"、"},{"lemmaId":27050,"tag":"innerlink","text":"Linux"},{"tag":"text","text":"、"},{"lemmaId":219943,"tag":"innerlink","text":"UNIX"},{"tag":"text","text":"、"},{"lemmaId":8654551,"tag":"innerlink","text":"MacOS"},{"tag":"text","text":"等"}]}]},{"key":"dateOfRelease","title":"上线时间","isArray":false,"delimiter":"","data":[{"dataType":"dateTime","text":[{"tag":"text","text":"1991年"}],"unit":"AD","value":{"day":"","hour":"","minitue":"","month":"","second":"","year":"1991"}}]},{"key":"dateOfLatestUpdate","title":"最近更新时间","isArray":false,"delimiter":"","data":[{"dataType":"dateTime","text":[{"tag":"text","text":"2023年6月6日"}],"unit":"AD","value":{"day":"6","hour":"","minitue":"","month":"6","second":"","year":"2023"}}]},{"key":"developingLanguage","title":"软件语言","isArray":false,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":"Python"}]}]},{"key":"developer","title":"开发商","isArray":false,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":"Python Software Foundation"}]}]},{"key":"licensingMethod","title":"软件授权","isArray":false,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":"Python Software Foundation"},{"ctrId":"","data":{"encodeUrl":"533aYdO6cr3_z3kATKCIzK_yMi7EZIj4t-aHB7FzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FRccvaJOMrgrGvF_wQ","index":1,"publishDate":"2020-01-05","refDate":"2020-12-28","site":"Python官方","title":"历史和许可证 — Python 3.9.1 文档","type":1,"uuid":"go6t5htbdg"},"index":1,"old":false,"tag":"ref","uuid":"go6t5htbdg"}]}]}],"right":[{"key":"latestVersion","title":"软件版本","isArray":true,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":"python2.x"},{"ctrId":"","data":{"encodeUrl":"533aYdO6cr3_z3kATPCIyKn3YXzHMdT57LSBBLNzzqIPmGapB4rqWZt88M4r9vhpWgTZt9d4aJsWmKf5FQwdv7NTL7hyXbY8lGinF26e","index":5,"publishDate":"2020-12-28","refDate":"2020-12-28","site":"Python 官方","title":"Python 3.9 有什么新变化 — Python 3.9.1 文档","type":1,"uuid":"go6t4878nx"},"index":5,"old":false,"tag":"ref","uuid":"go6t4878nx"},{"ctrId":"","data":{"encodeUrl":"533aYdO6cr3_z3kATPKPnqn4NS7CY92k7bLTUrZzzqIPmGapB4rqWZt88M4r9vhpWgTZt9dwZdoQl_uvFUhb5-kSbqptE_FhwyO4TGCamOCxqoVuzc8c59cfS_5P1q-m4xijhQ","index":12,"refDate":"2021-03-13","site":"Python.org","title":"Changelog — Python 3.9.2 documentation","type":1,"uuid":"2JvHHXAjI3w"},"index":12,"old":false,"tag":"ref","uuid":"2JvHHXAjI3w"}]},{"dataType":"text","text":[{"tag":"text","text":"python3.x"}]}]},{"key":"softwareSize","title":"软件大小","isArray":false,"delimiter":"","data":[{"dataType":"text","text":[{"tag":"text","text":"26 至 29 MB"}]}]},{"key":"customDefault","title":"是否区分大小写","isArray":false,"delimiter":"","data":[{"dataType":"enum","text":[{"tag":"text","text":"是"}],"value":"是"}]},{"key":"founder","title":"创始人","isArray":true,"delimiter":"、","data":[{"dataType":"lemma","text":[{"lemmaId":328361,"tag":"innerlink","text":"吉多·范罗苏姆"}],"value":{"id":"328361","title":"吉多·范罗苏姆"}}]},{"key":"customDefault","title":"最新正式版本","isArray":false,"delimiter":"","data":[{"dataType":"enum","text":[{"tag":"text","text":"Python 3.12.4"},{"ctrId":"","data":{"encodeUrl":"533aYdO6cr3_z3kATPyJnfv3ZCvHNIuk7eKAVrRzzqIPmGapB5nyTcYi-cM38fkpGxnM_5xtd9gZmemuSVQHu6tFIK5gXfVr2S6gDS7ByLzi4A","index":14,"publishDate":"2024-02-06","refDate":"2023-02-24","site":"python官网","title":"Python Release Python 3.13.0a4 | Python.org","type":1,"uuid":"tqZ2dOLBrhSz"},"index":14,"old":false,"tag":"ref","uuid":"tqZ2dOLBrhSz"}],"value":"Python 3.12.4"}]},{"key":"customDefault","title":"最新测试版本","isArray":false,"delimiter":"","data":[{"dataType":"enum","text":[{"tag":"text","text":"Python 3.13.0b2"},{"ctrId":"","data":{"encodeUrl":"533aYdO6cr3_z3kATPyPyPzxZC-SM9-q7-eGUOBzzqIPmGapB5nyTcYi-cM38fkpGxnM_5xtd9gZmemuSVQHu6tFIK5gXfVr2S6gDS7ByL3mrdsu","index":13,"publishDate":"2024-02-15","refDate":"2023-02-24","site":"python官网","title":"Python Release Python 3.13.0a4 | Python.org","type":1,"uuid":"tqZ20BRMJDua"},"index":13,"old":false,"tag":"ref","uuid":"tqZ20BRMJDua"}],"value":"Python 3.13.0b2"}]}]},"lemmaCnt":3,"navigation":{"word":"Python","disamLemma":{"lemmaId":22164521,"lemmaTitle":"Python","lemmaDesc":"Python","isRedirect":false,"redirectId":0,"redirectTitle":"","redirectDesc":"","rank":0,"isDefault":0,"isCurrent":0,"encodeId":"242a1308b00febde401c451c","redirectEncodeId":""},"lemmas":[{"lemmaId":407313,"lemmaTitle":"Python","lemmaDesc":"计算机编程语言","isRedirect":false,"redirectId":0,"redirectTitle":"","redirectDesc":"","rank":1,"classify":["数字产品"],"isDefault":1,"isCurrent":1,"encodeId":"242a1308b00febde401c451c","redirectEncodeId":"75a62f4118d970d3466e6c25"},{"lemmaId":22164520,"lemmaTitle":"Python","lemmaDesc":"英文单词","isRedirect":false,"redirectId":0,"redirectTitle":"","redirectDesc":"","rank":2,"classify":["术语"],"isDefault":0,"isCurrent":0,"encodeId":"9a7035920ee77d3f51cdea12","redirectEncodeId":"75a62f4118d970d3466e6c25"},{"lemmaId":62500179,"lemmaTitle":"Python","lemmaDesc":"Miguel演唱的歌曲","isRedirect":false,"redirectId":0,"redirectTitle":"","redirectDesc":"","rank":3,"classify":["音乐作品"],"isDefault":0,"isCurrent":0,"encodeId":"b9ad24c570c74cc6d50e5896","redirectEncodeId":"75a62f4118d970d3466e6c25"}],"categoryList":["数字产品","术语","音乐作品"]},"tips":{"isPolyseme":1,"disamTip":{"link":"/item/Python","title":"Python"}},"encodeLemmaId":"242a1308b00febde401c451c","versionUk":"onu7FilHWodOqtmgRofbcw","description":"Python由荷兰国家数学与计算机科学研究中心的吉多·范罗苏姆于1990年代初设计，作为一门叫做ABC语言的替代品。Python提供了高效的高级数据结构，还能简..."}</script><script>window.__abbaidu_2020_subidgetf||(window.__abbaidu_2020_subidgetf=function(){return"01000000"})</script><script async src="https://dlswbr.baidu.com/heicha/mw/abclite-2020-s.js"></script></body></html>