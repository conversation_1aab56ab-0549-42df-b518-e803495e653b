from fastapi import FastAPI, HTTPException
from jose import jwt
from datetime import datetime, timedelta
import sqlite3
from pydantic import BaseModel
import requests

app = FastAPI()

SECRET_KEY = "nRzInaP7nidgViZOimPD79NS1OF-TYZu5tjOf_vcVAA"
ALGORITHM = "HS256"

# 定义请求模型
class FileInfo(BaseModel):
    file_name: str
    file_url: str
    file_size: int

# 初始化数据库
def init_db():
    conn = sqlite3.connect('file.sqlite')
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS files
    (id INTEGER PRIMARY KEY AUTOINCREMENT,
     file_name TEXT,
     file_url TEXT,
     file_size INTEGER,
     timestamp DATETIME)
    ''')
    conn.commit()
    conn.close()

init_db()

@app.get("/file")
async def get_file():
    expiration = datetime.utcnow() + timedelta(minutes=5)
    payload = {
        "exp": expiration
    }
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    return {"token": token}

@app.post("/fileinfo")
async def post_fileinfo(file_info: FileInfo):
    try:
        conn = sqlite3.connect('file.sqlite')
        cursor = conn.cursor()
        
        # 获取当前时间戳
        timestamp = datetime.now().isoformat()
        
        # 插入数据
        cursor.execute('''
        INSERT INTO files (file_name, file_url, file_size, timestamp)
        VALUES (?, ?, ?, ?)
        ''', (file_info.file_name, file_info.file_url, file_info.file_size, timestamp))
        
        conn.commit()
        conn.close()
        resp = requests.get(f"https://sctapi.ftqq.com/SCT217624TA-Fsz8NXpAJUD203fDkFVdFHHV.send?title=文件上传通知&desp=文件名：{file_info.file_name}\n文件url：{file_info.file_url}\n文件大小：{file_info.file_size}")
        return {"message": "File info saved successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3000)

