import os
import base64

print('欢迎使用haorwen的音频转网页生成器')
input('请将所有的音频文件放入当前目录music文件夹中（注意要mp3格式！！），然后按回车键继续生成网页...')
passwd = input('请设定打开网页时要输入的密码（无法找回！请务必牢记）：')
def convert_audio_to_base64(filepath):
    with open(filepath, "rb") as audio_file:
        audio_base64bytes = base64.b64encode(audio_file.read())
        audio_base64str = audio_base64bytes.decode('utf-8')
    return audio_base64str

def generate_html(audio_dict):
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Base64 Music Player</title>
    <meta charset="utf-8" />
    <script type="text/javascript">   
loopy()   
function loopy() {{   
var sWord =""  
while (sWord != {}) {{//设置密码
sWord = prompt("输入完整性校验码：")   
}}   
alert("欢迎访问")   
}}   
</script>
</script>
</head>
<body>
""".format(passwd)
    html += f"""
    <div>
"""
    for filename, base64audio in audio_dict.items():
        html += f"<h2>{filename}</h2>\n"
        html += '<audio controls>\n'
        html += f'<source src="data:audio/mp3;base64,{base64audio}" type="audio/mp3">\n'
        html += 'Your browser does not support the audio element.\n'
        html += '</audio>\n'

    html += """
    </div>
</body>
</html>
"""
    with open('music_player.html', 'w', encoding='utf-8') as html_file:
        html_file.write(html)

def main(directory):
    audio_dict = {}
    for filename in os.listdir(directory):
        if filename.endswith('.mp3'):  # 或者使用其他音频文件扩展名
            filepath = os.path.join(directory, filename)
            base64audio = convert_audio_to_base64(filepath)
            audio_dict[filename] = base64audio
    generate_html(audio_dict)

main('musicnan')  # 替换'你的音频文件夹路径'为你的实际音频文件夹
