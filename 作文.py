# coding:utf-8
import os
from openai import OpenAI
import time
import concurrent.futures

api_key = "sk-daY1LRI2OTxF18Yu6569F196B1914d7eAfD1A9B6Cb59D06c"
api_base = "https://api.zyai.online/v1"

# 判断文件夹是否存在，不存在则创建
if not os.path.exists("作文"):
    os.mkdir("作文")

def write(material):
    client = OpenAI(api_key=api_key, base_url=api_base)
    timestamp = time.time()
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "user",
             "content": f"当前时间戳{timestamp}（请无视）你现在是一个高中生，坐在考场里，我会给你语文考试的作文材料，按照高考作文的格式来写，不要分小标题，加一点事例，用辩证的观点来写，不要非对即错，请写一篇议论文的大纲，题目自拟 材料：{material}"}
        ]
    )

    material =  completion.choices[0].message.content
    client = OpenAI(api_key=api_key, base_url=api_base)
    timestamp = time.time()
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "user",
             "content": f"当前时间戳{timestamp}（请无视）你现在是一个高中生，坐在考场里，我会给你语文考试的作文大纲，按照高考作文的格式来写，不要分小标题，加一点事例，用辩证的观点来写，不要非对即错，请写一篇议论文，题目自拟 大纲：{material}"}
        ]
    )

    return completion.choices[0].message.content

materials = [
    "一秒钟就能看清一件事本质的人，与花了半辈子也看不清的人相比，前者一定会有更好的命运吗?请自拟题目，谈谈你对这个问题的思考，不少于 800字。",
    "垂柳婆娑起舞，青松迎风而立，自然万物呈现出不同的美，这启发我们去思考生命的独特性与丰富性。请自拟题目，就此谈谈你的看法，不少于 800 字。",
    "生活中，人们常常在不确定性中寻找确定性。请自拟题目，谈谈你对这个问题的思考，不少于800字。",
    "原汁原味是传统文化最好的传承吗?请自拟题目，就此谈谈你的看法，不少于 800字。"
]

def save_essay(index, material):
    print(f"正在生成第 {index+1} 篇作文")
    content = write(material)
    with open(f"作文/{index+1}.txt", "w", encoding="utf-8") as f:
        f.write(content)
    print(f"第 {index+1} 篇作文生成完成")

# 使用并发请求四篇作文
with concurrent.futures.ThreadPoolExecutor() as executor:
    futures = [executor.submit(save_essay, i, material) for i, material in enumerate(materials)]
    for future in concurrent.futures.as_completed(futures):
        future.result()

print("所有作文生成完成")
