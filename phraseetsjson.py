import json

# Load the JSON content from the uploaded file
file_path = "download.json"
with open(file_path, "r", encoding="utf-8") as f:
    data = json.load(f)

# Prepare a list to collect the relevant information
results = []

# Iterate over the questions and extract those with answers
for item in data:
    course_title = item.get("body", {}).get("lvread_course_name", "")
    for question in item.get("body", {}).get("lvread_question", []):
        question_title = question.get("course_title", "")
        description = question.get("description", "")
        for child in question.get("children", []):
            # Check if there's a non-empty standard_answer
            standard_answer = child.get("standard_answer", "").strip()
            if standard_answer:
                results.append({
                    "course_title": course_title,
                    "question_title": question_title,
                    "description": description,
                    "answer": standard_answer
                })

# Prepare the markdown output
markdown_output = "# ETS分级阅读答案 by ha<PERSON><PERSON>\n\n"

for result in results:
    markdown_output += f"### 标题: {result['course_title']}\n"
    markdown_output += f"**题目:** {result['question_title']}\n"
    markdown_output += f"**描述:** {result['description']}\n"
    markdown_output += f"**答案:** {result['answer']}\n\n"

# Save the output to a markdown file
markdown_file_path = "answer.md"
with open(markdown_file_path, "w", encoding="utf-8") as f:
    f.write(markdown_output)
