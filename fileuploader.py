import json
import os
from zhixuewang.account import login_playwright
import requests
import uuid
import datetime
import oss2
from tqdm import tqdm

student = login_playwright('340827200710160315', 'chen992487401')
print(student.get_auth_header()['XToken'])


# student = login_playwright('310116200805160630', '56415880wen')

def generate_uuid():
    return str(uuid.uuid4())


def upload_file_to_zhixue(student_id, token, local_file_path):
    filename = f'EDU_{student_id}_{generate_uuid()}.docx'
    today = datetime.date.today().strftime("%Y/%m/%d/")
    filepath = "middleHomework/android/zxzy/" + today + filename

    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": filename,
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()

    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    security_token = ossinfo['data'][0]['securityToken']
    auth = oss2.StsAuth(sts_access_key_id, sts_access_key_secret, security_token)
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

    total_size = os.path.getsize(local_file_path)

    with tqdm(total=total_size, unit='B', unit_scale=True, desc=filename, ascii=True) as pbar:
        def progress_callback(consumed_bytes, total_bytes):
            pbar.update(consumed_bytes - pbar.n)

        result = bucket.put_object_from_file(filepath, local_file_path, progress_callback=progress_callback)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"


i = 1

try:
    with open('filedata.json', 'r') as f:
        content = f.read()
        attachmentDTOs = json.loads(content)
        i = attachmentDTOs[-1]['sort'] + 1
except:
    attachmentDTOs = []

for filename in os.listdir('files'):
    if filename:  # 检查文件扩展名
        filepath = os.path.join('files', filename)
        print(f'第{i}个文件正在上传...')
        file_url = upload_file_to_zhixue(student.id, student.get_auth_header()['XToken'], filepath)
        print(file_url)
        attachmentDTOs.append({
            "fileType": 4,
            "sourceType": 1,
            "path": file_url,
            "sort": i,
            "videoDuration": 0,
            "videoCoverUrl": "",
            "name": filename,
            "extensionName": "docx"
        })
        i += 1

with open('filedata.json', 'w') as f2:
    json.dump(attachmentDTOs, f2, indent=4)
