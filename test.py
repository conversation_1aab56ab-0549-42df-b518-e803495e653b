import requests
from bs4 import BeautifulSoup
import os

# 基础URL
base_url = "https://zskzs.iflytek.com/myfiles/"

# 获取网页内容
def get_page_content(url):
    response = requests.get(url)
    if response.status_code == 200:
        return response.text
    else:
        return None

# 解析索引页面，找到所有文件和文件夹
def parse_index_page(content):
    soup = BeautifulSoup(content, 'html.parser')
    links = soup.find_all('a')
    items = []
    for link in links:
        href = link.get('href')
        if href and href not in ('../', '/'):
            items.append(href)
    return items

# 检查是否为文件夹
def is_directory(item):
    return item.endswith('/')

# 检查HTML文件中是否有中文元素并打印
def check_html_for_chinese(url):
    content = get_page_content(url)
    if content:
        soup = BeautifulSoup(content, 'html.parser')
        for element in soup.find_all(True):
            if any('\u4e00' <= char <= '\u9fff' for char in element.get_text()):
                print(f"URL: {url}")
                print(f"Chinese Element: {element}")
                return

# 遍历索引并处理文件和文件夹
def traverse_index(url):
    content = get_page_content(url)
    if content:
        items = parse_index_page(content)
        for item in items:
            item_url = os.path.join(url, item)
            if is_directory(item):
                print(item_url)
                traverse_index(item_url)
            elif item.endswith('.html'):
                check_html_for_chinese(item_url)

# 开始遍历
traverse_index(base_url)
