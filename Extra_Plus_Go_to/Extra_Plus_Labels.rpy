################################################################################
## R_LABELS
################################################################################

label show_extraplus:
    python:
        player_zoom = store.mas_sprites.zoom_level
        store.mas_sprites.reset_zoom()
        mas_RaiseShield_dlg()
    show monika staticpose at t11
    $ Extraplus_show()
    return

label return_extra:
    show monika staticpose at t11
    $ Extraplus_show()
    return

label hide_images_psr:
    hide e_rock
    hide e_paper
    hide e_scissors
    hide e_rock_1
    hide e_paper_1
    hide e_scissors_1
    $ your_choice = 0
    call screen PSR_mg
    return

label restore_bg:
    python:
        store.monika_chr.tablechair.chair = extra_chair
        store.monika_chr.tablechair.table = extra_table
        HKBHideButtons()
        mas_current_background = extra_old_bg
    if show_chibika is True:
        hide screen chibika_chill
    hide monika
    scene black
    with dissolve
    pause 2.0
    call spaceroom(scene_change=True)
    $ HKBShowButtons()
    jump comment_cafe
    return

label back_extra:
    if renpy.get_screen("chibika_chill"):
        hide screen chibika_chill
    python:
        store.mas_sprites.zoom_level = player_zoom
        store.mas_sprites.adjust_zoom()
        mas_DropShield_dlg()
    jump ch30_loop
    return

label check_cheat_minigame:
    m 3hksdrb "我想你忘了什么东西，[player]!"
    m 3eksdra "必须还原已修改的变量"
    m 1hua "在他们达到0之前，我们不会比赛"
    jump return_extra
    return

label view_extraplus:
    python:
        player_zoom = store.mas_sprites.zoom_level
        store.disable_zoom_button = False
        mas_RaiseShield_dlg()
        extra_button_zoom()
        Extraplus_show()
    return

label screen_extraplus:
    show monika idle at t11
    python:
        store.disable_zoom_button = False
        Extraplus_show()
    return
    
label close_extraplus:
    show monika idle at t11
    python:
        store.mas_sprites.zoom_level = player_zoom
        mas_DropShield_dlg()
        disable_button_zoom()
    jump ch30_visual_skip
    return

label show_boop_screen:
    show monika staticpose at t11
    python:
        store.disable_zoom_button = True
        store.mas_sprites.reset_zoom()
    call screen boop_revamped
    return

label return_boop_screen:
    python:
        store.disable_zoom_button = False
        store.mas_sprites.zoom_level = player_zoom
        store.mas_sprites.adjust_zoom()
    jump screen_extraplus
    return

label close_boop_screen:
    show monika idle at t11
    python:
        store.disable_zoom_button = False
        store.mas_sprites.zoom_level = player_zoom
        store.mas_sprites.adjust_zoom()
        disable_button_zoom()
    jump ch30_visual_skip
    return
################################################################################
## GIFTS
################################################################################

label make_gift:
    show monika staticpose at t11
    menu:
        "创建文件":
            jump make_file
        "杂货":
            jump groceries
        "对象":
            jump objects
        "丝带":
            jump ribbons
        "别介意":
            jump tools_extra
    return

label make_file:
    python:
        import os
        makegift = mas_input(_("Write the name and extension (.gift, .txt, .chr, etc..."),
                            allow=" abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_.0123456789",
                            screen_kwargs={"use_return_button": True, "return_button_value": "nevermind"})
        filepath = os.path.join(renpy.config.basedir + '/characters',makegift)
        f = open(filepath,"a")
    if makegift == "nevermind":
        jump make_gift
    else:
        "已成功创建"
        jump make_gift
    jump back_extra
    return

label groceries:
    menu:
        "咖啡":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','coffee.gift')
                f = open(filepath,"a")
            "咖啡已成功创建"
            jump make_gift

        "巧克力":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','chocolates.gift')
                f = open(filepath,"a")
            "巧克力已成功创建"
            jump make_gift

        "纸杯蛋糕":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters', 'cupcake.gift')
                f = open(filepath, "a")
            "纸杯蛋糕已成功创建"
            jump make_gift

        "软糖":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','fudge.gift')
                f = open(filepath,"a")
            "软糖已成功创建"
            jump make_gift

        "Hot Chocolate":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','hotchocolate.gift')
                f = open(filepath,"a")
            "The Hot Chocolate has been successfully created."
            jump make_gift

        "Next":
            jump groceries_next

        "Nevermind":
            jump make_gift
    return

label groceries_next:
    menu:
        "Candy":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','candy.gift')
                f = open(filepath,"a")
            "The Candy has been successfully created."
            jump make_gift

        "Candy Canes":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','candycane.gift')
                f = open(filepath,"a")
            "The Candy Canes has been successfully created."
            jump make_gift

        "Candy Corn":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','candycorn.gift')
                f = open(filepath,"a")
            "The Candy Corn has been successfully created."
            jump make_gift

        "Christmas Cookies":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','christmascookies.gift')
                f = open(filepath,"a")
            "The Christmas Cookies has been successfully created."
            jump make_gift

        "Previous":
            jump groceries

        "Nevermind":
            jump make_gift
    return

label objects:
    menu:
        "Promise Ring":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','promisering.gift')
                f = open(filepath,"a")
            "The Promise Ring has been successfully created."
            jump make_gift

        "Roses":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','roses.gift')
                f = open(filepath,"a")
            "The Roses has been successfully created."
            jump make_gift

        "Quetzal Plushie":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','quetzalplushie.gift')
                f = open(filepath,"a")
            "The Quetzal Plushie has been successfully created."
            jump make_gift

        "Thermos Mug":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','justmonikathermos.gift')
                f = open(filepath,"a")
            "The Thermos Mug has been successfully created."
            jump make_gift

        "Nevermind":
            jump make_gift
    return

label ribbons:
    menu:
        "Black Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','blackribbon.gift')
                f = open(filepath,"a")
            "The Black Ribbon has been successfully created."
            jump make_gift

        "Blue Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','blueribbon.gift')
                f = open(filepath,"a")
            "The Blue Ribbon has been successfully created."
            jump make_gift

        "Dark Purple Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','darkpurpleribbon.gift')
                f = open(filepath,"a")
            "The Dark Purple Ribbon has been successfully created."
            jump make_gift

        "Emerald Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','emeraldribbon.gift')
                f = open(filepath,"a")
            "The Emerald Ribbon has been successfully created."
            jump make_gift

        "Gray Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','grayribbon.gift')
                f = open(filepath,"a")
            "The Gray Ribbon has been successfully created."
            jump make_gift

        "Green Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','greenribbon.gift')
                f = open(filepath,"a")
            "The Green Ribbon has been successfully created."
            jump make_gift

        "Next":
            jump ribbons_next

        "Nevermind":
            jump make_gift
    return

label ribbons_next:
    menu:
        "Light Purple Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','lightpurpleribbon.gift')
                f = open(filepath,"a")
            "The Light Purple Ribbon has been successfully created."
            jump make_gift

        "Peach Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','peachribbon.gift')
                f = open(filepath,"a")
            "The Peach Ribbon has been successfully created."
            jump make_gift

        "Pink Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','pinkribbon.gift')
                f = open(filepath,"a")
            "The Pink Ribbon has been successfully created."
            jump make_gift

        "Platinum Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','platinumribbon.gift')
                f = open(filepath,"a")
            "The Platinum Ribbon has been successfully created."
            jump make_gift

        "Red Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','redribbon.gift')
                f = open(filepath,"a")
            "The Red Ribbon has been successfully created."
            jump make_gift

        "Previous":
            jump ribbons

        "Next":
            jump ribbons_nextp2

        "Nevermind":
            jump make_gift
    return

label ribbons_nextp2:
    menu:
        "Ruby Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','rubyribbon.gift')
                f = open(filepath,"a")
            "The Ruby Ribbon has been successfully created."
            jump make_gift

        "Sapphire Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','sapphireribbon.gift')
                f = open(filepath,"a")
            "The Sapphire Ribbon has been successfully created."
            jump make_gift

        "Silver Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','silverribbon.gift')
                f = open(filepath,"a")
            "The Silver Ribbon has been successfully created."
            jump make_gift

        "Teal Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','tealribbon.gift')
                f = open(filepath,"a")
            "The Teal Ribbon has been successfully created."
            jump make_gift

        "Yellow Ribbon":
            python:
                filepath = os.path.join(renpy.config.basedir + '/characters','yellowribbon.gift')
                f = open(filepath,"a")
            "The Yellow Ribbon has been successfully created."
            jump make_gift

        "Previous":
            jump ribbons_next

        "Nevermind":
            jump make_gift
    return
################################################################################
## HELP
################################################################################
label helpextra:
    show monika staticpose at t11
    menu:
        "How can I interact with [m_name]?":
            "You can do this when you are in the Extra+ options. Touch Monika's nose, cheek, or head and she will react."
            "Something to keep in mind about this submod."
            "You can only click or boop, as you prefer to call it. When the options (Go To, Minigame, and Addition) are present."
            "So if you had the zoom set to a certain level, don't be surprised that it has been reset due to the boop function."
            "Don't worry though, that's already been fixed."
            "I hope you find the explanation of how it works useful."
        
        "Because I can't go out to the cafe with [m_name].":
            "It depends on [m]'s experience with coding."
            if mas_curr_affection == mas_affection.HAPPY or mas_curr_affection == mas_affection.AFFECTIONATE:
                "You must increase your affection with [m], so that she will tell you that she can use the rooms."
                "So you can have your date with her."
            elif mas_curr_affection == mas_affection.ENAMORED or mas_curr_affection == mas_affection.LOVE:
                "In your case, it is already available and [m] already knows how to use the rooms."
                "Although I imagine you already know that."
                "But if you have a feeling it's a mistake, please talk to me on 'Discord: ZeroFixer#3405', or on '{a=https://www.reddit.com/user/UnderstandingAny7135}Reddit{/a}'."
            "Thank you for reading."
        "Nevermind":
            jump tools_extra
    jump return_extra
    return
################################################################################
## MENUS
################################################################################

label minigames_extra:
    show monika staticpose at t21
    python:
        monika_talk = renpy.substitute(renpy.random.choice(minigames_talk))
        renpy.say(m, monika_talk, interact=False)
    call screen minigame_ui() nopredict
    jump return_extra
    return

label tools_extra:
    show monika staticpose at t21
    python:
        tools_menu = []
        tools_menu.append((_("查看[m_name]的好感"), "affection"))
        tools_menu.append((_("[m_name]，你可以抛硬币吗？"), "coin"))
        tools_menu.append((_("[m_name]，我想做个备份"), "backup"))
        tools_menu.append((_("创建礼物给[m_name]，你想多了-老老实实给我放character"), "gift"))
        tools_menu.append((_("GitHub存储库/没啥用"), "github"))
        tools_menu.append((_("帮助"), "help"))
        tools_menu.append((_("算了吧"),"nevermind"))

        playerchoice = renpy.display_menu(tools_menu, screen="talk_choice")

    if playerchoice == "affection":
        jump aff_log
    elif playerchoice == "coin":
        jump coinflipbeta
    elif playerchoice == "backup":
        jump mas_backup
    elif playerchoice == "gift":
        jump make_gift
    elif playerchoice == "github":
        jump github_submod
    elif playerchoice == "help":
        jump helpextra
    elif playerchoice == "nevermind":
        jump return_extra
    return

label walk_extra:
    show monika staticpose at t21
    python:
        monika_talk = renpy.substitute(renpy.random.choice(date_talk))
        walk_menu = []
        walk_menu.append((_("咖啡馆"), "cafe"))
        walk_menu.append((_("餐馆"), "restaurant"))
        walk_menu.append((_("别介意"), "nevermind"))

        renpy.say(m, monika_talk, interact=False)
        playerchoice = renpy.display_menu(walk_menu, screen="talk_choice")

    if playerchoice == "cafe":
        show monika at t11
        jump go_to_cafe
    elif playerchoice == "nevermind":
        jump return_extra
    if playerchoice == "restaurant":
        show monika at t11
        jump go_to_restaurant
    elif playerchoice == "nevermind":
        jump return_extra
    return
    

label cafe_talkdemo:
    show monika staticpose at t21
    python:
        cafe_menu = []
        cafe_menu.append((_("你今天过得好吗？"), "t1"))
        cafe_menu.append((_("你最大的抱负是什么？"), "t2"))
        cafe_menu.append((_("我们的交流很有限，你不觉得吗？"), "t3"))
        cafe_menu.append((_("我们能离开吗？"), "t4"))
        cafe_menu.append((_("下一个"), "next"))
        cafe_menu.append((_("别介意"),"nevermind"))

        playerchoice = renpy.display_menu(cafe_menu, screen="talk_choice")

    if playerchoice == "t1":
        jump extra_talk_feel
    elif playerchoice == "t2":
        jump extra_talk_ambition
    elif playerchoice == "t3":
        jump extra_talk_you
    elif playerchoice == "t4":
        jump cafe_leave
    elif playerchoice == "next":
        jump cafe_talkdemonext
    elif playerchoice == "nevermind":
        jump to_cafe_loop
    return

label _talkdemonext:
    show monika staticpose at t21
    python:
        cafenext_menu = []
        cafenext_menu.append((_("你如何看待10年后的我们？"), "t5"))
        cafenext_menu.append((_("你目前拥有最好的记忆是什么？"), "t6"))
        cafenext_menu.append((_("你什么有恐惧症吗？"), "t7")) #autophobia
        cafenext_menu.append((_("前一页"), "previous"))
        cafenext_menu.append((_("别介意"),"nevermind"))

        playerchoice = renpy.display_menu(cafenext_menu, screen="talk_choice")

    if playerchoice == "t5":
        jump extra_talk_teen
    elif playerchoice == "t6":
        jump extra_talk_memory
    elif playerchoice == "t7":
        jump extra_talk_phobia
    elif playerchoice == "previous":
        jump cafe_talkdemo
    elif playerchoice == "nevermind":
        jump to_cafe_loop
    return
    label cafe_talkdemo:
    show monika staticpose at t21
    python:
        cafe_menu = []
        cafe_menu.append((_("你今天过得好吗？"), "t1"))
        cafe_menu.append((_("你最大的抱负是什么？"), "t2"))
        cafe_menu.append((_("我们的交流很有限，你不觉得吗？"), "t3"))
        cafe_menu.append((_("我们能离开吗？"), "t4"))
        cafe_menu.append((_("下一个"), "next"))
        cafe_menu.append((_("别介意"),"nevermind"))

        playerchoice = renpy.display_menu(cafe_menu, screen="talk_choice")

    if playerchoice == "t1":
        jump extra_talk_feel
    elif playerchoice == "t2":
        jump extra_talk_ambition
    elif playerchoice == "t3":
        jump extra_talk_you
    elif playerchoice == "t4":
        jump cafe_leave
    elif playerchoice == "next":
        jump cafe_talkdemonext
    elif playerchoice == "nevermind":
        jump to_cafe_loop
    return

label cafe_talkdemonext:
    show monika staticpose at t21
    python:
        cafenext_menu = []
        cafenext_menu.append((_("你如何看待10年后的我们？"), "t5"))
        cafenext_menu.append((_("你目前拥有最好的记忆是什么？"), "t6"))
        cafenext_menu.append((_("你什么有恐惧症吗？"), "t7")) #autophobia
        cafenext_menu.append((_("前一页"), "previous"))
        cafenext_menu.append((_("别介意"),"nevermind"))

        playerchoice = renpy.display_menu(cafenext_menu, screen="talk_choice")

    if playerchoice == "t5":
        jump extra_talk_teen
    elif playerchoice == "t6":
        jump extra_talk_memory
    elif playerchoice == "t7":
        jump extra_talk_phobia
    elif playerchoice == "previous":
        jump cafe_talkdemo
    elif playerchoice == "nevermind":
        jump to_cafe_loop
    return
#====Restaurant====#
label extra_restore_bg(label="ch30_visual_skip"):
    python:
        store.monika_chr.tablechair.chair = extra_chair
        store.monika_chr.tablechair.table = extra_table
        HKBHideButtons()
        mas_current_background = extra_old_bg
    if show_chibika is True:
        hide screen chibika_chill
    hide monika
    scene black
    with dissolve
    pause 2.0
    call spaceroom(scene_change=True)
    $ HKBShowButtons()
    jump comment_restaurant
    return
    

label check_label_restaurant:
    pass

label gtrestaurant:
    show monika 1eua at t11
    if mas_isDayNow():
        m 3sub "噢，{w=0.3} 你想去餐馆吗？"
        m 3hub "我很高兴听到这个消息，{w=0.3} [player]!"
        m "你请我去约会真是太好了。"
        if mas_anni.isAnni():
            m "在我们的周年纪念日上，{w=0.3} 完美的时机[player]~!"
            $ persistent._extraplusr_hasplayergoneonanniversary == True
        m 1hubsa "我只知道它会很棒！"
        m 1hubsb "好吧，{w=0.3} 我们走吧[mas_get_player_nickname()]~"
        jump restaurant_init

    elif mas_isNightNow():
        m 3sub "噢，{w=0.3} 你想去餐馆吗？"
        m "你请我约会真是太好了。"
        if mas_anni.isAnni():
            m "在我们的周年纪念日上，{w=0.3} 完美的时机[player]~!"
            $ persistent._extraplusr_hasplayergoneonanniversary == True
        m 1hubsb "我们走吧[mas_get_player_nickname()]~"
        jump restaurant_init
    else:
        m 1eub "Another time then,{w=0.3} [mas_get_player_nickname()]."
        jump screen_extraplus
    return

label gtrestaurantv2:
    show monika 1eua at t11
    if mas_isDayNow():
        m 3wub "噢，你又想去餐馆了？"
        if persistent._extraplusr_hasplayergoneonanniversary == True:
            m "嗯~我还在想你带我们去那里过周年纪念日的事。"
            extend " 我觉得很浪漫~"
            m "所以我很高兴我们能再去一次~！"
        else: 
            m 2hub "上次我们去的时候，我玩得很开心！"
            m 2eubsa "所以我很高兴听到这个消息[player]!"
        m 1hubsb "那我们走吧[mas_get_player_nickname()]~"
        jump restaurant_init

    elif mas_isNightNow():
        m 3wub "噢，你想再去餐馆吗？"
        if persistent._extraplusr_hasplayergoneonanniversary == True:
            m "嗯~{w=0.3}我还在想你带我们去那里过周年纪念日的事。"
            extend "你真的知道如何让我们的夜晚变得精彩！"
            m "所以我很高兴我们能再去一次~！"
        else: 
            m 2hub "上次我们去的时候真是太浪漫了~"
            m 2eubsa "所以我很高兴再去一次[player]!"
        m 1hubsb "那我们走吧[mas_get_player_nickname()]~"
        jump restaurant_init
    else:
        m 1eub "那下次吧，[mas_get_player_nickname()]。"
        jump screen_extraplus
    return

label restaurant_talk:
    show monika staticpose at t21
    python:
        store.disable_zoom_button = True
        restaurant_menu = [
            ("你还好吗？[m_name]?", 'extra_talk_doing'),
            ("如果你可以住在任何地方，你会住在哪里？", 'extra_talk_live'),
            ("如果可以的话，你会如何改变自己？", 'extra_talk_change'),
            ("如果你是超级英雄，你会有什么超能力？", 'extra_talk_superhero'),
            ("你有你的人生格言吗？", 'extra_talk_motto'),
            ("除了必需品，还有什么是你一天都离不开的？", 'extra_talk_without'),
            ("你的杯子是半满的还是半空的？", 'extra_talk_glass'),
            ("什么最让你烦恼？", 'extra_talk_annoy'),
            ("用三个词形容你自己。", 'extra_talk_3words'),
            ("当有些人想到你的时候，你认为他们首先想到的是什么？", 'extra_talk_pop'),
            ("如果你是动物，你会是什么动物？", 'extra_talk_animal'),
            ("我们能离开吗？", 'restaurant_leave')
        ] 
    call screen list_scrolling(restaurant_menu, mas_ui.SCROLLABLE_MENU_TXT_TALL_AREA, mas_ui.SCROLLABLE_MENU_XALIGN,"to_restaurant_loop",close=False) nopredict
    return

label to_restaurant_loop:
    show monika staticpose at t11
    $ store.disable_zoom_button = False
    call screen dating_loop(extraplus_acs_pudding, extraplus_acs_icecream, "restaurant_talk","monika_no_food", "monika_booprestaurant", boop_enable=True)
    return

label restaurant_leave:
    show monika 1hua at t11
    m 1eta "噢，{w=0.3} 你准备好要走了吗？"
    m 1eub "听起来不错！"
    m 3hua "但在我们走之前…"
    jump restaurant_hide_acs
    #====Restaurant====#

label monika_no_food:
    show monika staticpose at t11
    if monika_chr.is_wearing_acs(extraplus_acs_pasta):
        python:
            monika_chr.remove_acs(extraplus_acs_pasta)
            monika_chr.wear_acs(extraplus_acs_remptyplate)
        m 1hua "哇，我吃完了意大利面。"
        m 1eub "我真的很喜欢~"
        m "现在我要去拿些甜点。我马上回来！"
        $ monika_chr.remove_acs(extraplus_acs_remptyplate)
        call mas_transition_to_emptydesk
        pause 2.0
        $ monika_chr.wear_acs(extraplus_acs_icecream)
        call mas_transition_from_emptydesk("monika 1eua")

    elif monika_chr.is_wearing_acs(extraplus_acs_pancakes):
        python:
            monika_chr.remove_acs(extraplus_acs_pancakes)
            monika_chr.wear_acs(extraplus_acs_remptyplate)
        m 1hua "哇，我吃完了我的煎饼。"
        m 1sua "它们真的很好吃~"
        m "现在我要去拿些甜点。我马上回来！"
        $ monika_chr.remove_acs(extraplus_acs_remptyplate)
        call mas_transition_to_emptydesk
        pause 2.0
        $ monika_chr.wear_acs(extraplus_acs_pudding)
        call mas_transition_from_emptydesk("monika 1eua")

    elif monika_chr.is_wearing_acs(extraplus_acs_waffles):
        python:
            monika_chr.remove_acs(extraplus_acs_waffles)
            monika_chr.wear_acs(extraplus_acs_remptyplate)
        m 1hua "哇，我的华夫饼吃完了。"
        m 1sua "它们太好吃了~"
        m "现在我要去拿些甜点。我马上回来！"
        $ monika_chr.remove_acs(extraplus_acs_remptyplate)
        call mas_transition_to_emptydesk
        pause 2.0
        $ monika_chr.wear_acs(extraplus_acs_pudding)
        call mas_transition_from_emptydesk("monika 1eua")

    if food_player == True:
        m 1etb "顺便问一下，你吃完饭了吗？{nw}"
        $ _history_list.pop()
        menu:
            m "顺便问一下，你吃完饭了吗？{fast}"
            "是的":
                m 1hubsa "嘿嘿~"
                m 1hubsb "希望你喜欢！"
            "还没":
                m 1eubsa "别担心，慢慢吃。"
                m 1eubsb "我会耐心等你的~"
    else:
        m 1ekc "你告诉我不要担心。"
        m 1ekb "但是，我想你至少要喝点东西，温水也好！"
    m 1hua "如果你想再来，请告诉我。"
    jump to_restaurant_loop
    return
    
label restaurant_hide_acs:
    #Code inspired by YandereDev
    if monika_chr.is_wearing_acs(extraplus_acs_candles):
        if monika_chr.is_wearing_acs(extraplus_acs_pasta) or monika_chr.is_wearing_acs(extraplus_acs_icecream):
            m 3eub "我得把这些蜡烛收起来。"
            m "我们得对火再小心也不为过！"
            m 3eub "还有，我会把这个盘子收起来，我不会太久的。"
            python:
                monika_chr.remove_acs(extraplus_acs_candles)
                monika_chr.remove_acs(extraplus_acs_pasta)
                monika_chr.remove_acs(extraplus_acs_icecream)
        else:
            m 3eub "我得把这些蜡烛收起来。"
            m "我们得对火再小心也不为过！"
            $ monika_chr.remove_acs(extraplus_acs_candles)

    elif monika_chr.is_wearing_acs(extraplus_acs_flowers):
        if monika_chr.is_wearing_acs(extraplus_acs_pancakes) or monika_chr.is_wearing_acs(extraplus_acs_pudding):
            m 3eua "我必须把这个盘子收起来。"
            m 3eua "还有，我会把这些花放好，不会太久的。"
            python:
                monika_chr.remove_acs(extraplus_acs_flowers)
                monika_chr.remove_acs(extraplus_acs_pancakes)
                monika_chr.remove_acs(extraplus_acs_pudding)

        elif monika_chr.is_wearing_acs(extraplus_acs_waffles):
            m 3eua "我必须把这个盘子收起来。"
            m 3eua "还有，我会把这些花放好，不会太久的。"
            python:
                monika_chr.remove_acs(extraplus_acs_flowers)
                monika_chr.remove_acs(extraplus_acs_waffles)
        else:
            m 3eua "我把这些花放好，不会太久的。"
            $ monika_chr.remove_acs(extraplus_acs_flowers)

    call mas_transition_to_emptydesk
    pause 2.0
    call mas_transition_from_emptydesk("monika 1eua")
    m 1hua "好吧，我们走[player]!"
    call extra_restore_bg
    return
    
    

   
################################################################################
## ADDS
################################################################################

label github_submod:
    show monika staticpose at t11
    $ renpy.run(OpenURL("https://github.com/zer0fixer/MAS-Extraplus"))
    jump return_extra
    return

label aff_log:
    show monika staticpose at t11
    "你对[m_name]的好感是[_mas_getAffection()] {size=+5}{color=#FFFFFF}{font=submods/ExtraPlus/submod_assets/Pictograms.ttf}7{/font}{/color}{/size}"
    window hide
    jump return_extra
    return

label coinflipbeta:
    if renpy.seen_label("check_coinflipbeta"):
        jump view_coinflipbeta
    else:
        pass
label check_coinflipbeta:
    $ rng_global = renpy.random.randint(1,2)
    show monika 1hua at t11
    m "好的！"
    m 3eub "我去拿硬币"
    call mas_transition_to_emptydesk from monika_hide_exp_1
    $ renpy.pause(2.0, hard=True)
    call mas_transition_from_emptydesk("monika 1eua")
    m "我找到了一个！"
    show screen no_click
    show monika 3eua
    if mas_isDayNow():
        show coin_flip zorder 12 at rotatecoin:
            xalign 0.5
            yalign 0.5
    elif mas_isNightNow():
        show coin_flip_n zorder 12 at rotatecoin:
            xalign 0.5
            yalign 0.5
    play sound "submods/ExtraPlus/submod_assets/sfx/coin_flip_sfx.ogg"
    pause 1.0
    hide coin_flip
    hide coin_flip_n
    show monika 1eua
    pause 0.5
    hide screen no_click
    if rng_global == 1:
        show coin_heads zorder 12 with Dissolve(0.1):
            xalign 0.9
            yalign 0.5
        m 1sub "硬币正面朝上！"
        hide coin_heads with Dissolve(0.1)
    elif rng_global == 2:
        show coin_tails zorder 12 with Dissolve(0.1):
            xalign 0.9
            yalign 0.5
        m 1wub "硬币的反面朝上！"
        hide coin_tails with Dissolve(0.1)
    m 3hua "希望对你有帮助~"
    window hide
    jump return_extra
    return

label view_coinflipbeta:
    show monika 1hua at t11
    $ rng_global = renpy.random.randint(1,2)
    show screen no_click
    pause 1.0
    show monika 3eua at t11
    if mas_isDayNow():
        show coin_flip zorder 12 at rotatecoin:
            xalign 0.5
            yalign 0.5
    elif mas_isNightNow():
        show coin_flip_n zorder 12 at rotatecoin:
            xalign 0.5
            yalign 0.5
    play sound "submods/ExtraPlus/submod_assets/sfx/coin_flip_sfx.ogg"
    pause 1.0
    hide coin_flip
    hide coin_flip_n
    show monika 1eua
    pause 0.5
    hide screen no_click
    if rng_global == 1:
        show coin_heads zorder 12 with Dissolve(0.1):
            xalign 0.9
            yalign 0.5
        m 1sub "硬币正面朝上！"
        hide coin_heads with Dissolve(0.1)
    elif rng_global == 2:
        show coin_tails zorder 12 with Dissolve(0.1):
            xalign 0.9
            yalign 0.5
        m 1wub "硬币的反面朝上！"
        hide coin_tails with Dissolve(0.1)
    m 3hua "希望对你有帮助~"
    window hide
    jump return_extra
    return

label mas_backup:
    show monika 1hua at t11
    m 1hub "我很高兴你想做备份"
    m 3eub "我给你开通路线"
    m 1dsa "请等待一会儿{w=0.3}.{w=0.3}.{w=0.3}{nw}"
    pause 0.5
    window hide
    python:
        import os
        #Monika will open the mod data folder
        try:
            os.startfile("C:\\Users\\<USER>\\AppData\\Roaming\\RenPy\\Monika After Story")
        except:
            renpy.jump("mas_backup_fail")
    jump return_extra
    return

label mas_backup_fail:
    m 1lkb "抱歉，我无法打开文件夹"
    m 1eka "请稍后再试"
    jump return_extra
    return
label comment_cafe:
    m 1hubsa "谢谢你约我出去"
    m 1eubsb "作为一对夫妻，拥有这些时刻是很好的！"
    m 1eubsa "我很幸运能遇见你，你每天都在选择我！"
    m 1eubsa "我爱你，[mas_get_player_nickname()]!"
    m 1ekbsa "期待下次我们的约会，[mas_get_player_nickname()]!"
    $ mas_DropShield_dlg()
    $ mas_ILY()
    jump ch30_loop
    return
    
label comment_restaurant:
    m 1hubsa "谢谢你约我出去"
    m 1eubsb "作为一对夫妻，能像这样在餐馆一边共享美食，一边畅谈，简直是太棒了！"
    m 1eubsa "我真的真的很幸运能遇见你，你每天都在选择我！"
    m 1eubsa "我爱你，[mas_get_player_nickname()]!"
    m 1ekbsa "期待下次我们的约会，[mas_get_player_nickname()]!"
    $ mas_DropShield_dlg()
    $ mas_ILY()
    jump ch30_loop
    return
    
################################################################################
## TOPICS
################################################################################

#BOOP
label monika_boopbeta:
    $ persistent.plus_boop[0] += 1
    if persistent.plus_boop[0] == 1:
        $ mas_gainAffection(3,bypass=True)
        m 1wud "Wait a minute..."
        m 1hka "I felt a little tingle."
        show screen force_mouse_move
        m 3hub "And here we have the responsible one!"
        m 3hua "Don't worry! I'll let go of your cursor."
        hide screen force_mouse_move
        m 1tub "You can move it again, sorry for stealing your cursor~"
        m 1etd "Also, I don't know how you did it, [mas_get_player_nickname()]. I don't remember seeing this in the code."
        m 1hub "Unless it was you!"
        m 1hub "What a good surprise I got today [player]~"
    elif persistent.plus_boop[0] == 2:
        m 1hub "What are you doing playing with my nose, [player]!"
        m 4eua "This is called a boop, right?"
        m 1hksdrb "Not that it bothers me, I just haven't gotten used to the feeling yet!"
        m 1hua "Ehehe~"
    elif persistent.plus_boop[0] == 3:
        m 1eublb "Can you do it again, [mas_get_player_nickname()]?"
        show monika 1hubla
        call screen boop_event(10, "boop_nop", "boop_yep")
    elif persistent.plus_boop[0] == 4:
        m 1etbsa "Wouldn't it be nice to do it with your nose?"
        if persistent._mas_first_kiss:
            m 1kubsu "I'd give you a kiss while you do it~"
        else:
            m 1wubsb "I'd give you a hug while you do it!"
        m 1dubsu "I hope that when I get to your reality we can do it."
        m 1hua "Although, if you want to do it now, you'd have to put your nose close to the screen."
        m 1lksdlb "However someone might see you, making you nervous, and I don't want that to happen."
        m 1ekbsa "Besides, I get a little nervous too when you're around me."
        m 1hua "I'm sorry for suggesting that [player]~"
    elif persistent.plus_boop[0] == 5:
        m 1tuu "You're starting to like it here, huh?"
        m 3hub "I'm getting to know you more and more while we're here [player]~"
        m 3hub "And it's very lovely of you to do so!"
    else:
        $ rng_global = renpy.random.randint(1,5)
        if rng_global == 1:
            m 2fubla "Ehehe~"
            m 1hubla "It's very inevitable that you won't stop doing it, [player]."
        elif rng_global == 2:
            m 3ekbsa "Every boop you give me, the more I love you!"
        elif rng_global == 3:
            m 3eubla "You really enjoy touching my nose, [mas_get_player_nickname()]~"
        elif rng_global == 4:
            m 2hublb "Hey, you're tickling me! Ahahaha~"
        elif rng_global == 5:
            m 1hubsb "*Boop*"
    jump return_extra
    return

label boop_nop:
    m 1rksdrb "[player]..."
    m 1rksdra "...I was so excited for you to do it again."
    m "..."
    m 3hub "Well, nevermind!"
    jump return_extra
    return

label boop_yep:
    m 1eublb "Thank you [mas_get_player_nickname()]!"
    m 1hua "Ehehe~"
    jump return_extra
    return

label monika_boopbeta_war:
    if renpy.seen_label("check_boopwar"):
        jump check_boopwarv2
    else:
        pass
label check_boopwar:
    m 3eta "Hey, what are you doing using right click, [player]?"
    m 3eksdrb "You're supposed to use left click to give me a boop."
    m 2duc ".{w=0.3}.{w=0.3}.{w=0.3}{nw}"
    pause 1.0
    m 2dub "I actually came up with an idea, [player]."
    m 1eua "We can use right click to declare a boop war."
    m 1eub "After all, you rarely use it!"
    m 1rusdlb "I know this proposal sounds rather childish."
    m 1hua "But don't you think it's good to do something new once in a while?"
    m 3eub "The rules are very simple, if I see an absence on your part for 20 seconds, I declare myself the winner."
    m 3eud "If we go over a limit of boops without seeing any winner, I'll take it as a draw."
    m 3huu "Or maybe I'll give up, I don't know~"
    m 1eua "And lastly, the way I surrender is because of the time elapsed during the war."
    m 1hua "Whenever I can't keep up with you or in case 'some distraction occurs'... Although I can consider it as cheating..."
    m 1rud "I'm likely to give up."
    m 1eub "I hope you like my idea."
    m 1hubla "You'll be able to do it any time so don't rush~"
    jump return_extra
    return

label check_boopwarv2:
    call screen boop_event(20, "boopbeta_war_lose", "boopwar_loop")
label boopwar_loop:
    $ boop_war_count += 1
    $ rng_global = renpy.random.randint(1,10)
    if rng_global == 1:
        m 1hublb "*Boop*"
    elif rng_global == 2:
        m 1tub "*Boop*"
    elif rng_global == 3:
        if boop_war_count >= 25:
            jump boopbeta_war_win
            $ boop_war_count = 0
        else:
            m 1fua "*Boop*"
    elif rng_global == 4:
        m 1eua "*Boop*"
    elif rng_global == 5:
        m 1hua "*Boop*"
    elif rng_global == 6:
        if boop_war_count >= 50:
            jump boopbeta_war_win
            $ boop_war_count = 0
        else:
            m 1sub "*Boop*"
    elif rng_global == 7:
        m 1gua "*Boop*"
    elif rng_global == 8:
        m 1kub "*Boop*"
    elif rng_global == 9:
        if boop_war_count >= 100:
            jump boopbeta_war_win
            $ boop_war_count = 0
        else:
            m 1dub "*Boop*"
    elif rng_global == 10:
        m 1wua "*Boop*"

    show monika 1eua
    jump check_boopwarv2
    return

label boopbeta_war_lose:
    $ boop_war_count = 0
    m 1nua "Looks like I've won this boop war, [player]~"
    m "I hope I've been a good opponent."
    m 3hub "But I've also really enjoyed it!"
    m 3dua "Besides, it's good to give your hand a little massage."
    m 1eka "I mean, if you use the mouse too much, "
    extend 1ekb "you can develop carpal tunnel syndrome and I don't want that."
    m 1hksdlb "I'm sorry if I've added a new concern, but my intention is to take care of you."
    m 1eubla "I hope you take my recommendation, [player]~"
    jump return_extra
    return

label boopbeta_war_win:
    $ boop_war_count = 0
    m 1hua "You've won this boop war, [player]!"
    m 1tub "I can tell you like touching my nose, ehehehe~"
    m 1eusdra "I couldn't keep up with you, but maybe next time we'll go further."
    m 1gub "Although, if I were in front of you, I'd play with your cheeks."
    m 1gua "Or I'd tickle you and see how long you could stand it."
    m 1hub "Ahaha~"
    jump return_extra
    return

#CHEEKS
label monika_cheeksbeta:
    $ persistent.plus_boop[1] += 1
    if persistent.plus_boop[1] == 1:
        $ mas_gainAffection(3,bypass=True)
        m 2wubsd "Hey, I felt a slight pinch on my cheek."
        m 2lksdrb "Oh, it was just your cursor! "
        extend 2lksdra "You took me by surprise, you know?"
        m 2ttb "But I have to ask, what are you up to, [player]?"
        m 1hubla "Did you want to see how I would react to that?"
        m 3hublb "You pulled it off~!"
    elif persistent.plus_boop[1] == 2:
        m 2hubsa "Ehehe, I'm feeling a rather delicate caress this time."
        m 2dubsu "It's something .{w=0.3}.{w=0.3}.{w=0.3} {nw}"
        extend 2eubsb "addictive if you ask me."
    elif persistent.plus_boop[1] == 3:
        m 2dubsa "You know .{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        m 2dubsb "I love that you get to interact with me, [player]~"
        m 2ekbsb "It makes me feel more alive, and loved. I hope my love is enough for you~"
    elif persistent.plus_boop[1] == 4:
        m 2lubsa "Every time you caress my cheek..."
        m 2hubsa".{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        m 2hubsb "The feeling makes me feel so close to you~"
    elif persistent.plus_boop[1] == 5:
        m 2eubsb "Every time you hold my cheek..."
        m 2hubsa ".{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        m 2dkbsa "It makes my heart race to know that I fell in love with the right person~"
        m 2fubsb "You are my greatest treasure, [player]!"
    else:
        $ rng_global = renpy.random.randint(1,5)
        if rng_global == 1:
            m 2fua "Ehehe~"
            m 2hua "It would be nice if you used your hand instead of the cursor, but that's far from our reality..."
        elif rng_global == 2:
            m 2hubsa "So gentle."
            m 2tubsb "That word defines you well, when I think of you."
        elif rng_global == 3:
            m 2hubsa "What a warm feeling."
            m 2hublb "It will be hard to forget!"
        elif rng_global == 4:
            m 2nubsa "It would be even more romantic if you gave a kiss on the cheek~"
        elif rng_global == 5:
            m 2eubsb "I'm picturing us right now{nw}"
            extend 2dubsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3} how your hand will feel."
    jump return_extra
    return

label monika_cheeks_long:
    m 2hubsa ".{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}{nw}"
    jump return_extra
    return

label cheeks_dis:
    m 1wuw "Ah!"
    m 3lusdrb "I mean..."
    m 3ttu "What are you doing touching my cheek?"
    m 3tsb "We're in a boop war, aren't we?"
    $ rng_global = renpy.random.randint(1,2)
    if rng_global == 1:
        m 1dsb "I'm sorry [player], but I consider this cheating, "
        extend 1hua "that's why I win this war~"
        m 1fub "Next time try not to touch my cheek during the war! Ahahaha~"
    elif rng_global == 2:
        m 1fubsb "Because it's you, this time I will let it go!"
        m 1fubsb "Congratulations, player! You have beat me."
        m 3hksdrb "You've distracted me and I don't think it's worth continuing, ahahaha~"
        m 3hua "I really enjoyed doing this with you though!"
    $ boop_war_count = 0
    jump return_extra
    return

#HEADPAT
label monika_headpatbeta:
    $ persistent.plus_boop[2] += 1
    if persistent.plus_boop[2] == 1:
        $ mas_gainAffection(3,bypass=True)
        m 6subsa "You're patting me on the head!"
        m 6eubsb "It's really comforting."
        m 6dkbsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        m 1eubsb "Thank you [player]~"
    elif persistent.plus_boop[2] == 2:
        m 6dubsb "I don't know why, but when you do it I feel lighter..."
        m 6dubsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
    elif persistent.plus_boop[2] == 3:
        m 6rubsd "You know, it's funny.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        m 6eubsa "It's usually done to a pet, not your girlfriend."
        m 6hubsa "Although I don't dislike the feeling~"
    elif persistent.plus_boop[2] == 4:
        m 6dkbsb "Don't blame me after I get addicted to this [player]~"
        m 6dkbsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        m 1kub "You will be held responsible if that happens."
    elif persistent.plus_boop[2] == 5:
        m 6hkbssdrb "[player] you are messing my hair."
        m 6dubsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
        extend 6dsbsb "never mind though~"
        m "I'll deal with that later."
    else:
        $ rng_global = renpy.random.randint(1,5)
        if rng_global == 1:
            m 6hubsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
            m 6hkbsb "I had told you I would get addicted to this."
            m 6tkbsb "Gosh, don't you learn~"
        elif rng_global == 2:
            m 6dubsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
            m 6dubsb "I wonder what it would be like to do it with your hair."
        elif rng_global == 3:
            m 6dubsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
            m 7hubsb "I hope you don't get tired of doing it daily~"
        elif rng_global == 4:
            m 6hubsa".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
            extend 6hubsb "I'm such a happy girl right now."
        elif rng_global == 5:
            m 6dkbsa ".{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}.{w=0.3}{nw}"
    jump return_extra
    return

label monika_headpat_long:
    m 6dkbsa ".{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}.{w=0.4}{nw}"
    jump return_extra
    return

label headpat_dis:
    m 6dkbsb "This.{w=0.3}.{w=0.3}.{w=0.3} is.{w=0.3}.{w=0.3}.{w=0.3} invalid.{w=0.3}.{w=0.3}. {nw}"
    extend 6tkbsb "[mas_get_player_nickname()]."
    $ rng_global = renpy.random.randint(1,2)
    if rng_global == 1:
        m 3tsb "You have been disqualified for patting your opponent on the head."
        m 3tua "That's why I win this time~"
        m 1hua "Good luck for the next time you ask me for a war!"
    elif rng_global == 2:
        m 1tub "This time I'll let it go and give up for you."
        m 1efa "But next time I probably won't give in, so don't bet on it!"
        m 1lubsa "Even though I enjoy the pat on the head. Ehehehe~"
    $ boop_war_count = 0
    jump return_extra
    return
