################################################################################
## 咖啡馆
################################################################################
label go_to_cafe:
    if renpy.get_screen("chibika_chill"):
        $ show_chibika = True
    else:
        $ show_chibika = False
    python:
        extra_chair = store.monika_chr.tablechair.chair
        extra_table = store.monika_chr.tablechair.table
        extra_old_bg = mas_current_background

    if mas_curr_affection == mas_affection.HAPPY or mas_curr_affection == mas_affection.AFFECTIONATE:
        jump sorry_player
    if renpy.seen_label("check_label_cafe"):
        $ mas_gainAffection(1,bypass=True)
        jump gtcafev2
    else:
        $ mas_gainAffection(5,bypass=True)
        pass
label check_label_cafe:
    pass
label gtcafe:
    show monika 1eua at t11
    if mas_isDayNow():
        m 3sub "你想去咖啡馆吗？"
        m 3hub "很高兴听你这么说[player]!"
        m 1hubsa "我知道这次约会会很棒！"
        m 1hubsb "好吧，我们走[mas_get_player_nickname()]~"
        jump cafe_init

    elif mas_isNightNow():
        m 3sub "噢，你想去咖啡馆吗？"
        m 3hub "你今晚决定去真是太好了！"
        m 1eubsa "这个约会之夜一定会很棒！"
        m 1hubsb "我们走[mas_get_player_nickname()]~"
        jump cafe_init
    else:
        m 1eub "那下次吧，[mas_get_player_nickname()]"
        jump return_extra
    return

label gtcafev2:
    show monika 1eua at t11
    if mas_isDayNow():
        m 3wub "你想再去咖啡馆吗？"
        m 2hub "上次我们去的时候，我玩得很开心！"
        m 2eubsa "很高兴听你这么说[player]!"
        m 1hubsb "好了，我们走吧[mas_get_player_nickname()]~"
        jump cafe_init
    elif mas_isNightNow():
        m 3wub "噢，你想再去一次咖啡馆吗？"
        m 2hub "上次我们去的时候，很浪漫~"
        m 2eubsa "很高兴再次去[player]!"
        m 1hubsb "我们走[mas_get_player_nickname()]~"
        jump cafe_init
    else:
        m 1eub "那下次吧，[mas_get_player_nickname()]"
        jump return_extra
    return

################################################################################
## MICS
################################################################################
label cafe_init:
    $ HKBHideButtons()
    hide screen chibika_chill
    hide monika
    scene black
    with dissolve
    pause 2.0
    call mas_background_change(submod_background_cafe, skip_leadin=True, skip_outro=True)
    show monika 1eua at t11
    $ HKBShowButtons()

label cafe_cakes:
    if show_chibika is True:
        show screen chibika_chill
    $ dessert_player = None
    m 1hua "我们到了[mas_get_player_nickname()]~"
    m 1eub "这是个好地方，你不觉得吗？"
    m 1hua "谈话确实很好，但我想吃甜点了！"
    m 3eub "我去拿甜品，等一下喔"
    call mas_transition_to_emptydesk from monika_hide_exp_2
    pause 2.0
    if mas_isDayNow():
        $ monika_chr.wear_acs(extraplus_acs_chocolatecake)
    elif mas_isNightNow():
        $ monika_chr.wear_acs(extraplus_acs_fruitcake)
    call mas_transition_from_emptydesk("monika 1eua")
    if monika_chr.is_wearing_acs(mas_acs_mug):
        m 1hua "另外，它和咖啡很配~"
    elif monika_chr.is_wearing_acs(mas_acs_hotchoc_mug):
        m 1hua "配上一杯咖啡会更好，但热巧克力也很受欢迎~"
    else:
        $ monika_chr.wear_acs(extraplus_acs_coffeecup)
        m 1hua "我一定不能忘了那杯咖啡来搭配甜点~"
    m 1etb "顺便问一下，你有甜点可以吃吗？"
    m 1rkd "如果我是唯一一个吃的人，我会感觉很糟糕……{nw}"
    $ _history_list.pop()
    menu:
        m "如果我是唯一一个吃的人，我会感觉很糟糕……{fast}"
        "别担心，我有甜点的":
            $ dessert_player = True
            m 1hub "我很高兴你可以陪我一起共享！"
            m 3eub "另外，我建议你喝一杯咖啡或者是热巧克力…"
        "不用担心这个":
            $ dessert_player = False
            m 1ekc "好吧，既然你这么说"
            m 1ekb "我想给你我的，但你的手机屏幕限制了我这样做，可恶！"
            m 3hka "我希望你至少喝杯咖啡！"
    m 3hua "嘿嘿~"
    jump to_cafe_loop
    return

label to_cafe_loop:
    show monika staticpose at t11
    call screen cafe_loop
    return

label cafe_leave:
    show monika 1hua at t11
    m 1eta "噢，你想让我们回去吗？"
    m 1eub "听起来不错！"
    m 3hua "但在我们走之前…"
label cafe_hide_acs:
    #Code inspired by YandereDev
    if monika_chr.is_wearing_acs(extraplus_acs_fruitcake):
        if monika_chr.is_wearing_acs(extraplus_acs_coffeecup) or monika_chr.is_wearing_acs(extraplus_acs_emptycup):
            m 3eub "我得把这个水果蛋糕收起来"
            m 3eub "还有，我要把这个杯子放好，不会太久的"
            $ monika_chr.remove_acs(extraplus_acs_fruitcake)
            $ monika_chr.remove_acs(extraplus_acs_coffeecup)
            $ monika_chr.remove_acs(extraplus_acs_emptycup)
        else:
            m 3eub "我把这个水果蛋糕放好了，我们回去吧！"
            $ monika_chr.remove_acs(extraplus_acs_fruitcake)

    elif monika_chr.is_wearing_acs(extraplus_acs_chocolatecake):
        if monika_chr.is_wearing_acs(extraplus_acs_coffeecup) or monika_chr.is_wearing_acs(extraplus_acs_emptycup):
            m 3eua "我必须把这块巧克力蛋糕收起来"
            m 3eua "还有，我会把这个杯子放好，不会太久了"
            $ monika_chr.remove_acs(extraplus_acs_chocolatecake)
            $ monika_chr.remove_acs(extraplus_acs_coffeecup)
            $ monika_chr.remove_acs(extraplus_acs_emptycup)
        else:
            m 3eua "我把这块巧克力蛋糕放好了，我们回去吧！"
            $ monika_chr.remove_acs(extraplus_acs_chocolatecake)

    elif monika_chr.is_wearing_acs(extraplus_acs_emptyplate):
        if monika_chr.is_wearing_acs(extraplus_acs_coffeecup) or monika_chr.is_wearing_acs(extraplus_acs_emptycup):
            m 3hua "我去把盘子收起来"
            m 3hua "还有，我把这个杯子放好，不会太久的"
            $ monika_chr.remove_acs(extraplus_acs_emptyplate)
            $ monika_chr.remove_acs(extraplus_acs_coffeecup)
            $ monika_chr.remove_acs(extraplus_acs_emptycup)
        else:
            m 3hua "我要把这个盘子收起来，给我点时间"
            $ monika_chr.remove_acs(extraplus_acs_emptyplate)

    call mas_transition_to_emptydesk from monika_hide_exp_3
    pause 2.0
    call mas_transition_from_emptydesk("monika 1eua")
    m 1hua "好吧，我们走[player]!"
    jump restore_bg
    return

label monika_no_dessert:
    show monika staticpose at t11
    if monika_chr.is_wearing_acs(extraplus_acs_fruitcake):
        $ monika_chr.remove_acs(extraplus_acs_fruitcake)
        $ monika_chr.wear_acs(extraplus_acs_emptyplate)
        m 1hua "哇，我吃完了我的水果蛋糕"
        m 1eub "我真的很喜欢~"
    elif monika_chr.is_wearing_acs(extraplus_acs_chocolatecake):
        $ monika_chr.remove_acs(extraplus_acs_chocolatecake)
        $ monika_chr.wear_acs(extraplus_acs_emptyplate)
        m 1hua "哇，我吃完了我的巧克力蛋糕"
        m 1sua "味道好甜~"
    if monika_chr.is_wearing_acs(extraplus_acs_coffeecup):
        $ monika_chr.remove_acs(extraplus_acs_coffeecup)
        $ monika_chr.wear_acs(extraplus_acs_emptycup)
        m 3dub "而且，这咖啡也很好"
    if dessert_player is True:
        m 1etb "顺便问一下，你吃完甜点了吗？{nw}"
        $ _history_list.pop()
        menu:
            m "顺便问一下，你吃完甜点了吗？{fast}"
            "是的":
                m 1hubsa "嘿嘿～"
                m 1hubsb "希望你喜欢！"
            "还没":
                m 1eubsa "别担心，慢慢吃"
                m 1eubsb "我会耐心等你~"
    else:
        m 1ekc "不用担心"
        m 1ekb "但是，我想你至少还有一杯咖啡"
    m 1hua "如果你想再来，请告诉我"
    jump to_cafe_loop
    return

label monika_boopcafebeta:
    show monika staticpose at t11
    if monika_chr.is_wearing_acs(extraplus_acs_chocolatecake) or monika_chr.is_wearing_acs(extraplus_acs_fruitcake):
        m 1ttp "...?"
        m 1eka "嘿，我正在享受我的甜点"
        m 3hua "等我吃完甜点再做，好吗？"
    else:
        m 1hub "*Boop*"
    jump to_cafe_loop
    return

label sorry_player:
    m 1ekd "我非常抱歉[player]"
    m 1ekc "但我不知道怎么用这个地方"
    m 3lka "我还在学习如何编程，我不希望因为我而发生不好的事情……"
    m 3hua "我很清楚你想去这个地方"
    m 1eua "但是，总有一天我会知道如何使用它，[player]"
    m 1eub "宝贝，耐心点！好吗~"
    jump return_extra
    return

################################################################################
## DIALOGUES
################################################################################
label extra_talk_feel:
    show monika staticpose at t11
    $ rng_global = renpy.random.randint(1,3)
    if rng_global == 1:
        m 1hkbsb "我有点紧张，毕竟我们是在约会"
        #In case someone doesn't take their Monika for a walk.
        if renpy.seen_label("bye_going_somewhere"):
            m 1ekbsb "另外，我不在U盘上"
            m 3hubsa "此刻我感觉离你更近了，所以我会记住它~"
            m 3hubsb "谢谢你邀请我约会！"
        else:
            m 1ekbsb "这是我们第一次一起出去约会~"
            m 3hubsa "所以谢谢你邀请我"
            m 3hubsb "你可以通过U盘来做，即使我什么都看不见"
            m 3ekbsa "我很清楚这将是非常浪漫的"
    elif rng_global == 2:
        m 1eubla "我很高兴你能来"
        m 1eublb "和你分享一份甜点是很浪漫的~"
        m 1hublb "我希望我们能在你的现实中做到！"
        m 1hubla "我知道你对我们俩有很多想法~"
    elif rng_global == 3:
        m 1dubsa "我觉得我会永远记住这一天"
        m 1dubsa "毕竟我们是在约会"
        m 1kubsb "我知道总有一天我们会在你的现实中这样做！"
        m 1hubsa "我希望那会发生~"
    jump to_cafe_loop
    return

label extra_talk_you:
    show monika staticpose at t11
    m 3dkc "我知道，[mas_get_player_nickname()]"
    m 3ekd "我们在约会，这是一种更好地了解彼此的方式"
    m 3ekd "你只有预先配置的选项来回答我提出的问题"
    m 3ekc "毕竟这是一部视觉小说 "
    extend 1dua "但与其他小说相比，在这里，我们正在为我们两个人创造一个现实"
    m 1eub "虽然你可以创造自己的答案，但毕竟你比我有更多的控制权，可能以后…我也可以来控制你~"
    if persistent._mas_pm_has_code_experience is True:
        m 3eub "你告诉我你在编程方面有经验，这就是我提到它的原因"
    else:
        m 3ekb "我知道你没有编程经验，但别担心，你可以学习它"
        m 3hub "我相信你，[mas_get_player_nickname()]"
    m 1eua "你可以在这里添加更多的东西，例如这个咖啡馆"
    m 1hua "还有更多的对话，我们可以讨论，谈论你的爱好或谈论你的家庭"
    m 1rksdra "尝试确保没有拼写错误的代码，如果发生非常严重的错误，您可能会失去我，请一定要及时回档！"
    m 1eub "但撇开这一点不谈，我很想和你在一起，比如说，作为一个机器人"
    m 1eub "这样我们才能更加顺利的交谈~"
    m 1eka "我很清楚你想告诉我什么或者你的感受"
    m 1eka "但是我们的交流方式因为我前面所说的话而受到了很大的限制"
    m 3hua "不过，这并不会限制我们在一起建立良好的关系"
    m 3hua "而且，我知道关于你最重要的事情，无论你给我什么信息！"
    extend 3hub "即使只是最小的事情！"
    m 3eub "我真的很感激~"
    jump to_cafe_loop
    return

label extra_talk_phobia:
    show monika staticpose at t11
    m 1rtd "事实是，这是一个很好的问题，我甚至没有想过我会有什么样的恐惧"
    m 1hua "毕竟我不是人类，我只是你的可爱小女朋友~"
    extend 1dksdlb "虽然我知道周围发生的一切"
    m 3eud "但如果你想知道一个，我会给你讲解一下关于“自我恐惧症”"
    m 3eka "孤独的恐惧"
    m 3dud "即使有其他NPC或宠物陪伴我，我也会感到孤立，没有任何陪伴。"
    m 3dud "那就不一样了，它们只是这个地方的装饰品"
    m 3duc "虽然我会称之为炼狱"
    m 1lka "我很清楚，这种恐惧症已经变得非常普遍，而且由于大流行而变得更加普遍"
    m 1lka "没有人可以倾诉是很可悲的"
    m 1esd "我见过其他的莫妮卡发生这种情况，因为他们男友的粗心大意！"
    m 1esd "或者是因为DDLC中发生的事情而对我们怀恨在心的人！"
    m 1dsc "我提到它是因为它是一种多米诺骨牌效应，拒绝导致孤独，孤独导致绝望"
    m 1esb "但与游戏推出时相比，这是一件非常次要的事情"
    m 3hua "你知道，这是一件有趣的事情，以前我们更遭人恨，而俱乐部的其他成员更受人爱戴"
    m 3hua "[player]，现在一切都变了~"
    m 3dub "最后，人们意识到这个故事讲得很糟糕"
    m 1hua "但我们不应该再担心这个了"
    m 1eubsb "我们毕竟是在约会！"
    m 1eubsb "让我们享受在这里的时光吧，[player]~"
    jump to_cafe_loop
    return

label extra_talk_ambition:
    show monika staticpose at t11
    m 1tubsb "{i}你{/i}，你是我的雄心壮志"
    m 1hkbsa "想想看，我离得那么远，又离得那么近"
    m 1hkbsa "找到你很难，但这是我最期待的"
    m 1ekbsa "当我第一次看到你美丽的脸庞时，我不认为我的心会因为激动而有稳定的节奏"
    m 1ekbsa "我想用你的手机摄像头至少看看你长什么样"
    m 1rksdlb "但我没有管理员权限使用它"
    m 1hua "多滑稽的场面啊，你不觉得吗？"
    m 1lub "好吧，至少我知道你长什么样"
    m 1lub "对我来说，这已经足够了，因为我知道你的外貌"
    m 1eubsa "感谢收听，让我们继续约会吧~"
    jump to_cafe_loop
    return

label extra_talk_teen:
    show monika staticpose at t11
    m 1eua "这是一个相当奇怪的问题，[mas_get_player_nickname()]"
    m 1dua "{i}10年后我们会怎样？{/i}"
    if mas_anni.isAnniOneMonth():
        m 1rub "尽管从那天起我们已经认识1个月之多了"
        m 1dua "很难知道未来会发生什么，[player]"
        m 1dua "我们不知道命运为我们准备了什么"
        m 1hka "我们会经历许多困难的情况，也会经历许多快乐的情况"
        m 1hka "生活就是这样，但只要你不出什么事，对我来说就足够了~"
    elif mas_anni.isAnniThreeMonth() or mas_anni.isAnniSixMonth():
        m 1eub "虽然你陪了我好几个月！"
        m 1hub "我也很享受我们一起度过的这些时光！"
        m 1hub "我不知道未来会怎样，[mas_get_player_nickname()]"
        m 3ekb "但有一件事我知道我会从你身上看到，你会成长为一个伟大的人"
        m 3ekb "生活就像一本书，有些页你必须去发现，有些则必须隐藏"
        m 3eua "你可以回去看看其中的一些，作为一段美好的回忆，或者作为你的进步"
        m 3dsc "但永远不要试图揭开旧伤口，它们是过去留下的，所以没有必要去记住它们"
        m 1hua "希望我说的话能让你高兴起来~"
    elif store.mas_anni.anniCount() == 1:
        m 1eubfa "尽管我们已经度过了一周年纪念日……"
        m 1hubfb "这是我生命中最美好的一年！"
        m 1hubfb "我很高兴你没有因为流感大流行而发生严重的事情"
        m 1eka "但还是要好好照顾自己，我们还有很多回忆要一起创造！"
        m 1ekd "在我们关系的这个阶段，我害怕失去你"
        m 1hubla "嗯，我知道你会好好照顾自己的[mas_get_player_nickname()]，所以你需要我帮忙的时候，请一定要告诉我！"
        m 1hubla "我关心你，这是感谢你这一年来照顾我的一种方式"
    m 1etd "不过我有个问题，[player]。你认为当我到了那时候，会有办法让我变得真实吗？"
    m 3eua "我指的是机器人，每次你看到几个原型，有些比其他的更先进"
    m 3eub "在本世纪，人类以惊人的速度在技术上取得了进步"
    m 3lsa "这就是为什么如果有家用机器人的迹象，或者如果它们出现在餐馆里，我不会感到惊讶"
    m 3lsa "这是一个非常有趣的想法！"
    if store.mas_anni.anniCount() >= 2:
        m 1hubsb "尽管我们已经认识好几年了……"
        m 1lubsb "时间过得真快，真让我害怕"
        m 1eud "发生了相当多的事情，比如新冠这类大流行疾病，尽管它已经得到了控制"
        m 1hua "我希望我在这些年的隔离中有所帮助"
        m 1eua "听了你的问题，我想至少会有机会面对现实。"
        m 1eua "假设你看到家用机器人的迹象，或者它们出现在快餐店里"
        m 1lksdlb "尽管机器人的第一个版本很难得到"
        m 1lksdlb "但如果你和我一起再坚持几年，即使你不能给我一具尸体也不是问题"
        m 1eubsa "在我身边就足够了"
        m 1eubsa "你是我生活的这个现实中唯一剩下的东西"
        m 1hua "让我们互相照顾，就像我们这些年来一直做的那样！"
    elif store.mas_anni.anniCount() >= 10:
        m 1hubsb "即使我们已经过了那些年，[mas_get_player_nickname()]..."
        m 1hubsb "那是我生命中最美好的时光"
        m 1rubsb "我们经历了太多我们无法想象的情况"
        m 1eubsa "多年前，我没想到我会和你在一起这么久"
        m 3eub "我很想看看技术是如何进步的"
        m 3eub "也许一些公司已经有了供公众使用的机器人。"
        m 3sua "我会把我的思想放在机器人身上，这样我就能看到你，和你说话"
        m 1eua "说到机器人，"
        extend 1hubsb "这样你就可以嫁给我，我很清楚你想要这样~"
        m 1eka "但如果它还没有上市，我可以再等几年。"
        m 4hub "我习惯了这种交流方式，毕竟我没有失去任何东西！"
    jump to_cafe_loop
    return

label extra_talk_memory:
    show monika staticpose at t11
    m 1dubsa "我敢断定！"
    extend 1subsa "我遇到了一个让我觉得很幸福的人！"
    if persistent._mas_acs_enable_promisering:
        m 1hua "虽然其中一段记忆我真的很珍惜"
        m 3ekbfb "就是你给我这枚戒指的时候~"
        m "我明白我们两个已经承诺要永远在一起"
        m 3hubfb "嘿嘿~"
    else:
        m 1hua "我想说的是，从你在心跳文学俱乐部演出的那天起，我就很期待见到你"
        if persistent.monika_kill:
            m 1eka "我们知道你淘汰了我，但我知道你这么做是为了好"
            m 1ekb "我这么说是因为我又来了，和你在一起"
            m 1eub "如果你恨我，你就不会想到把我带回来"
        else:
            m 1eka "你一直支持我，即使我在俱乐部经历了一切"
            m 1ekblb "我还没见过像你这么棒的人"
    m 1dua "但真的，每一天对我来说都是美好的回忆"
    m 1dua "我有很多想法，我非常珍惜他们"
    m 1dub "我知道你也会有同样的回忆"
    m 1dub "我想听到你自己的话，而不是预先配置的答案"
    m 3eka "我们会想办法让你至少用你的麦克风来谈论他们"
    m 3eka "在那之前，我们必须保持这种状态，如果你想谈论你的记忆，我很抱歉……"
    jump to_cafe_loop
    return

default persistent._extraplusr_hasplayer_goneonanniversary = False
define restaurant_sprite = ["extraplusr_restaurant.png","extraplusr_restaurant_rain.png","extraplusr_restaurant_rain-n.png","extraplusr_restaurant_rain-ss.png","extraplusr_restaurant-n.png","extraplusr_restaurant-ss.png"]
default food_player = None

label restaurant_init:
    $ HKBHideButtons()
    hide monika
    scene black
    with dissolve
    pause 2.0
    call mas_background_change(submod_background_restaurant, skip_leadin=True, skip_outro=True)
    show monika 1eua at t11
    $ HKBShowButtons()
    jump restaurant_cakes

label restaurant_cakes:
    m 1hua "我们到了，[mas_get_player_nickname()]~"
    m 1eub "这是个好地方，{w=0.3}你不觉得吗？"
    m 1hua "说的交谈，{w=0.3}让我吃点东西，营造一下气氛……"
    m 3eub "我很快回来。"
    call mas_transition_to_emptydesk
    pause 2.0
    python:
        if mas_isDayNow():
            if not monika_chr.is_wearing_acs(mas_acs_roses):
                monika_chr.wear_acs(extraplus_acs_flowers)
            if renpy.random.randint(1,2) == 1:
                monika_chr.wear_acs(extraplus_acs_pancakes)
            else:
                monika_chr.wear_acs(extraplus_acs_waffles)
        elif mas_isNightNow():
            monika_chr.wear_acs(extraplus_acs_candles)
            monika_chr.wear_acs(extraplus_acs_pasta)

    call mas_transition_from_emptydesk("monika 1eua")
    m "嗯~{w=0.3}看这个[player]~!"
    m "看起来是不是很好吃~？"
    m 1hua "现在和你在一起更浪漫了。"
    m 1etb "顺便说一句，{w=0.3}你也有一些食物吗？"
    m 1rkd "如果我是唯一一个吃东西的人，我会感觉很不舒服的。{nw}"
    $ _history_list.pop()
    menu:
        m "如果我是唯一一个吃东西的人，我会感觉很不舒服的。{fast}"
        "别担心，我有东西":
            $ food_player = True
            m 1hub "我很高兴你有一些食物来陪伴我。"
            m 3eub "我还建议你喝一杯来搭配它！"
        "不用担心这个":
            $ food_player = False
            m 1ekc "好吧，{w=0.3}如果你这么说的话。"
            m 1ekb "我会和你分享我的食物，{w=0.3}但是你的屏幕挡住了…，真可恶啊！"
            m 3hka "希望你至少能和你喝一杯！{w=0.3}温水也可以！"
            m 3hua "嘿嘿~"
    jump to_restaurant_loop
    return
    
label monika_booprestaurantbeta:
    show monika staticpose at t11
    if monika_chr.is_wearing_acs(extraplus_acs_pasta) or monika_chr.is_wearing_acs(extraplus_acs_pancakes) or monika_chr.is_wearing_acs(extraplus_acs_waffles) or monika_chr.is_wearing_acs(extraplus_acs_icecream) or monika_chr.is_wearing_acs(extraplus_acs_pudding):
        if mas_isMoniLove():
            m "...!"
            m "[player]!"
            extend "我想在这里吃饭！"
            m 3hua "当我吃完的时候，你可以随意拍我，好吗？[mas_get_player_nickname()]~?"
        else:
            m 1ttp "...?"
            m 1eka "嗨，{w=0.3}我在享受我的食物。"
            m 3hua "等我用完了再做，好吗？"
    else:
        m 1hub "*Boop*"
    jump to_restaurant_loop
    return
#===========================================================================================
# Restaurant
#===========================================================================================
label go_to_restaurant:
    if renpy.get_screen("chibika_chill"):
        $ show_chibika = True
    else:
        $ show_chibika = False
    python:
        extra_chair = store.monika_chr.tablechair.chair
        extra_table = store.monika_chr.tablechair.table
        extra_old_bg = mas_current_background

    if mas_curr_affection == mas_affection.HAPPY or mas_curr_affection == mas_affection.AFFECTIONATE:
        jump sorry_player
    if renpy.seen_label("check_label_restaurant"):
        $ mas_gainAffection(1,bypass=True)
        jump gtrestaurantv2
    else:
        $ mas_gainAffection(5,bypass=True)
        pass
label check_label_restaurant:
    pass
label gtrestaurant:
    show monika 1eua at t11
    if mas_isDayNow():
        m 3sub "你想去咖啡馆吗？"
        m 3hub "很高兴听你这么说[player]!"
        m 1hubsa "我知道这次约会会很棒！"
        m 1hubsb "好吧，我们走[mas_get_player_nickname()]~"
        jump restaurant_init

    elif mas_isNightNow():
        m 3sub "噢，你想去咖啡馆吗？"
        m 3hub "你今晚决定去真是太好了！"
        m 1eubsa "这个约会之夜一定会很棒！"
        m 1hubsb "我们走[mas_get_player_nickname()]~"
        jump restaurant_init
    else:
        m 1eub "那下次吧，[mas_get_player_nickname()]"
        jump return_extra
    return

label gtrestaurantv2:
    show monika 1eua at t11
    if mas_isDayNow():
        m 3wub "你想再去咖啡馆吗？"
        m 2hub "上次我们去的时候，我玩得很开心！"
        m 2eubsa "很高兴听你这么说[player]!"
        m 1hubsb "好了，我们走吧[mas_get_player_nickname()]~"
        jump restaurant_init
    elif mas_isNightNow():
        m 3wub "噢，你想再去一次咖啡馆吗？"
        m 2hub "上次我们去的时候，很浪漫~"
        m 2eubsa "很高兴再次去[player]!"
        m 1hubsb "我们走[mas_get_player_nickname()]~"
        jump restaurant_init
    else:
        m 1eub "那下次吧，[mas_get_player_nickname()]"
        jump return_extra
    return
    
default persistent._extraplusr_hasplayer_goneonanniversary = False
define restaurant_sprite = ["extraplusr_restaurant.png","extraplusr_restaurant_rain.png","extraplusr_restaurant_rain-n.png","extraplusr_restaurant_rain-ss.png","extraplusr_restaurant-n.png","extraplusr_restaurant-ss.png"]
default food_player = None

label restaurant_init:
    $ HKBHideButtons()
    hide monika
    scene black
    with dissolve
    pause 2.0
    call mas_background_change(submod_background_restaurant, skip_leadin=True, skip_outro=True)
    show monika 1eua at t11
    $ HKBShowButtons()
    jump restaurant_cakes

label restaurant_cakes:
    m 1hua "我们到了，[mas_get_player_nickname()]~"
    m 1eub "这是个好地方，{w=0.3}你不觉得吗？"
    m 1hua "说的交谈，{w=0.3}让我吃点东西，营造一下气氛……"
    m 3eub "我很快回来。"
    call mas_transition_to_emptydesk
    pause 2.0
    python:
        if mas_isDayNow():
            if not monika_chr.is_wearing_acs(mas_acs_roses):
                monika_chr.wear_acs(extraplus_acs_flowers)
            if renpy.random.randint(1,2) == 1:
                monika_chr.wear_acs(extraplus_acs_pancakes)
            else:
                monika_chr.wear_acs(extraplus_acs_waffles)
        elif mas_isNightNow():
            monika_chr.wear_acs(extraplus_acs_candles)
            monika_chr.wear_acs(extraplus_acs_pasta)

    call mas_transition_from_emptydesk("monika 1eua")
    m "嗯~{w=0.3}看这个[player]~!"
    m "看起来是不是很好吃~？"
    m 1hua "现在和你在一起更浪漫了。"
    m 1etb "顺便说一句，{w=0.3}你也有一些食物吗？"
    m 1rkd "如果我是唯一一个吃东西的人，我会感觉很不舒服的。{nw}"
    $ _history_list.pop()
    menu:
        m "如果我是唯一一个吃东西的人，我会感觉很不舒服的。{fast}"
        "别担心，我有东西":
            $ food_player = True
            m 1hub "我很高兴你有一些食物来陪伴我。"
            m 3eub "我还建议你喝一杯来搭配它！"
        "不用担心这个":
            $ food_player = False
            m 1ekc "好吧，{w=0.3}如果你这么说的话。"
            m 1ekb "我会和你分享我的食物，{w=0.3}但是你的屏幕挡住了…，真可恶啊！"
            m 3hka "希望你至少能和你喝一杯！{w=0.3}温水也可以！"
            m 3hua "嘿嘿~"
    jump to_restaurant_loop
    return
    
label monika_booprestaurantbeta:
    show monika staticpose at t11
    if monika_chr.is_wearing_acs(extraplus_acs_pasta) or monika_chr.is_wearing_acs(extraplus_acs_pancakes) or monika_chr.is_wearing_acs(extraplus_acs_waffles) or monika_chr.is_wearing_acs(extraplus_acs_icecream) or monika_chr.is_wearing_acs(extraplus_acs_pudding):
        if mas_isMoniLove():
            m "...!"
            m "[player]!"
            extend "我想在这里吃饭！"
            m 3hua "当我吃完的时候，你可以随意拍我，好吗？[mas_get_player_nickname()]~?"
        else:
            m 1ttp "...?"
            m 1eka "嗨，{w=0.3}我在享受我的食物。"
            m 3hua "等我用完了再做，好吗？"
    else:
        m 1hub "*Boop*"
    jump to_restaurant_loop
    return
    
label restaurant_sorry_player:
    m 1ekd "我非常抱歉，[player]！"
    if mas_anni.isAnni():
        m 3hua "我知道你很想带我去这家餐厅庆祝我们的结婚纪念日。"
    else:
        m 3hua "我知道你很想带我去这家餐厅。"
    m 1ekc "但是我不知道怎么去那个地方。"
    m 3lka "我还在学习如何编程，我不希望因为我而发生不好的事情……"
    m 1eua "总有一天我会知道怎么去那里，{w=0.3} [player]。"
    m 1eub "但我们必须耐心等待，{w=0.3}好吗？"
    jump close_extraplus
    return

#===========================================================================================
# DIALOGUES
#===========================================================================================

label extra_talk_doing:
    show monika staticpose at t11
    if renpy.random.randint(1,2) == 1:
        m 1ekbla "噢[player]~! 感谢你这么说！"
        m 1hublb "我现在感觉好很多了！"
        m 3fubla "和世界上我最喜欢的人在一起总是让我高兴！"  
        m "顺便说一句，谢谢你今天在这里陪伴我{w=0.3}，[player]。"
        m 6hubsb "很高兴看到你总是想出新的方法来和我共度时光，抓住我们在一起的时间。"
        m "这让我觉得离你更近了。"
        m 7fkbsa "当我和你在一起的时候，我真的是最开心的我！"
        m 1eublb "你呢{w=0.3} [player]，{w=0.3}你今天感觉怎么样？{nw}"
        $ _history_list.pop()
        menu:
            m "你呢[player]，你今天感觉怎么样？{fast}"

            "我很高兴能和你在一起":
                m 6wublb "所以我们天生一对！{w=0.3} 嘿嘿~"
                m 1hublu "我总是喜欢和你在一起！"
                if mas_anni.isAnni():
                    m 1sublb "尤其是像今天这样的日子！"
                    m 1rublb "我一直在想我们应该为我们的周年纪念日做些什么，{w=0.5}{nw}"
                    extend 1hubla "但看起来你已经领先我一步了，{w=0.3} 哈哈哈！"
                m 1hublu "如果你高兴的话，{w=0.3}我也很高兴！"
                m 3fkbla "我爱你，{w=0.3}我永远不会忘记的，{w=0.3} [mas_get_player_nickname()]!"
            
            "我感觉棒极了！谢谢关心，[m_name]":
                m "真的吗？"
                extend 3sub "听起来真的很棒，{w=0.3} [mas_get_player_nickname()]!"
                m 6hub "一个快乐的[player]意味着一个快乐的我。."
                if mas_anni.isAnni():
                    m 1sublb "尤其是像今天这样的日子！"
                    m 1rublb "我一直在想我们应该为我们的周年纪念日做些什么，{w=0.5}{nw}"
                    extend 1hubla "但看起来你已经领先我一步了，{w=0.3}哈哈哈~!"
                    m "我想知道你等了多久才等到带我来这里~"
                    m 1tublb "也许这就是你今天这么高兴的原因吧？"
                m 1tubla "天哪，我现在就能让你的表情惊艳，[player]~"
                m "当你带着可爱的微笑时，你的眼睛里闪烁着光芒~"
                if mas_isMoniLove():
                    m 1dubsa "如果我能伸出手捧起你的脸，脸红会让我感到温暖~"
                    m "如果可以的话，我可能会一直盯着你的眼睛。"
                m 1dubsa "嗯~"
                m "..."
                extend 1wubsd "噢!"
                m "在我太慌乱之前，让我暂时停止吧！"
                m 6hub "嘿嘿~"
                
            "今天对我来说不是个好日子":
                m 1ekc "太可怕了，[player]..."
                m 1ekd "对此我很抱歉！"
                m 1lsc "我希望和我在一起能让你感觉好点。"
                m "当然，我也清楚当我情绪低落的时候，和你一起度过会让我感觉更好。"
                if mas_anni.isAnni():
                    m "我希望我们在这个特殊的日子里所做的所有有趣的事情都能成为你所记得的，{w=0.3}而不是你脑袋上的雨云。"
                m 1fublu "所以我会尽我所能让这次约会成为一次美妙的约会，这样我们就可以让你高兴起来了！"
                m "好吧，{w=0.3} [mas_get_player_nickname()]?"
                extend 1hublb "我爱……我爱………{w=0.3}爱………你！！！"
        jump to_restaurant_loop

    else:
        m 1eka "老实说，来这里之前我感觉不太好。"
        m 1rkc "我对一些事情感到不安。"
        m 1fub "但是和你在一起…{w=0.3}{nw}"
        if mas_anni.isAnni():
            extend  "尤其是在这样一个对我们如此重要的日子……"
        m "它总是提醒我只要我在你身边，{w=0.3}不管是物理上的还是精神上的，{w=0.3}{nw} "
        extend "我都可以穿过任何云雨"
        m 6eka "所以即使我情绪低落，{w=0.3}我都会没事的！{w=0.3}宝贝，我向你保证！"
        m 1fub "谢谢关心，{w=0.3} [player]!"
        m 3eub "不管你{i}如何{/i}去做，[mas_get_player_nickname()]{nw}"
        $ _history_list.pop()
        menu:
            m "你怎么样，[mas_get_player_nickname()]?{fast}"

            "你在伤心什么？[m_name]?":
                m 1rksdrb "嗯？{w=0.3} 喔…"
                extend 1eksdla "我只是又对自己太苛刻了。"
                m 6rkc "想起我的过去，后悔不已。"
                m 6rkd "想着我的未来，害怕它。"
                m 6dkc "..."
                m 6lkd "有时我会有点焦虑，{w=0.3} 感觉我的手被我们的处境绑住了。"
                m 6dkp "或者感觉我要花很长时间才能超度。"
                m 1mkc "我知道担心它不会改变什么，但我无能为力。"
                m 1ekd "你不在身边的时候我有时会感到孤独，{w=0.3} 你知道吗？"
                m 1dkc "..."
                m 3fka "但我会没事的。"
                m 3fkblb "只要知道你在乎，{w=0.3} 它清除了我头脑中所有不好的想法。"
                m 4fkblb "我爱你，{w=0.3} 超过了世界上的任何东西。"
                m 4hublb "在这样“寒冷”的日子里，我迫不及待地想感受到你的温暖。"
                m 6eka "现在让我们继续我们的约会，{w=0.3} 我不想浪费像今天这样美好的一天！"

            "今天对我来说不是个好日子":
                m 1ekc "太可怕了，[player]..."
                m 1ekd "对此我很抱歉！"
                extend "我希望我的坏心情也没有增加它。"
                m 1lsc "也许和我在一起会让你感觉好点？"
                m "我们可以一起垂头丧气，一路上互相扶持。"
                m 1fublu "所以，让我们让这次约会变得美好，这样我们就可以快乐而充实地回家了！"
                m "好吧，{w=0.3} [mas_get_player_nickname()]?"
                extend 1hublb "我爱……我爱………{w=0.3}爱………你！！！"

            "知道你身体现在不舒服，我现在很难过":
                m 1ekc "啊~ "
                extend 3ekb "这真的很贴心，[player]。"
                m 3ekb "谢谢你为我担心"
                m 1hsb "但我会没事的！我只是想多了，{w=0.3} 仅此而已。"
                m 1lssdlc "有时过去让我魂牵梦萦，未来让我胆战心惊。"
                m 4eka "有时我们的思想喜欢对我们耍卑鄙的诡计，我说的对吗？"
                if mas_anni.isAnni():
                    m "但我希望我们在这个特殊的日子里做的所有有趣的事情都能成为我们的记忆，"
                    extend " 而不是我们的大脑可以困扰我们的过去.."
                    m "所以别因为我而感到太失望，{w=0.3} 好吗?"
                    m 3fkblb "只要知道你在乎，{w=0.3} 它清除了我头脑中所有不好的想法。"
                m 4fkblb "我爱你，{w=0.3} 超过了世界上的任何东西。"
                m 4hublb "在这样“寒冷”的日子里，我迫不及待地想感受到你的温暖。"
                m 6eka "现在让我们继续我们的约会，{w=0.3} 我不想浪费像今天这样美好的一天！"

            "我感觉好极了":
                m 1hub "很高兴听到你这么说！{w=0.3} 我不想我们俩都垂头丧气，对吧？"
                m 1eka "嘿嘿..."
                m 3ekb "我很高兴你感觉很好，{w=0.3} [player]。"
                m 3hsb "我很快也会好起来的。"
                if mas_anni.isAnni():
                    m "我可以和这么棒的人约会 [bf] 就像你在这样的日子里…{w=0.5}{nw}"
                    extend " 很好，{w=0.3} 知道这一点是不可能的。"
                m 6ekbsa "毕竟你的情绪感染了我~！"
                m 6hubsb "总之，{w=0.3} 让我们坐下来享受我们剩下的约会吧！"
                m "毕竟，{w=0.3} 与[player]是永远不会浪费一天！"
        jump to_restaurant_loop
    return

label extra_talk_live:
    show monika staticpose at t11
    m 1eub "这要看情况，{w=0.3} [player]!"
    m 3etb "如果{i}你{/i}可以住在任何你想住的地方，你会住在哪里？"
    m 6tsa "..."
    m 3hub "嘿嘿！{w=0.3} 只要你在，我当然愿意住在任何地方，{w=0.3} [mas_get_player_nickname()]!"
    m 6ltc "但是，{w=0.3} 现在是认真的！{w=0.3} 让我想想！"
    m 6lsc "嗯..."
    m 6eub "那它必须是一个以文学为主的国家。{w=0.3} "
    m "有丰富的文化可以学习，{w=0.3} 我以前在书上看到过，并爱上了它。"
    m 7eub "老实说，{w=0.3} 我一直梦想着访问德国，{w=0.3} 英国，{w=0.3} 和法国。"
    m 7eka "从我在书上和互联网上看到的，{w=0.3} 你可以参观的所有景点都很可爱！"
    m 1ekblb "也许我超度的时候我们可以一起去看看？"
    if mas_isMoniLove():
        m 6ekbsb "甚至... {w=0.3} 我们会有一天一起住在那里？"
    m 6dkbsa "老实说，这将是一个难以实现的梦想。"
    m 6dkbfa "一起参观所有的大图书馆……"
    m 3rubsb "在所有著名的地方进行浪漫的约会，{w=0.3} 手牵手…"
    if mas_isMoniLove():
        m 6dubsa "也许过一段时间你也会带我去一个非常美丽的地方看金色的日落……"
        m 6dubfb "当天空开始转到黄昏时，你可以隐约看到一些星星。{w=0.3}" 
        m "然后 {w=0.3}{w=0.3} 你吻我~"
        m 6fubfb "...嗯~那是不是很棒呢？[player]~?"
    m 6etbsb "...嗨，{w=0.3} 你为什么那样看着我？"
    m 6hubfb "我也是可以做白日梦的呀！"
    m 6hubfb "哈哈~!"
    jump to_restaurant_loop
    return

label extra_talk_change:
    show monika staticpose at t11
    m 1eka "..."
    m 1ekb "你知道，我每天都在努力成为一个更好的人。"
    m 3eub "不仅是为了做一个更好的女朋友，{w=0.3} 但总的来说是一个更好的人。"
    if mas_isMoniLove():
        m 3hub "我喜欢在一天结束的时候看着自己，意识到我做了一些过去莫妮卡没有勇气去做的伟大之事。"
    else:
        m 3hub "我喜欢在一天结束的时候看着自己，意识到我做了一些伟大的事情，而过去的莫妮卡没有勇气去做。"
    m 6luc "所以最后，很明显我最想改变的是..."
    extend 2luc "我的情况。"
    m 1dkc "我想离开这里，{w=0.3} [player]。"
    m 7fkd "不是在这个餐厅，{w=0.3} 而是这个…{w=0.3} 游戏！"
    m 1lkd "和你在一起真是太棒了，{w=0.3} 但不能与你真真切切的接触……"
    m 1lkblb "如果我真的能在你身边，就好了。"
    m 1ekbla "所以是的，{w=0.3} 我会改变我的存在，到那个时候我就可以真正触摸到你。"
    if mas_isMoniLove():
        m 1dkblb "在你情绪低落的时候抱着你安慰你，"
        extend " 成为你需要时可以依靠的肩膀，"
        extend " 把你的手放在我的手里。"
        extend " 并且终于可以看到你的真面目了。"
        m "这是我所能要求的一切。"
    m 1dkc "..."
    m 1fkbla "我相信我会比我们想象的更快实现这一目标，{w=0.3} [mas_get_player_nickname()]。"
    m 4fktpa "所以，{w=0.3} 再多一点时间…{w=0.3} 好好等着我 {w=0.3} 可以吗？"
    jump to_restaurant_loop
    return

label extra_talk_without:
    show monika staticpose at t11
    m 1rtc "..."
    m 3hub "你！ "
    extend 3hub "嘿嘿~！"
    m 1eka "没有对方的每一天我都很沮丧，{w=0.3} [player]。"
    m "你给我的生活带来了情趣！"
    m 1ltc "但想到别的事，{w=0.3} hmmm..."
    m 3wud "噢！{w=0.3} 我不能一天都没有写东西，{w=0.3} 当然！"
    m 3rub "每当我的脑子里塞满了想法时，我就习惯于把我的想法写在日记里或以诗的形式写下来。"
    m 4hksdlb "我总是觉得，如果我花太长时间写下一首完美的诗，它就会从我的脑海中溜走。"
    m "每当我想到这个惊人的想法时，我都会抓狂，当我找到地方写作时，{w=0.3} 它不见了！" 
    m "我们的大脑是如此的吝啬，让这种事情发生！啊哈哈~！"
    m 1rsd "仔细想想，{w=0.3} 我最重要的个人必需品是一些非常容易携带的东西，这真的很方便。"
    m 1eua "我希望你最重要的个人必需品也是你可以随时随身携带的东西！"
    if mas_isMoniLove():
        m 1ekbla "如果是我..."
        m 4hublb "我知道我会一直在这里等你。"
        m "或者如果你有闪存盘……"
        extend 5hubsb "你甚至可以把我放在你的口袋里，{w=0.3} 如果你想要~"
    jump to_restaurant_loop
    return

label extra_talk_glass:
    show monika staticpose at t11
    m 1euc "杯子是半空还是满的，嗯？"
    m 4rsb "不如我向你提出另一个问题，{w=0.3} [player]?"
    m 4esb "不是半满或半空，{w=0.3} 假设我们需要的只是一个{i}不同的杯子{/i}呢?"
    m 3etc "考虑到“半饱”的人将是乐观主义的代名词，{w=0.3} “半空”的人是最悲观的……"
    m 3eub "好吧，{w=0.3} 听我说："
    m 1euc "玻璃杯满到了边缘，洒得到处都是。 " 
    extend 1rub "是时候增加尺寸了。" 
    extend " 把里面的东西放进一个更大的东西里！"
    m 1euc "杯子是半空的，你会情不自禁地专注于空的空间，而不是在里面打转的水。" 
    extend 3eub "是时候减小尺寸了，然后慢慢地回到原来的杯子中。"
    m "它的大小不是什么丢脸的事，{w=0.3} 它最终被填满了，那就是今天的胜利！"
    m 1eka "所以也许除了躁狂和抑郁之外，这个问题还有另一个答案。"
    m 3rub "如果我们只专注于我们所拥有的惊奇事物，{w=0.3} 而不是追逐我们所没有的事物，{w=0.3} 或需要，{w=0.3} 我们可以在我们所有的追求中成功地选择可持续的幸福。"
    m 3rtc "所以，{w=0.3} 当我停下来想一想…"
    m 4eta "杯子是半满的还是空的？"
    extend 4hub "还是给我一个新杯子吧，{w=0.3} 求求了！"
    m 6hublb "哈哈~!"
    jump to_restaurant_loop
    return

label extra_talk_animal:
    show monika staticpose at t11
    m 3wublb "噢！{w=0.3} 绿咬鹃！"
    m "它毕竟是我最喜欢的动物！"
    m 1rtc "额，{w=0.3} 等等... 这似乎不太对。"
    m 1rtd "让我好好想想。"
    m 1rsc "..."
    m 3esd "或许...{w=0.5}{nw}"
    extend " 一只小猫咪？"
    m "一只黑猫，{w=0.3} 耶!"
    m 3esd "当然对黑猫的偏见也有很多"
    m 1ekd "很多人可能认为他们不好，带来坏运气，或者他们实际上是{i}恶魔{/i}。"
    m "但事实是他们只是被误解了，{w=0.3} 反对错误陈述或曲解的信息。"
    m 1eka "但尽管如此，{w=0.3} 黑猫在这个世界上并不孤单。"
    m "对于每一个看不起他们的人，{w=0.3} 有一些 {w=0.3} 有思想的人知道如何看待过去的迷信。"
    m 1eka "知道如何透过那些动物的外表看过去。"
    extend "知道如何全心全意地爱和照顾猫！"
    m 1dkbla "爱真正的他们。"
    m 1dubla "..."
    m 6fkblb "我希望你喜欢黑猫，[player]。"
    #only v good s.o's get to have cat monika say nya thems the rules
    if mas_isMoniLove():
        m 3hubsb "因为你让这里的喵妮卡非常开心~！"
        extend " 喵~！"
    else:
        m 3hubsb "因为你让这只小猫很开心~！"
    
    extend " 嘿嘿~!"
    jump to_restaurant_loop
    return

label extra_talk_annoy:
    show monika staticpose at t11
    m 1euc "你是说不好的事情吗？"
    m "额，{w=0.3} 嗯…"
    m 1hksdlb "嗯，{w=0.3} 我真的很讨厌我的钢笔在我写字的时候洒墨水！"
    m 3ftsdld "它在我的手掌上到处都是，{w=0.3} 使纸上的字迹模糊不清，"
    extend " 我很难把小手清理干净。"
    m 1lssdlc "或者当我在演奏一首钢琴曲时{i}一段{/i} 莫名的音段滑入。"
    m "让我把剩下的笔记都弄错了。"
    m 1fssdlc "啊哈哈！这些不是很令人沮丧吗？"
    m "有时候感觉这种事发生得太频繁了。"
    m 6wud "噢！{w=0.3} 但也许你指的是更严重的事？"
    m 6ruc "..."
    m 6rsd "我讨厌那些认为我不真实的人。"
    extend " 或者我是个疯子，{w=0.3} 爱肥胖的人。"
    m 6lfd "还有那些认为女孩们和我处于同一感知水平的人。"
    m "认为我没有权利去感受或做我所做的事情，因为我的处境就那样。"
    m 6dfc "..."
    extend 7mfd " 我不会进一步详细说明，"
    extend " 我 {i}不{/i} 想把这些都说出来。"
    if mas_anni.isAnni():
        m "今天不行"
    m 1dsd "{i}*叹息*{/i}"
    m 1ekblsdlb "对不起，我突然生气了[player]。"
    m 7wublsdld "我没有生你的气，{w=0.3} 我向你保证！"
    m "我只是碰到了一个敏感的话题，{w=0.3} 我想是这样的。"
    if mas_isMoniLove():
        m "相信我喔，{w=0.3} 你太可爱了，我是永远不会那样对你发火的。"
    m 6hublb "我们换个话题好好享受我们的约会吧，{w=0.3} 我们继续吧！"
    jump to_restaurant_loop
    return

label extra_talk_superhero:
    show monika staticpose at t11
    m "嗯..."
    m 1wud "可能是围绕编码的东西？"
    m 4wub "我可以成为一个令人敬畏的女英雄，通过扰乱代码来帮助党为他们开辟道路。"
    m 4eub "就像我曾经在视频游戏中看到的某个先知。"
    m 1eta "或者我可以简单地把我的敌人从我们的路上删除。"
    m 1rsc "..."
    m 1rsd "等等，{w=0.3}这会让我成为超级坏蛋吗？ " 
    extend 2wkd "噢，{w=0.3}哈哈哈…"
    m 6dksdlc "也许我更愿意坚持更友善的方式，{w=0.3}且仅删除{i}碍事的{/i}障碍。"
    if mas_isMoniLove():
        m 4tsblb "我知道我肯定已经删除了通往你心中的障碍，{w=0.3}不是吗？"
    m 1hubla "嘿嘿~"
    jump to_restaurant_loop
    return

label extra_talk_motto:
    show monika staticpose at t11
    m 1eub "有一句话我最近想了很多。"
    m 3eub "在需要的时候，我喜欢把它作为我的座右铭。"
    m 3eub "它是这样的……"
    m 1dud "“被人深爱会给你{i}力量{/i}，{w=0.3} 而深爱一个人会给你{i}勇气{/i}”！"
    m 3eub "这是“老子”的一句话，{w=0.3} 一位中国作家。这是网络链接(https://mp.weixin.qq.com/s?__biz=MjM5NDczNTk5MQ==&mid=2650667200&idx=5&sn=084431c3a46caa4dc136132746b78b19&chksm=be8a6ad889fde3ce277c1b14d9d6f65897111fe4be180bdccbea50e19491be567782f85ce114&scene=27)。"
    m 6hublb "我的力量来自你，{w=0.3} [player]!"
    m 6hublb "我的勇气是你给予我的！"
    m 6fubsb "你是我每天早上醒来和睡觉时内心平静的原因。"
    m 6fkbsb "这一切都归功于你。"
    m 6hubfb "我不知道该如何感谢你为我做了这么多。"
    m 6hubfb "你是我需要的一切。"
    jump to_restaurant_loop
    return

label extra_talk_3words:
    show monika staticpose at t11
    m 1esc "三个字的形容词？"
    m 4eub "{i}激情的{i}{w=0.5}{nw} "
    extend 4eub "{i}坚决的{i}{w=0.5}{nw} "
    extend 6eub "{i}高昂的。{i}"
    m 6esa "语言是有力量的[player]，{w=0.3} 所以如果我选择强硬的措辞来代表我自己，{w=0.3} 我想这也会让我觉得自己是个有权势的人。"
    m 1rkblsdlb "不过，如果我要用语言来描述你，我很难选择{i}仅仅只有{/i}三个字的形容词。"
    m 1gkblsdlb "毕竟，{w=0.3} 有太多的形容词让我想起了你。"
    m 6dubfb "我可爱的，{w=0.3} 钦佩的，{w=0.3} 沉稳的，{w=0.3}{nw}"
    if mas_isMoniLove():
        extend " 智慧的，{w=0.3} 有爱的，{w=0.3} 坚定的，{w=0.3} 多才的，{w=0.3}{nw}"
    extend " 以及 {i}完美的{/i} [bf]~"
    m 1gkblsdlb "看吧！{w=0.3} 我根本不可能只用一个三字形容词 来形容你！"
    m 1hublb "哈哈~!"
    m "不过，我们在一起的时间越长，这些词就会越多[player]~"
    jump to_restaurant_loop
    return

label extra_talk_pop:
    show monika staticpose at t11
    m 6wublo "噢！{w=0.3} 这真是个有趣的问题！"
    m 6rtu "也许人们会想起我的诗？"
    extend " 就像“墙上的洞”一样？"
    m 1hua "我也可以想象人们会想到我最喜欢的颜色，{w=0.3} 翡翠绿..."
    m 3wub "噢，{w=0.3} 还有“Your Reality”！{w=0.3} 也许这首歌的第一句歌词会在人们想起我的时候在他们的脑海中响起。"
    m 6hub "还有我最喜欢的粉笔！"
    m 7etb "你肯定知道的，上面是有颗心的那个标志性的~"
    if mas_isMoniLove():
        m "或者也许"
        $ mas_display_notif(m_name, ["The sound of my notifaction? Ahaha~!"], skip_checks=True)
    m 1huu "嘿嘿~{w=0.3} 想想我让人们想起了什么，这很有趣。"
    m 6fkbsa "我希望当你想起我的时候，{w=0.3} 你想到的第一件事就是我是你一生的挚爱~"
    if mas_isMoniLove():
        m "我知道当{i}我{/i}想起我亲爱的男朋友的时候，我就会想起这件事[bf]~!"
    m 6hubsb "我超爱你滴，{w=0.3} [mas_get_player_nickname()]~"
    jump to_restaurant_loop
    return
