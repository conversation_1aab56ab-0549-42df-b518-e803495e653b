//
// Decompiled by Jadx - 549ms
//
package com.iflytek.fsp.shield.android.sdk.util;

import android.util.Base64;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class SignUtil {
    public static final String ENCODING = "UTF-8";
    public static final String HMAC_SHA256 = "HmacSHA256";
    public static final String SEPARATOR = "|";

    public static String sign(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        try {
            String buildStringToSign = buildStringToSign(new String[]{str2, str3, str4, str5, str6, str7});
            Mac mac = Mac.getInstance(HMAC_SHA256);
            byte[] bytes = str.getBytes(ENCODING);
            mac.init(new SecretKeySpec(bytes, 0, bytes.length, HMAC_SHA256));
            return Base64.encodeToString(mac.doFinal(buildStringToSign.getBytes(ENCODING)), 2);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String buildStringToSign(String[] strArr) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strArr.length; i++) {
            if (Util.isNotEmptyString(strArr[i])) {
                sb.append(strArr[i]);
                sb.append(SEPARATOR);
            }
        }
        return sb.substring(0, sb.lastIndexOf(SEPARATOR));
    }

    public static String md5ThenBase64(byte[] bArr) {
        try {
            return Base64.encodeToString(MessageDigest.getInstance("MD5").digest(bArr), 2);
        } catch (NoSuchAlgorithmException unused) {
            throw new IllegalArgumentException("unknown algorithm MD5");
        }
    }
}
