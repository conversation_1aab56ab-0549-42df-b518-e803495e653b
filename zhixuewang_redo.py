# coding:utf-8
from zhixuewang.account import login_playwright
import requests
student = login_playwright("310116200805160630", "56415880wen")
token = student.get_auth_header()['XToken']
print('正在获取所有的作业...')
homeworks = student.get_homeworks(1000,True)
print('正在筛选所有的已提交打卡作业...')
clock_homeworks = []
i = 1
for homework in homeworks:
    if homework.type.code == 107:
        clock_homeworks.append(homework)
        print(f'{i}.{homework.title}')
        i += 1
print('筛选完成！')
choice = clock_homeworks[int(input('请选择要打回的打卡作业：')) - 1]
url = 'https://mhw.zhixue.com/hwreport/learning/learningRecordList'
headers = {
    "Host": "mhw.zhixue.com",
    "Connection": "keep-alive",
    "Content-Length": "355",
    "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Microsoft Edge\";v=\"120\"",
    "sucOriginAppKey": "pc_web",
    "sucAccessDeviceId": "48840E9D-D1FE-4F68-840E-58FAA62070E9",
    "sucUserToken": token,
    "sec-ch-ua-mobile": "?0",
    "Authorization": token,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Content-Type": "application/json;charset=UTF-8",
    "sucClientType": "pc-web",
    "Accept": "application/json, text/plain, */*",
    "appName": "com.iflytek.zxzy.web.zx.tea",
    "sec-ch-ua-platform": "\"Windows\"",
    "Origin": "https://www.zhixue.com",
    "Sec-Fetch-Site": "same-site",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    "Referer": "https://www.zhixue.com/middlehomework/web-teacher/views/",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "zh-CN,zh;q=0.9",
}
payload = {
    "base": {
        "appId": "OAXI57PG",
        "appVersion": "",
        "sysVersion": "v1001",
        "sysType": "web",
        "packageName": "com.iflytek.edu.hw",
        "expand": {}
    },
    "params": {
        "hwId": choice.id,
        "hwType": 107,
        "classId": choice.class_id
    }
}

response = requests.post(url, json=payload, headers=headers)
try:
    datas = response.json()['result']['learningRecordList']
except Exception as e:
    print(e)
    exit()
for studentwork in datas:
    if studentwork['studentName'] == student.name and studentwork['status'] == 2:
        stuhwid = studentwork['stuHwId']
        clockid = studentwork['clockRecordId']
        payload = {
            "base": {
                "appId": "OAXI57PG",
                "appVersion": "",
                "sysVersion": "v1001",
                "sysType": "web",
                "packageName": "com.iflytek.edu.hw",
                "expand": {}
            },
            "params": {
                "hwId": choice.id,
                "stuHwId": stuhwid,
                "clockRecordId": clockid,
                "redoReason": input('请输入打回理由：'),
                "classId": choice.class_id
            }
        }
        url = "https://mhw.zhixue.com/hw/clock/comment/redo"
        try:
            response = requests.post(url, json=payload, headers=headers)
        except Exception as e:
            print(e)
            exit()
        print('打回成功！')
input('按回车键退出...')