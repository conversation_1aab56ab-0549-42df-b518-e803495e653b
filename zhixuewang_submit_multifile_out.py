# coding:utf-8
import datetime
import json
import os
import time
import webbrowser
import requests

from jose import jwt, JWTError
from datetime import datetime
import secrets
# 获取当前目录
current_directory = os.getcwd()

# 构建文件路径
file_path = os.path.join(current_directory, "secret")
try:
    with open(file_path, "r", encoding="utf-8") as file:
        SECRET_KEY = file.read()
except IOError as e:
    print(f"读取文件时出错: {e}")
    SECRET_KEY = input("请输入SECRET_KEY：")
ALGORITHM = "HS256"


def verify_jwt(token: str):
    try:
        # 解码 JWT
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # 检查 JWT 是否过期
        exp = payload.get("exp")
        if exp is None:
            return False

        if datetime.utcfromtimestamp(exp) < datetime.utcnow():
            return False
        return True

    except JWTError:
        return False
result = verify_jwt(requests.get("https://api.haorwen.top/file").json()['token'])
if result:
    print("JWT 验证成功，进入程序...")
    # 获取当前目录
    current_directory = os.getcwd()

    # 构建文件路径
    file_path = os.path.join(current_directory, "secret")

    # 写入文件
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(SECRET_KEY)
        print("密钥已自动保存，请勿删除secret文件，若更换新设备需重新输入")
    except IOError as e:
        print(f"写入文件时出错: {e}")
else:
    input("JWT 验证失败或已过期...")
    exit()


try:
    from zhixuewang.account import login_cookie

    print("请先登录并用脚本获取cookie")
    webbrowser.open('https://www.zhixue.com/wap_login.html')

    # 复制的cookie字符串
    cookie_string = input("请输入登陆后获取到的cookie：")

    # 将cookie字符串转换为字典
    cookies = dict(item.split("=") for item in cookie_string.split("; "))
    student = login_cookie(cookies)
    # student = login_student(input("请输入智学网用户名："), input("请输入密码："))
    print('正在获取所有的作业...')
    homeworks = student.get_homeworks(200)
    # print(homeworks)
    print('正在筛选所有的打卡作业...')
    clock_homeworks = []
    i = 1
    for homework in homeworks:
        if homework.type.code == 107:
            clock_homeworks.append(homework)
            print(f'{i}.{homework.title}')
            i += 1
    print('筛选完成！')
    choice = clock_homeworks[int(input('请选择要提交的打卡作业：')) - 1]
    print(choice)
    print('请先运行fileuploader.exe生成filedata.json，exe请找haorwen获取')
    input('生成完成后按回车键以继续...')
    f = open('filedata.json', 'r')
    content = f.read()
    attachmentDTOs = json.loads(content)
    f.close()
    session = student._session
    token = student.get_auth_header()['XToken']
    confirm = input('是否确认提交？（y/n）')
    if confirm != 'y':
        input('已取消提交，按回车键退出')
        exit()
    print('正在提交...')
    r = session.post(
        "https://mhw.zhixue.com/hw/clock/answer/submit",
        json={
            "base": {
                "Authorization": token,
                "appId": "OAXI57PG",
                "appVersion": "2.0.1906",
                "packageName": "com.iflytek.elpmobile.student",
                "sucAccessDeviceId": "",
                "sucClientType": "android",
                "sucOriginAppKey": "",
                "sucUserToken": token,
                "sysType": "Android",
                "sysVersion": "22021211RC",
                "udid": "",
                "userId": student.id,
                "utag": ""
            },
            "params": {
                "attachmentDTOs": attachmentDTOs,
                "type": 3,
                "hwId": choice.id,
                "stuHwId": choice.stu_hwid
            }
        },
        headers={
            "Authorization": token,
            "sucAccessDeviceId": "",
            "sucUserToken": token,
            "Referer": "https://mhw.zhixue.com/zhixuestudent/views/learningTask/topic.html"
        },
    )
    data = r.json()
    print(data['code'])
    if data['code'] == '900003':
        print('检测到为第一次提交，正在重新提交...')
        r = session.post(
            'https://mhw.zhixue.com/hw/clock/answer/submit',
            json={
                "base": {
                    "Authorization": token,
                    "appId": "OAXI57PG",
                    "appVersion": "2.0.1906",
                    "packageName": "com.iflytek.elpmobile.student",
                    "sucAccessDeviceId": "",
                    "sucClientType": "android",
                    "sucOriginAppKey": "",
                    "sucUserToken": token,
                    "sysType": "Android",
                    "sysVersion": "22021211RC",
                    "udid": "",
                    "userId": student.id,
                    "utag": ""
                },
                "params": {
                    "attachmentDTOs": attachmentDTOs,
                    "type": 2,
                    "hwId": choice.id,
                    "stuHwId": choice.stu_hwid
                }
            },
            headers={
                "Authorization": token,
                "sucAccessDeviceId": "",
                "sucUserToken": token,
                "Referer": "https://mhw.zhixue.com/zhixuestudent/views/learningTask/topic.html"
            },
        )
        data = r.json()
        print(data['code'])
    if data['code'] == "000000":
        print('提交成功！')
        url = 'https://mhw.zhixue.com/hwreport/learning/learningRecordList'
        time.sleep(10)
        now = datetime.datetime.now()
        headers = {
            "Host": "mhw.zhixue.com",
            "Connection": "keep-alive",
            "Content-Length": "355",
            "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Microsoft Edge\";v=\"120\"",
            "sucOriginAppKey": "pc_web",
            "sucAccessDeviceId": "",
            "sucUserToken": token,
            "sec-ch-ua-mobile": "?0",
            "Authorization": token,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Content-Type": "application/json;charset=UTF-8",
            "sucClientType": "pc-web",
            "Accept": "application/json, text/plain, */*",
            "appName": "com.iflytek.zxzy.web.zx.tea",
            "sec-ch-ua-platform": "\"Windows\"",
            "Origin": "https://www.zhixue.com",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.zhixue.com/middlehomework/web-teacher/views/",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9",
        }
        payload = {
            "base": {
                "appId": "OAXI57PG",
                "appVersion": "",
                "sysVersion": "v1001",
                "sysType": "web",
                "packageName": "com.iflytek.edu.hw",
                "expand": {}
            },
            "params": {
                "hwId": choice.id,
                "hwType": 107,
                "classId": choice.class_id
            }
        }

        response = requests.post(url, json=payload, headers=headers)
        try:
            datas = response.json()['result']['learningRecordList']
        except Exception as e:
            print(e)
            exit()
        for studentwork in datas:
            if studentwork['studentName'] == student.name and studentwork['status'] == 2:
                stuhwid = studentwork['stuHwId']
                clockid = studentwork['clockRecordId']
                payload = {
                    "base": {
                        "appId": "OAXI57PG",
                        "appVersion": "",
                        "sysVersion": "v1001",
                        "sysType": "web",
                        "packageName": "com.iflytek.edu.hw",
                        "expand": {}
                    },
                    "params": {
                        "hwId": choice.id,
                        "stuHwId": stuhwid,
                        "clockRecordId": clockid,
                        "redoReason": f"{now.hour}:{now.minute} 打回成功",
                        "classId": choice.class_id
                    }
                }
                url = "https://mhw.zhixue.com/hw/clock/comment/redo"
                try:
                    response = requests.post(url, json=payload, headers=headers)
                except Exception as e:
                    print(e)
                    exit()
                print('打回成功！重装应用后即可获取到打回的文件！')
    else:
        print('提交失败！')
except Exception as e:
    print('error:',e)
    input('按回车键退出...')
input('按回车键退出...')