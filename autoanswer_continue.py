import base64
import datetime
import json
import re
import time
import traceback
from io import BytesIO

import matplotlib.pyplot as plt
import numpy as np
import oss2
import requests
from PIL import Image
from bs4 import BeautifulSoup
from zhixuewang import login_student
import logging
logging.basicConfig(format='%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s',
                    level=logging.INFO,
                    filename='ata.log',
                    filemode='a')


def replace_latex(s):
    # 首先将","替换为","
    s = re.sub('，', ',', s)
    s = re.sub('（', '(', s)
    s = re.sub('）', ')', s)
    # 然后将"\le"替换为"\leq"
    s = re.sub(r'\\le(?!ft)', r'\\leq', s)
    # 最后将"\ge"替换为"\geq"
    s = re.sub(r'\\ge', r'\\geq', s)

    return s


def formula2img(str_latex, font_size=16):
    fig = plt.figure()
    ax = fig.add_axes((0, 0, 1, 1))
    ax.get_xaxis().set_visible(False)
    ax.get_yaxis().set_visible(False)
    ax.set_xticks([])
    ax.set_yticks([])
    plt.text(
        0.5,
        0.5,
        str_latex,
        fontsize=font_size,
        verticalalignment='center',
        horizontalalignment='center')
    save_file = BytesIO()
    save_file2 = BytesIO()
    plt.axis('off')
    plt.savefig(save_file, format='png', bbox_inches='tight', pad_inches=0.02)
    image = Image.open(save_file)  # 打开tiff图像
    ImageArray = np.array(image)
    row = ImageArray.shape[0]
    col = ImageArray.shape[1]
    # 先计算所有图片的裁剪范围，然后再统一裁剪并输出图片
    x_left = row
    x_top = col
    x_right = 0
    x_bottom = 0
    for r in range(row):
        for c in range(col):
            # if ImageArray[row][col][0] < 255 or ImageArray[row][col][0] ==0:
            if ImageArray[r][c][0] < 255 and ImageArray[r][c][0] != 0:  # 外框有个黑色边框，增加条件判断
                if x_top > r:
                    x_top = r  # 获取最小x_top
                if x_bottom < r:
                    x_bottom = r  # 获取最大x_bottom
                if x_left > c:
                    x_left = c  # 获取最小x_left
                if x_right < c:
                    x_right = c  # 获取最大x_right
    # (left, upper, right, lower)
    cropped = image.crop((x_left - 5, x_top - 5, x_right + 5, x_bottom + 5))
    cropped.save(save_file2, format='png')
    # 转换base64并以utf8格式输出
    save_file_base64 = base64.b64encode(save_file2.getvalue()).decode('utf8')
    return save_file_base64


def is_latex(s):
    if '\\' in s or '^' in s:
        return True
    else:
        return False


student = login_student('310116200805160630', '56415880wen')
old_homeworks = []
cishu = 1
while True:
    try:
        logging.info('正在获取所有的作业...')
        homeworks = student.get_homeworks(200)
        # print(homeworks)
        logging.info('正在筛选所有的非打卡作业...')
        common_homeworks = []
        i = 1
        for homework in homeworks:
            if homework.type.code != 107:
                common_homeworks.append(homework)
                # print(f'{i}.{homework.title}')
                i += 1
        # print('筛选完成！')
        if old_homeworks != common_homeworks or cishu == 10:
            out_answers = '''<html>\n<head>\n<meta charset="utf-8" />\n<title>Homework Answers</title>\n<style>
        .progress-bar {
            width: 20%;
            background-color: #ccc9bd;
            border-radius: 5px;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }

        .progress {
            height: 10px;
            background-color: #306998;
            border-radius: 5px;
            width: 0%;
            transition: width 1s;
        }
        #toggle-text1 {
 display: none;
}
#toggle-button1 {
 position: absolute;
 width: 100px;
 height: 40px;
 background-color: lightblue;
 line-height: 40px;
 text-align: center;
 cursor: pointer;
}}
#toggle1 {
 display: none;
}
#toggle1:checked ~ #toggle-text1 {
 display: block;
}
    </style></head>\n<body>
    <input id="toggle1" type="checkbox" />
<label id="toggle-button1" for="toggle1">查看详情</label>'''
            token = student.get_auth_header()['XToken']
            for work in common_homeworks[:6]:
                time.sleep(1)
                answer_percent = []
                score_rates = []
                answer_percent_i = 0
                try:
                    answers = student.get_homework_answer(work)
                    url = "https://mhw.zhixue.com/hwreport/class/classView"
                    headers = {
                        "accept": "application/json, text/plain, */*",
                        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                        "appname": "com.iflytek.zxzy.web.zx.tea",
                        "authorization": token,
                        "content-type": "application/json;charset=UTF-8",
                        "sec-ch-ua": "\"Microsoft Edge\";v=\"119\", \"Chromium\";v=\"119\", \"Not?A_Brand\";v=\"24\"",
                        "sec-ch-ua-mobile": "?0",
                        "sec-ch-ua-platform": "\"Windows\"",
                        "sec-fetch-dest": "empty",
                        "sec-fetch-mode": "cors",
                        "sec-fetch-site": "same-site",
                        "sucaccessdeviceid": "[object Object]",
                        "succlienttype": "pc-web",
                        "sucoriginappkey": "pc_web",
                        "sucusertoken": token,
                        "Referer": "https://www.zhixue.com/middlehomework/web-teacher/views/",
                        "Referrer-Policy": "unsafe-url"}

                    payload = {
                        "base": {
                            "appId": "OAXI57PG",
                            "appVersion": "",
                            "sysVersion": "v1001",
                            "sysType": "web",
                            "packageName": "com.iflytek.edu.hw",
                            "expand": {}
                        },
                        "params": {
                            "hwId": work.id,
                            "classId": "1500000100217094151",
                            "hwType": work.type.code
                        }
                    }
                    data = requests.post(
                        url, json=payload, headers=headers).json()['result']
                    avg_score = data['avgScore']
                    max_score = data['maxScore']
                    submit_people = data['submitCount']
                    url = "https://mhw.zhixue.com/hwreport/question/teachModel"
                    data = requests.post(
                        url, json=payload, headers=headers).json()['result']
                    for question in data['teachModelQuestionList']:
                        answer_results = question["answerDetail"][0]['answerResult']
                        answer_percents = ''
                        try:
                            score_rate = question["answerDetail"][0]['scoreRate']
                        except BaseException:
                            score_rate = '获取失败'
                        score_rates.append(str(score_rate) + '%')
                        for answer_result in answer_results:
                            if answer_result['answer'] == '正确' or answer_result['isCorrect']:
                                color = 'background-color:#00f91a;'
                            else:
                                color = 'background-color:#ff2700;'
                            answer_percents += f'<div id="toggle-text1">{answer_result["answer"]}<div class="progress-bar"><div class="progress" style="width: {round(answer_result["count"] / submit_people * 100, 1)}%;{color}"></div></div> {round(answer_result["count"] / submit_people * 100, 1)}%<br /></div>'
                        answer_percent.append(answer_percents)
                        answer_percent_i += 1
                except Exception as e:
                    logging.error(traceback.format_exc())
                    logging.warning('获取答案失败，正在启用基本答案')
                    time.sleep(1)
                    answers = student.get_homework_answer(work)
                    max_score = avg_score = '获取失败'
                finally:
                    pass
                out_answers += f'<h2>{work.title} 平均分：{avg_score} 最高分:{max_score}</h2>'
                i = 0
                for answer in answers:
                    if is_latex(answer.content):
                        try:
                            answer.content = replace_latex(answer.content)
                            # 这个正则表达式匹配任何一个或多个连续的非中文字符
                            non_chinese_regex = re.compile(r"[^\u4e00-\u9fff]+")
                            # 分割成一个由中文和非中文部分交替组成的列表
                            parts = non_chinese_regex.split(answer.content)
                            # 使用同样的正则表达式找到非中文部分
                            non_chinese_parts = non_chinese_regex.findall(
                                answer.content)
                            # 对每个非中文部分进行处理
                            processed_non_chinese_parts = [
                                f'<img src="data:image/png;base64,{formula2img("$" + part + "$")}">' for part in
                                non_chinese_parts]
                            # 构造一个用于重组的迭代器，它会先从中文部分取一个，然后从处理过的非中文部分取一个，然后再从中文部分取一个，依此类推
                            recombined_parts = zip(
                                parts, processed_non_chinese_parts + [""])
                            # 重组成新的字符串
                            answer.content = "".join(
                                part for pair in recombined_parts for part in pair)
                        except BaseException:
                            pass
                    if answer.content == '':
                        answer.content = '暂无答案'
                        out_answers += f'<h3>{answer.title}：{answer.content}</h3>'
                    elif answer_percent_i == len(answers):
                        out_answers += f'<h3>{answer.title}：{answer.content} 正确率：{score_rates[i]}</h3> {answer_percent[i]}'
                    else:
                        out_answers += f'<h3>{answer.title}：{answer.content}</h3>'
                    i += 1
                out_answers += '<hr>\n'
            out_answers += '</body>\n</html>'
            soup = BeautifulSoup(out_answers, 'html.parser')
            with open('out_answer.docx', 'w', encoding='utf-8') as f:
                f.write(soup.prettify())


            def upload_file_to_zhixue(
                    student_id,
                    token,
                    local_file_path,
                    filename,
                    filepath):

                today = datetime.date.today()
                today = today.strftime("%Y/%m/%d/")
                # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
                data = {
                    "stsTokenQueryList": [
                        {
                            "appKey": "XXX_ANDROID_ZXZY_STU",
                            "chunks": 1,
                            "fileName": filename,
                            "productKey": ""
                        }
                    ]
                }

                headers = {
                    'clientType': 'android',
                    'epasAppId': 'zhixue_parent',
                    'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
                    'token': token,
                    'Content-Type': 'application/json; charset=utf-8',
                    'Content-Length': '163',
                    'Host': 'aidp.changyan.com',
                    'Connection': 'Keep-Alive',
                    'Accept-Encoding': 'gzip',
                    'User-Agent': 'okhttp/3.12.12'}

                response = requests.post(
                    'https://aidp.changyan.com/open-svc/file/listStsTokenV2',
                    headers=headers,
                    data=json.dumps(data))
                ossinfo = response.json()

                # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
                sts_access_key_id = ossinfo['data'][0]['accessId']
                sts_access_key_secret = ossinfo['data'][0]['accessSecret']
                # 填写从STS服务获取的安全令牌（SecurityToken）。
                security_token = ossinfo['data'][0]['securityToken']
                # 使用临时访问凭证中的认证信息初始化StsAuth实例。
                auth = oss2.StsAuth(sts_access_key_id,
                                    sts_access_key_secret,
                                    security_token)
                # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
                # 填写Bucket名称。
                bucket = oss2.Bucket(
                    auth,
                    'https://oss-cn-hangzhou.aliyuncs.com',
                    'zhixue-ugc')

                # 上传文件。
                # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
                # headers = dict()
                # headers["x-oss-storage-class"] = "Standard"
                # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
                # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
                # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
                result = bucket.put_object_from_file(filepath, local_file_path)

                return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"


            old_homeworks = common_homeworks
            logging.info('正在上传...')
            file_url = upload_file_to_zhixue(
                student.id,
                student.get_auth_header()['XToken'],
                'out_answer.docx',
                'EDU_1500000100217351485_bfbe47b1-82e7-4490-839b-a7f096d138cb.docx',
                'middleHomework/android/zxzy/2023/12/09/EDU_1500000100217351485_bfbe47b1-82e7-4490-839b-a7f096d138cb.docx')
            # file_url = 'https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2023/11/29/EDU_1500000100217351485_bc4ea515-b8b4-4cb7-b47d-17a9c1b99423.docx'
            logging.debug(file_url)
            logging.info('文件修改完成！')
            logging.info(datetime.datetime.now(), '已更新答案')
            cishu = 1
            time.sleep(60)
        else:
            logging.info(datetime.datetime.now(), '没有新作业')
            cishu += 1
            time.sleep(60)
    except Exception as e:
        logging.error('出现错误:', e, '60秒后重试...')
        old_homeworks = []
        time.sleep(60)
    finally:
        pass
