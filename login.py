# coding:utf-8
import base64
import hashlib
import json
import time
import uuid
import requests

username = "sfzzhu<PERSON>"
password = "zhuhui606917"
if len(password) != 32:
    password = (
        pow(
            int.from_bytes(password.encode()[::-1], "big"),
            65537,
            186198350384465244738867467156319743461,
        )
        .to_bytes(16, "big")
        .hex()
    )  # by immoses648
print(password)
session = requests.Session()
r = session.get("https://sso.zhixue.com/sso_alpha/login?service=https://www.zhixue.com:443/ssoservice.jsp")

json_obj = json.loads(r.text.strip().replace("\\", "").replace("'", "")[1:-1])
print(json_obj)
lt = json_obj["data"]["lt"]
execution = json_obj["data"]["execution"]
r = session.get(
    "https://sso.zhixue.com/sso_alpha/login?service=https://www.zhixue.com:443/ssoservice.jsp",
    params={
        "encode": "true",
        "sourceappname": "tkyh,tkyh",
        "_eventId": "submit",
        "appid": "zx-container-client",
        "client": "web",
        "type": "loginByNormal",
        "key": "auto",
        "lt": lt,
        "execution": execution,
        "customLogoutUrl": "https://www.zhixue.com/login.html",
        "username": username,
        "password": password,
    },
)

json_obj = json.loads(r.text.strip().replace("\\", "").replace("'", "")[1:-1])
print(json_obj)
ticket = json_obj["data"]["st"]
session.post(
    "https://www.zhixue.com:443/ssoservice.jsp",
    data={
        "action": "login",
        "ticket": ticket,
    },
)
auth_guid = str(uuid.uuid4())
auth_time_stamp = str(int(time.time() * 1000))
md5 = hashlib.md5()
md5.update((auth_guid + auth_time_stamp + "iflytek!@#123student").encode(encoding="utf-8"))
auth_token = md5.hexdigest()
token, cur_time = "", 0.0
r = session.get(
    "https://www.zhixue.com/addon/error/book/index",
    headers={
        "authbizcode": "0001",
        "authguid": auth_guid,
        "authtimestamp": auth_time_stamp,
        "authtoken": auth_token,
    },
)
print(r.json()["result"])

