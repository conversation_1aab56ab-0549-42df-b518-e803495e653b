import base64
import webbrowser

import requests
import json
import zhixuewang
import subprocess
import time
import psutil

def is_process_running(process_name):
    """检查是否有正在运行的进程包含给定的名称。"""
    for proc in psutil.process_iter(['name']):
        try:
            if process_name.lower() in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False

def start_executable():
    process_name = 'geetest_api_packed.exe'

    if not is_process_running(process_name):
        try:
            # 使用 cmdstart 命令启动可执行文件
            command = f'start {process_name}'
            subprocess.Popen(command, shell=True)
            print("等待验证码API启动...")
            time.sleep(3)
            print("可执行文件启动成功。")
        except Exception as e:
            print(f"发生错误：{e}")
    else:
        print(f"{process_name} 已在运行。")

start_executable()




class ZhiXueException(Exception):
    pass


class ZhiXue:
    def __init__(self, user_account: str, password: str):
        self.user_account = user_account
        self.password = password
        self.user_id = None

    def login_student(self) -> requests.Session:
        session = zhixuewang.session.get_basic_session()

        try:
            captcha_data, captcha_id = self._login_step_0(session)
            lt, execution = self._login_step_1(session)
            st = self._login_step_2(session, lt, execution, captcha_data, captcha_id)
            self._login_step_3(session, st)
            print("登录成功.")
            return session
        except ZhiXueException as e:
            print(f"登录失败: {e}")

    def _login_step_0(self, session: requests.Session) -> tuple:
        print("    [-1/6] 初始化登录.")
        for i in range(3):
            try:
                captcha_data = requests.get("http://127.0.0.1:8080/get_geetest", timeout=4).json()["data"]
                if captcha_data["result"] != "success":
                    raise ZhiXueException("登录智学网异常: 验证码获取失败.")
                break
            except Exception as exc:
                print(exc)
        else:
            raise ZhiXueException("登录智学网异常: 验证码获取失败.")

        url = "https://www.zhixue.com/edition/login?from=web_login"
        data = {
            "appId": "zx-container-client",
            "captchaType": "third",
            "thirdCaptchaExtInfo[lot_number]": captcha_data["seccode"]["lot_number"],
            "thirdCaptchaExtInfo[pass_token]": captcha_data["seccode"]["pass_token"],
            "thirdCaptchaExtInfo[gen_time]": captcha_data["seccode"]["gen_time"],
            "thirdCaptchaExtInfo[captcha_output]": captcha_data["seccode"]["captcha_output"],
            "loginName": self.user_account,
            "password": self.password,
        }
        result = session.post(url, params={"from": "web_login"}, data=data).json()
        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['message']}")

        self.user_id = result["data"]["userId"]
        return (captcha_data, result["data"]["captchaId"])

    def _login_step_1(self, session: requests.Session) -> tuple:
        print("    [0/6] 发送登录请求.")
        if "&" in self.password:
            raise ZhiXueException("登录智学网异常: 不支持登录密码包含 & 的账号.")

        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {"service": "https://www.zhixue.com:443/ssoservice.jsp"}
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" in result["data"]:
            raise ZhiXueException("登录智学网异常: 此会话已登录.")

        lt = result["data"]["lt"]
        execution = result["data"]["execution"]
        return (lt, execution)

    def _login_step_2(self, session: requests.Session, lt: str, execution: str, captcha_data: dict, captcha_id: str) -> str:
        print("    [1/6] 发送账号密码.")
        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {
            "service": "https://www.zhixue.com:443/ssoservice.jsp",
            "captchaId": captcha_id,
            "captchaType": "third",
            "thirdCaptchaParam": captcha_data["seccode"],
            "version": "v3",
            "_eventId": "submit",
            "key": "auto",
            "lt": lt,
            "execution": execution,
            "username": self.user_account,
            "password": self.password,
        }
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" not in result["data"]:
            raise ZhiXueException("登录智学网异常: st 未找到.")

        st = result["data"]["st"]
        return st

    def _login_step_3(self, session: requests.Session, st: str) -> None:
        print("    [2/6] 发送登录凭证.")
        url = "https://www.zhixue.com/ssoservice.jsp"
        data = {"ticket": st}
        result = session.post(url, params=data).text
        result = result.split("\n", 1)[0]

        if "<!DOCTYPE HTML" in result:
            raise ZhiXueException("登录智学网异常: 服务器 IP 被智学网封禁.")

        if result != "success":
            raise ZhiXueException(f"登录智学网异常: {result}")

        session.cookies.set("uname", base64.b64encode(self.user_account.encode()).decode())

# 使用示例
zhi_xue = ZhiXue(user_account="310116200805160630", password="56415880wen")
session = zhi_xue.login_student()
student = zhixuewang.account.StudentAccount(session).set_base_info()
print(student.role)
print('正在获取所有的作业...')
homeworks = student.get_homeworks(200)
# print(homeworks)
print('正在筛选所有的非打卡作业...')
clock_homeworks = []
i = 1
for homework in homeworks:
    if homework.type.code != 107:
        clock_homeworks.append(homework)
        print(f'{i}.{homework.title}')
        i += 1
print('筛选完成！')
while True:
    choice = clock_homeworks[int(input('请选择要查询答案的作业：')) - 1]
    answers = student.get_exercise_answer(choice)
    for answer in answers:
        if answer.content == '':
            answer.content = '暂无答案'
        print(answer.title, '：', answer.content)
    input('按回车键以继续...')
    i = 1
    for homework in clock_homeworks:
        print(f'{i}.{homework.title}')
        i += 1
