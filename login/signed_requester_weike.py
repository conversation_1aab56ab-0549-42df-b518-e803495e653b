import requests, hashlib, hmac, random, base64, time, uuid
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
from urllib.parse import urlparse
from rich import print

class IFLYTEKSignedRequest(requests.Session):
    def __init__(self, public_key, secret_key, app_id):
        super().__init__()
        self.public_key = public_key
        self.secret_key = secret_key
        self.app_id = app_id
        self.headers.update({'S-Auth-AppId': app_id})
        self.headers.update({'S-Auth-ClientType': 'pc-web'})
        self.headers.update({'S-Auth-Version': '1'})
        self.headers.update({'S-Auth-Stage': 'RELEASE'})

    def sign_random_key(self) -> str:
        str2 = random.randint(100000, 1000000)
        str2 = str(str2)
        # 将十六进制字符串转换为字节
        public_key_bytes = base64.b64decode(self.public_key)
        # 从字节中恢复公钥
        rsa_key = RSA.import_key(public_key_bytes)
        # 创建一个使用PKCS1_OAEP填充的加密器
        cipher = PKCS1_OAEP.new(rsa_key)
        # 加密数据
        encrypted_data = cipher.encrypt(str2.encode('utf-8'))
        # 将加密后的数据转换为Base64编码的字符串
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def sign(self, url:str, random_key:str, nonce:str, timestamp:str, content_md5:str) -> str:
        # secret, path, random_key, auth_nonce, timestamp, app_id, content_md5
        # 处理path
        parsed_url = urlparse(url)
        path = parsed_url.path
        if parsed_url.query:
            path += '?' + parsed_url.query
        # 生成签名，n按照此顺序拼接
        string_to_sign = [path, random_key, nonce, timestamp, self.app_id, content_md5]
        # 删除空值
        string_to_sign = [s for s in string_to_sign if s]
        # 拼接字符串
        string_to_sign = '|'.join(string_to_sign)
        # 使用HMAC-SHA256算法生成签名
        signature = hmac.new(self.secret_key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha256).digest()
        # 将签名转换为Base64编码的字符串
        signature = base64.b64encode(signature).decode('utf-8')
        return signature
    def request(self, method: str, url: str, _options: dict = {}, **kwargs):
        options = _options.copy()
        options['auth_type'] = options.get('auth_type', 0)  # 0:不包含md5和随机字符串，1:包含md5和随机字符串
        options['auth_nonce'] = options.get('auth_nonce', str(uuid.uuid4()))
        options['timestamp'] = options.get('timestamp', str(round(time.time() * 1000)))
        if options['auth_type'] == 1:
            options['content_md5'] = options.get('content_md5', hashlib.md5(kwargs['data'].encode('utf-8')).hexdigest())
            options['random_key'] = options.get('random_key', self.sign_random_key())
            options['signature'] = options.get('signature', self.sign(url, options['random_key'], options['auth_nonce'], options['timestamp'], options['content_md5']))
        else:
            options['signature'] = options.get('signature', self.sign(url, '', options['auth_nonce'], options['timestamp'], ''))
        self.headers.update({'S-Auth-Nonce': options['auth_nonce']})
        self.headers.update({'S-Auth-Timestamp': options['timestamp']})
        self.headers.update({'S-Auth-Signature': options['signature']})
        print('请求参数:', options)
        return super().request(method, url, **kwargs)
    def get(self, url, options: dict = {}, **kwargs):
        return self.request('GET', url, options, **kwargs)
    def post(self, url, options: dict = {}, **kwargs):
        return self.request('POST', url, options, **kwargs)
    def put(self, url, options: dict = {}, **kwargs):
        return self.request('PUT', url, options, **kwargs)
    def delete(self, url, options: dict = {}, **kwargs):
        return self.request('DELETE', url, options, **kwargs)

if __name__ == '__main__':
    public_key = "305C300D06092A864886F70D0101010500034B003048024100C90131F09E314134F2F6A4DEF9FE7BA1509C9B8E3463D6C3E462579275899CAA31943608214AD3B2760E6A5B7772BECA7E1DF2B1DBBACEA85FB9B84750D7CE870203010001"
    sign = IFLYTEKSignedRequest(public_key, "87116065e97dae5d", "zhwk-web")

    # 设置请求的 URL
    url = "https://api.changyan.com/zhwk-service/commonRest/listStudentClass?userId=1500000100217351485&schoolId=2300000001000094443"

    # 设置请求头
    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
        "Host": "api.changyan.com",
        "Origin": "https://cdnzhwk.changyan.com",
        "Pragma": "no-cache",
        "Referer": "https://cdnzhwk.changyan.com/",
        "S-Auth-AppId": "zhwk-web",
        "S-Auth-ClientType": "web",
        "S-Auth-DeviceId": "CA9A70E3-D341-4977-A98A-09804B252224",
        "S-Auth-Stage": "RELEASE",
        "S-Auth-Token": "53268041-054d-4352-832d-e1604b9128f1",
        "S-Auth-Version": "1",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "sec-ch-ua": '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
    }


    # 发送 GET 请求

    response = sign.get(url, headers=headers)

    # 打印返回内容
    print(response.text)
