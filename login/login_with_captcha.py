import base64
import hashlib
import re
import time
import uuid
import requests
import json
import binascii
import zhi<PERSON><PERSON><PERSON>

def re_get_json(text):
    pattern = r'\((.*)\)'
    match = re.search(pattern, text)

    if match:
        json_str = match.group(1)
        return json.loads(json_str)
    else:
        print("未找到 JSON 数据")
        return {}

def rc4(key, data):
    S = list(range(256))
    j = 0

    for i in range(256):
        j = (j + S[i] + ord(key[i % len(key)])) % 256
        S[i], S[j] = S[j], S[i]

    i = 0
    j = 0
    output = []

    for char in data:
        i = (i + 1) % 256
        j = (j + S[i]) % 256
        S[i], S[j] = S[j], S[i]
        k = S[(S[i] + S[j]) % 256]
        output.append(chr(ord(char) ^ k))

    return ''.join(output)


def to_hex_string(data):
    return ''.join(format(ord(c), '02x') for c in data)

def get_encrypt_password(k):
    return requests.get(f"http://localhost:5000/encrypt?k={k}").json()["result"]

def get_captcha_output():
    # Implementation should go here
    data = requests.get('http://localhost:8080/get_geetest').json()
    return data


def login(username, password):
    session = requests.Session()  # Create a session object

    encrypted_password = to_hex_string(rc4('iflytzhixueweb', password))
    # print(encrypted_password)

    login_url = "https://www.zhixue.com/edition/login?from=web_login"
    captcha_result = get_captcha_output()['data']

    params = {
        "loginName": username,
        "description": "encrypt",
        "password": encrypted_password,
        "appId": "zx-container-client",
        "captchaType": "third",
        "deviceName": "web",
        "client": "web",
        "thirdCaptchaExtInfo[captcha_id]": captcha_result['seccode']['captcha_id'],
        "thirdCaptchaExtInfo[lot_number]": captcha_result['lot_number'],
        "thirdCaptchaExtInfo[pass_token]": captcha_result['seccode']['pass_token'],
        "thirdCaptchaExtInfo[gen_time]": int(time.time()),
        "thirdCaptchaExtInfo[captcha_output]": captcha_result['seccode']['captcha_output']
    }

    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Origin': 'https://www.zhixue.com',
        'Referer': 'https://www.zhixue.com/login.html',
        'X-Requested-With': 'XMLHttpRequest',
        'Connection': 'keep-alive',
    }

    try:
        session.get("https://www.zhixue.com/login.html")
        response = session.post(login_url, data=params, headers=headers)  # Use session to post
        result_info = response.json()

        if result_info.get("result") == "success":
            user_id = result_info['data']['userId']
            captchaId = result_info['data']['captchaId']
            print(f"登录成功，用户ID: {user_id}")
            response = session.get(f"https://open.changyan.com/sso/login?sso_from=zhixuesso&service=https://www.zhixue.com:443/ssoservice.jsp&callback=jQuery&_={int(time.time() * 1000)}")
            # print(response.text.replace('\n', '').replace(' ', '').replace('\\', ''))
            lt = re_get_json(response.text.replace('\n', '').replace(' ', '').replace('\\', '').replace("'", ''))['data']['lt']
            k = f"LT/{lt}/{password}"
            en = get_encrypt_password(k)
            url = "https://open.changyan.com/sso/login"
            captcha_info = '{' + f'"captcha_id":"{captcha_result["seccode"]["captcha_id"]}","lot_number":"{captcha_result["lot_number"]}","pass_token":"{captcha_result["seccode"]["pass_token"]}","gen_time":{int(time.time() * 1000)},"captcha_output":"{captcha_result["seccode"]["captcha_output"]}"' + '}'
            params = {
                'sso_from': 'zhixuesso',
                'service': 'https://www.zhixue.com:443/ssoservice.jsp',
                'callback': 'jQuery',
                'captchaId': captchaId,
                'captchaType': 'third',
                'thirdCaptchaParam': captcha_info,
                'version': 'v2',
                'encode': 'true',
                'sourceappname': 'tkyh,tkyh',
                '_eventId': 'submit',
                'appId': 'zx-container-client',
                'client': 'web',
                'type': 'loginByNormal',
                'key': 'auto',
                'lt': lt,
                'execution': 'e1s1',
                'customLogoutUrl': '//www.zhixue.com',
                'ncetAppId': 'QLIqXrxyxFsURfFhp4Hmeyh09v6aYTq1',
                'sysCode': '',
                'behaviorCheckInPc': 'true',
                'needBehaviorCheck': 'true',
                'customConfig': '{"redirectUrl":"https://www.zhixue.com","needSsoLogin":"false"}',
                'username': username,
                'encodeType': 'R2/P/LT',
                'password': en,
                'useAreaExamNo': 'true',
                '_': {int(time.time() * 1000)}
            }

            response = session.get(url, params=params)
            json_obj = re_get_json(response.text.replace('\n', '').replace(' ', '').replace('\\', '').replace("'", ''))
            print(json_obj)
            ticket = json_obj["data"]["st"]
            session.post(
                "https://www.zhixue.com:443/ssoservice.jsp",
                data={
                    "action": "login",
                    "ticket": ticket,
                },
            )
            auth_guid = str(uuid.uuid4())
            auth_time_stamp = str(int(time.time() * 1000))
            md5 = hashlib.md5()
            md5.update((auth_guid + auth_time_stamp + "iflytek!@#123student").encode(encoding="utf-8"))
            auth_token = md5.hexdigest()
            token, cur_time = "", 0.0
            r = session.get(
                "https://www.zhixue.com/addon/error/book/index",
                headers={
                    "authbizcode": "0001",
                    "authguid": auth_guid,
                    "authtimestamp": auth_time_stamp,
                    "authtoken": auth_token,
                },
            )
            print(r.json()["result"])
            session.cookies.set("uname", base64.b64encode(username.encode()).decode())
            return session
        else:
            print(f"登录失败: {result_info.get('message', '未知错误')}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


# 示例调用

session = login("310116200805160630", "56415880wen")
stu = zhixuewang.account.StudentAccount(session).set_base_info()
print(stu.role)
stu.get_homeworks()