from quart import Quart, request, jsonify
from playwright.async_api import async_playwright
import asyncio
import os

app = Quart(__name__)

# 假设zhixue.js在当前目录下
SCRIPT_PATH = os.path.join(os.path.dirname(__file__), 'security.js')

# 全局变量来存储playwright和browser实例
playwright = None
browser = None
context = None

async def initialize_browser():
    global playwright, browser, context
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch()
    context = await browser.new_context()

async def create_page():
    global context
    page = await context.new_page()
    await page.goto("https://www.zhixue.com/wap_login.html")
    await page.wait_for_load_state('networkidle')
    
    # 设置h变量
    await page.evaluate("""() => {
        window.h = RSAUtils.getKeyPair(
            '010001',
            '',
            '00ccd806a03c7391ee8f884f5902102d95f6d534d597ac42219dd8a79b1465e186c0162a6771b55e7be7422c4af494ba0112ede4eb00fc751723f2c235ca419876e7103ea904c29522b72d754f66ff1958098396f17c6cd2c9446e8c2bb5f4000a9c1c6577236a57e270bef07e7fe7bbec1f0e8993734c8bd4750e01feb21b6dc9'
        );
    }""")
    return page

@app.before_serving
async def before_serving():
    await initialize_browser()

@app.after_serving
async def after_serving():
    global playwright, browser
    if browser:
        await browser.close()
    if playwright:
        await playwright.stop()

@app.route('/encrypt', methods=['GET'])
async def encrypt():
    k = request.args.get('k')
    if not k:
        return jsonify({"error": "Missing 'k' parameter"}), 400
    
    try:
        page = await create_page()
        result = await page.evaluate(f"RSAUtils.encryptedString(h, '{k}')")
        await page.close()
        return jsonify({"result": result})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run()