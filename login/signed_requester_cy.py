import requests, hashlib, hmac, random, base64, time, uuid
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
from urllib.parse import urlparse
from rich import print

class IFLYTEKSignedRequest(requests.Session):
    def __init__(self, public_key, secret_key, app_id):
        super().__init__()
        self.public_key = public_key
        self.secret_key = secret_key
        self.app_id = app_id
        self.headers.update({'S-Auth-AppId': app_id})
        self.headers.update({'S-Auth-ClientType': 'pc-web'})
        self.headers.update({'S-Auth-Version': '1'})
        self.headers.update({'S-Auth-Stage': 'RELEASE'})

    def sign_random_key(self) -> str:
        str2 = random.randint(100000, 1000000)
        str2 = str(str2)
        # 将十六进制字符串转换为字节
        public_key_bytes = base64.b64decode(self.public_key)
        # 从字节中恢复公钥
        rsa_key = RSA.import_key(public_key_bytes)
        # 创建一个使用PKCS1_OAEP填充的加密器
        cipher = PKCS1_OAEP.new(rsa_key)
        # 加密数据
        encrypted_data = cipher.encrypt(str2.encode('utf-8'))
        # 将加密后的数据转换为Base64编码的字符串
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def sign(self, url:str, random_key:str, nonce:str, timestamp:str, content_md5:str) -> str:
        # secret, path, random_key, auth_nonce, timestamp, app_id, content_md5
        # 处理path
        parsed_url = urlparse(url)
        path = parsed_url.path
        if parsed_url.query:
            path += '?' + parsed_url.query
        # 生成签名，n按照此顺序拼接
        string_to_sign = [path, random_key, nonce, timestamp, self.app_id, content_md5]
        # 删除空值
        string_to_sign = [s for s in string_to_sign if s]
        # 拼接字符串
        string_to_sign = '|'.join(string_to_sign)
        # 使用HMAC-SHA256算法生成签名
        signature = hmac.new(self.secret_key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha256).digest()
        # 将签名转换为Base64编码的字符串
        signature = base64.b64encode(signature).decode('utf-8')
        return signature
    def request(self, method: str, url: str, _options: dict = {}, **kwargs):
        options = _options.copy()
        options['auth_type'] = options.get('auth_type', 0)  # 0:不包含md5和随机字符串，1:包含md5和随机字符串
        options['auth_nonce'] = options.get('auth_nonce', str(uuid.uuid4()))
        options['timestamp'] = options.get('timestamp', str(round(time.time() * 1000)))
        if options['auth_type'] == 1:
            options['content_md5'] = options.get('content_md5', hashlib.md5(kwargs['data'].encode('utf-8')).hexdigest())
            options['random_key'] = options.get('random_key', self.sign_random_key())
            options['signature'] = options.get('signature', self.sign(url, options['random_key'], options['auth_nonce'], options['timestamp'], options['content_md5']))
        else:
            options['signature'] = options.get('signature', self.sign(url, '', options['auth_nonce'], options['timestamp'], ''))
        self.headers.update({'S-Auth-Nonce': options['auth_nonce']})
        self.headers.update({'S-Auth-Timestamp': options['timestamp']})
        self.headers.update({'S-Auth-Signature': options['signature']})
        print('请求参数:', options)
        return super().request(method, url, **kwargs)
    def get(self, url, options: dict = {}, **kwargs):
        return self.request('GET', url, options, **kwargs)
    def post(self, url, options: dict = {}, **kwargs):
        return self.request('POST', url, options, **kwargs)
    def put(self, url, options: dict = {}, **kwargs):
        return self.request('PUT', url, options, **kwargs)
    def delete(self, url, options: dict = {}, **kwargs):
        return self.request('DELETE', url, options, **kwargs)

if __name__ == '__main__':
    public_key = "305C300D06092A864886F70D0101010500034B003048024100C90131F09E314134F2F6A4DEF9FE7BA1509C9B8E3463D6C3E462579275899CAA31943608214AD3B2760E6A5B7772BECA7E1DF2B1DBBACEA85FB9B84750D7CE870203010001"
    sign = IFLYTEKSignedRequest(public_key, "a6dc8753aa66268d", "xkt-zhkt-lsc")

    url = "https://xktptapi.changyan.com/futureclass/resourceServer/leader/share/record?userId=1500000100279486681&page=1&limit=16&endTime=2024-12-10+23%3A59%3A59&startTime=2024-11-10+19%3A11%3A13"

    headers = {
        "Host": "xktptapi.changyan.com",
        "Connection": "keep-alive",
        "kt-platform": "3",
        "sec-ch-ua": '"Not?A_Brand";v="8", "Chromium";v="108"',
        "X-B3-TraceId": "eac5897c-9736-410c-ae2f-be7de85396c9",
        "kt-appver": "*******",
        "S-Auth-Stage": "RELEASE",
        "S-Auth-GroupId": "[object Object]",
        "kt-productid": "p_qzk5",
        "S-Auth-Version": "1",
        "S-Auth-Token": "b2135079-6d57-4a92-9b03-81810f1bd4ec",
        "sec-ch-ua-platform": '"Windows"',
        "sucAccessDeviceId": "2C-FD-A1-1F-15-F6;000806EAFEDAF387BFEBFBFF760CD7CE",
        "S-Auth-DeviceId": "2C-FD-A1-1F-15-F6",
        "X-B3-cs-application": "xkt-zhkt-lsc",
        "kt-sn": "HAN0CV05L780417",
        "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
        "Accept": "application/json, text/plain, */*",
        "S-Token-UserId": "1500000100279486681",
        "kt-channel": "xkt_greenclass_qzk5",
        "kt-comboid": "1630149962142720001",
        "S-Auth-AppId": "xkt-zhkt-lsc",
        "sucOriginAppKey": "xkt-zhkt-lsc",
        "sucUserToken": "b2135079-6d57-4a92-9b03-81810f1bd4ec",
        "S-Auth-Mac": "2C-FD-A1-1F-15-F6",
        "S-Auth-ClientType": "pc-web",
        "X-B3-cs-platform": "1019",
        "sucClientType": "pc-web",
        "X-B3-ParentSpanId": "eac5897c-9736-410c-ae2f-be7de85396c9",
        "S-Auth-ExtInfo": "{}",
        "sucAccessIp": "***********",
        "sec-ch-ua-mobile": "?0",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) main/0.1.2 Chrome/108.0.5359.215 Electron/22.3.8 Safari/537.36",
        "X-B3-SpanId": "99bfad02-d3af-4239-bab4-d7a43995b079",
        "kt-appid": "main",
        "Sec-Fetch-Site": "cross-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN",
        "Cookie": "aliyungf_tc=fb78cd2626860934ae89331b726c78411d4065c02c2ee3075a53fb0dad713257"
    }

    response = sign.get(url, headers=headers)

    # 打印返回内容
    print(response.json())
