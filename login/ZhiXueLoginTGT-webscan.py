import requests, qrcode.main, uuid, base64, sys, time, json, Crypto.PublicKey.RSA, Crypto.Cipher.PKCS1_v1_5
REQUEST_USERAGENT = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
from pyzbar.pyzbar import decode
from PIL import ImageGrab
import requests
from urllib.parse import urlparse, parse_qs


def print_QR(text: str):
    print(text)
    code = qrcode.main.QRCode()
    code.add_data(base64.b64encode(text.encode()).decode())
    code.print_ascii()


class ZhiXueLogin:
    def __init__(self):
        self.deviceID = str(uuid.uuid4()).upper()



    def generate_QR(self, from_clipboard=False):
        session = requests.Session()
        session.headers.update(REQUEST_USERAGENT)
        self.session_temp = session

        if from_clipboard:
            try:
                # 1. 从剪贴板获取图片
                clipboard_image = ImageGrab.grabclipboard()
                if clipboard_image is None:
                    print("剪贴板中没有图片")
                    raise ValueError("剪贴板中没有图片")

                # 2. 使用pyzbar解码二维码
                decoded_objects = decode(clipboard_image)
                if not decoded_objects:
                    raise ValueError("未能从图片中解码出二维码内容")

                qr_data = decoded_objects[0].data.decode('utf-8')
                try:
                    # 尝试Base64解码
                    decoded_data = base64.b64decode(qr_data).decode('utf-8')
                    print("检测到Base64编码的二维码内容，已解码")
                    qr_url = decoded_data
                except:
                    # 如果不是Base64，直接使用原始数据
                    qr_url = qr_data
                # 3. 解析URL获取参数
                parsed = urlparse(qr_url)
                params = parse_qs(parsed.query)

                # 4. 提取codeId和deviceId
                self.codeID = params.get('codeId', [None])[0]
                device_id_from_qr = params.get('deviceId', [None])[0]

                if not self.codeID:
                    raise ValueError("URL中未找到codeId参数")

                # 可选：更新deviceId
                if device_id_from_qr and device_id_from_qr != self.deviceID:
                    print(f"更新deviceId: {self.deviceID} -> {device_id_from_qr}")
                    self.deviceID = device_id_from_qr

                print(f"成功从剪贴板二维码解析: codeId={self.codeID}")

            except Exception as e:
                print(f"剪贴板二维码解析失败: {e}")
                exit()
        else:
            return self._generate_new_qr(session)

        self.session_temp = session
        return self.codeID

    def _generate_new_qr(self, session):
        """Helper method to generate a new QR code"""
        self.codeID = session.post(
            "https://www.zhixue.com/edition/code/create",
            data={"deviceId": self.deviceID}
        ).json()["data"]["codeId"]
        return self.codeID

    def let_user_scan_QR(self, show_QR = True):
        session = self.session_temp
        codeURL = f"https://www.zhixue.com/edition/code/login?deviceId={self.deviceID}&codeId={self.codeID}&from=zhixue_web_login&redirectUrl=https://www.zhixue.com/htm-vessel"
        if show_QR:
            print_QR(codeURL)
        for num in range(1, 21):
            response = session.post("https://www.zhixue.com/edition/code/status", data = {"deviceId": self.deviceID, "codeId": self.codeID}).json()
            status = response["data"]["status"]
            if status == 0:
                print("")
                raise Exception("登录智学网异常: 二维码过期.")
                sys.exit()
            if status == 1:
                print(f"等待被扫码... {num}", end = "\r")
                time.sleep(1)
            if status == 2:
                print("")
                self.userID = response["data"]["userId"]
                self.ticket = response["data"]["ticket"]
                self.atLoginURL = response["data"]["redirectUrl"]
                return
        print("")
        raise Exception("登录智学网异常: 扫码超时.")

    def finish_login(self):
        session = self.session_temp
        session.get(self.atLoginURL)
        result = session.get(
            "https://sso.zhixue.com/sso_alpha//login",
            params = {"service": "https://www.zhixue.com:443/ssoservice.jsp"}
        ).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))
        if result["result"] != "success":
            raise Exception(f"登录智学网异常: {result['data']}")
        if "st" not in result["data"]:
            raise Exception(f"登录智学网异常: st 未找到.")
        st = result["data"]["st"]

        result = session.post(
            "https://www.zhixue.com/ssoservice.jsp",
            data = {"action": "login", "ticket": st}
        ).text
        result = result.replace("\r", "").split("\n", 1)[0]
        if result != "success":
            raise Exception(f"登录智学网异常: {result}")

        self.session = session
        del self.session_temp


class ZhiXueTGT:
    def __init__(self, account, deviceID, tgt):
        self.account = account
        self.deviceID = deviceID
        self.tgt = tgt

    def scan_QR(self, deviceID, codeID):
        session = requests.Session()
        session.headers.update(REQUEST_USERAGENT)
        self.session = session

        result = session.post(
            "https://open.changyan.com/sso/v1/api",
            data = {
                'tgt': self.tgt,
                'method': 'sso.extend.tgt',
                'ncetAppId': 'E3KzZvjVkC8kQXWBlR5521GztpApNn99',
                'appId': 'zhixue_teacher',
                'osType': 'android',
                'client': 'android',
                'userAgent': '智学网教师端',
                'userProxy': 'false',
                'deviceId': self.deviceID,
                'deviceName': 'sth1,sth2,sth3',
                'networkState': 'wifi',
                'extInfo': '{"deviceId":"%s"}' % self.deviceID
            }
        ).json()
        print(result)
        at = result["data"]["at"]
        userID = result["data"]["userId"]

        result = session.post(
            "https://app.zhixue.com/appteacher/home/<USER>",
            data = {
                'appId': "zhixue_teacher",
                'at': at,
                'autologin': 'true',
                'ncetAppId': 'E3KzZvjVkC8kQXWBlR5521GztpApNn99',
                'userId': userID
            }
        ).json()
        print(result["result"]["user"])
        token = result["result"]["user"]["token"]

        result = self.session.post(
            "https://open.changyan.com/sso/v1/api",
            data = {
                'tgt': self.tgt,
                'method': 'sso.create.at',
                'userAgent': '智学网教师端',
                'userProxy': False,
                'deviceId': self.deviceID,
                'deviceName': 'sth1,sth2,sth3',
                'networkState': 'wifi',
                'extInfo': '{"deviceId":"%s"}' % self.deviceID,
                'ncetAppId': 'E3KzZvjVkC8kQXWBlR5521GztpApNn99',
                'service': 'https://www.zhixue.com/htm-vessel',
                'appId': 'zhixue_teacher',
                'osType': 'android',
                'client': 'android'
            }
        ).json()
        print(result)
        atLoginURL = result["data"]["service"]
        result = self.session.get(
            "https://www.zhixue.com/edition/code/login",
            params = {
                "deviceId": deviceID,
                "codeId": codeID,
                "from": "zhixue_web_login",
                "redirectUrl": atLoginURL,
                "token": token,
                "tgt": self.tgt
            }
        ).json()
        print(result)


if __name__ == "__main__":
    if "TGT 登录示例":
        teacher = ZhiXueTGT("sfzzhuhui", "24-1C-04-11-CD-44", "TGT-617231-zaI7CguYa2nPh1UiIv7PsW0Pb6aQR1GQYGtGoz4cO5JukmzgGa-open.changyan.com")
        # teacher = ZhiXueTGT("sfzyedongjun", "abfdd41b-0759-4f95-8b36-a6358906f70f",
        #                     "TGT-1223803-zBV0euYV1mmcvEiyOTRkL2jGUjLOxpVMiHv7xA2Gqh0xrbcNXt-open.changyan.com")

        zhixuelogin = ZhiXueLogin()
        try:
            zhixuelogin.generate_QR(from_clipboard=True)
            print(f"Got codeID from clipboard: {zhixuelogin.codeID}")
        except Exception as e:
            print(f"Couldn't get QR from clipboard: {e}")
            print("Generating new QR code instead...")
            zhixuelogin.generate_QR()
            print(f"New codeID: {zhixuelogin.codeID}")
        print("模拟扫码...")
        teacher.scan_QR(zhixuelogin.deviceID, zhixuelogin.codeID)
        # zhixuelogin.let_user_scan_QR(show_QR = False)
        print("完成登录...")
        # zhixuelogin.finish_login()
        print("登录成功.")
        # print(zhixuelogin.session.get("https://www.zhixue.com/exam/common/getCurrentUser").json())
