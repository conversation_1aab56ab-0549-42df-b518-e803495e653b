import requests, qrcode.main, uuid, base64, sys, time, json, Crypto.PublicKey.RSA, Crypto.Cipher.PKCS1_v1_5
REQUEST_USERAGENT = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}



def print_QR(text: str):
    print(text)
    code = qrcode.main.QRCode()
    code.add_data(base64.b64encode(text.encode()).decode())
    code.print_ascii()


class ZhiXueLogin:
    def __init__(self):
        self.deviceID = str(uuid.uuid4()).upper()

    def generate_QR(self):
        session = requests.Session()
        session.headers.update(REQUEST_USERAGENT)
        self.codeID = session.post(
            "https://www.zhixue.com/edition/code/create",
            data = {"deviceId": self.deviceID}
        ).json()["data"]["codeId"]
        self.session_temp = session

    def let_user_scan_QR(self, show_QR = True):
        session = self.session_temp
        codeURL = f"https://www.zhixue.com/edition/code/login?deviceId={self.deviceID}&codeId={self.codeID}&from=zhixue_web_login&redirectUrl=https://www.zhixue.com/htm-vessel"
        if show_QR:
            print_QR(codeURL)
        for num in range(1, 21):
            response = session.post("https://www.zhixue.com/edition/code/status", data = {"deviceId": self.deviceID, "codeId": self.codeID}).json()
            status = response["data"]["status"]
            if status == 0:
                print("")
                raise Exception("登录智学网异常: 二维码过期.")
                sys.exit()
            if status == 1:
                print(f"等待被扫码... {num}", end = "\r")
                time.sleep(1)
            if status == 2:
                print("")
                self.userID = response["data"]["userId"]
                self.ticket = response["data"]["ticket"]
                self.atLoginURL = response["data"]["redirectUrl"]
                return
        print("")
        raise Exception("登录智学网异常: 扫码超时.")

    def finish_login(self):
        session = self.session_temp
        session.get(self.atLoginURL)
        result = session.get(
            "https://sso.zhixue.com/sso_alpha//login",
            params = {"service": "https://www.zhixue.com:443/ssoservice.jsp"}
        ).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))
        if result["result"] != "success":
            raise Exception(f"登录智学网异常: {result['data']}")
        if "st" not in result["data"]:
            raise Exception(f"登录智学网异常: st 未找到.")
        st = result["data"]["st"]

        result = session.post(
            "https://www.zhixue.com/ssoservice.jsp",
            data = {"action": "login", "ticket": st}
        ).text
        result = result.replace("\r", "").split("\n", 1)[0]
        if result != "success":
            raise Exception(f"登录智学网异常: {result}")

        self.session = session
        del self.session_temp


class ZhiXueTGT:
    def __init__(self, account, deviceID, tgt):
        self.account = account
        self.deviceID = deviceID
        self.tgt = tgt

    def scan_QR(self, deviceID, codeID):
        session = requests.Session()
        session.headers.update(REQUEST_USERAGENT)
        self.session = session

        result = session.post(
            "https://open.changyan.com/sso/v1/api",
            data = {
                'tgt': self.tgt,
                'method': 'sso.extend.tgt',
                'ncetAppId': 'E3KzZvjVkC8kQXWBlR5521GztpApNn99',
                'appId': 'zhixue_teacher',
                'osType': 'android',
                'client': 'android',
                'userAgent': '智学网教师端',
                'userProxy': 'false',
                'deviceId': self.deviceID,
                'deviceName': 'sth1,sth2,sth3',
                'networkState': 'wifi',
                'extInfo': '{"deviceId":"%s"}' % self.deviceID
            }
        ).json()
        # print(result)
        at = result["data"]["at"]
        userID = result["data"]["userId"]

        result = session.post(
            "https://app.zhixue.com/appteacher/home/<USER>",
            data = {
                'appId': "zhixue_teacher",
                'at': at,
                'autologin': 'true',
                'ncetAppId': 'E3KzZvjVkC8kQXWBlR5521GztpApNn99',
                'userId': userID
            }
        ).json()
        # print(result["result"]["user"])
        token = result["result"]["user"]["token"]

        result = self.session.post(
            "https://open.changyan.com/sso/v1/api",
            data = {
                'tgt': self.tgt,
                'method': 'sso.create.at',
                'userAgent': '智学网教师端',
                'userProxy': False,
                'deviceId': self.deviceID,
                'deviceName': 'sth1,sth2,sth3',
                'networkState': 'wifi',
                'extInfo': '{"deviceId":"%s"}' % self.deviceID,
                'ncetAppId': 'E3KzZvjVkC8kQXWBlR5521GztpApNn99',
                'service': 'https://www.zhixue.com/htm-vessel',
                'appId': 'zhixue_teacher',
                'osType': 'android',
                'client': 'android'
            }
        ).json()
        # print(result)
        atLoginURL = result["data"]["service"]
        result = self.session.get(
            "https://www.zhixue.com/edition/code/login",
            params = {
                "deviceId": deviceID,
                "codeId": codeID,
                "from": "zhixue_web_login",
                "redirectUrl": atLoginURL,
                "token": token,
                "tgt": self.tgt
            }
        ).json()
        # print(result)


def generate_markdown_from_json(json_data):
    # 提取作业信息
    hw_title = json_data['result']['hwTitle']
    hw_description = json_data['result']['hwDescription']

    # 初始化Markdown内容
    markdown = f"# {hw_title}\n"
    markdown += f"**作业描述**: {hw_description}\n\n"

    # 处理每个部分
    for section in json_data['result']['sectionList']:
        section_title = section['title']
        markdown += f"\n## {section_title}\n"

        # 处理每个题目
        for question in section['topicList']:
            question_title = question['title']
            markdown += f"{question_title}. "

            # 处理子问题
            for child in question['children']:
                answers = child['answers'][0]  # 获取答案列表

                markdown += f"{answers.strip(' ')}; "
            markdown = markdown[0:-2]
            markdown += "\n"

    return markdown


def fetch_hw_advance(selection:int = None):
    teacher = ZhiXueTGT("sfzzhuhui", "24-1C-04-11-CD-44",
                        "TGT-617231-zaI7CguYa2nPh1UiIv7PsW0Pb6aQR1GQYGtGoz4cO5JukmzgGa-open.changyan.com")
    # teacher = ZhiXueTGT("sfzyedongjun", "abfdd41b-0759-4f95-8b36-a6358906f70f",
    #                     "TGT-1223803-zBV0euYV1mmcvEiyOTRkL2jGUjLOxpVMiHv7xA2Gqh0xrbcNXt-open.changyan.com")

    zhixuelogin = ZhiXueLogin()
    # print("生成二维码...")
    zhixuelogin.generate_QR()
    # print codeid
    # print("二维码ID:", zhixuelogin.codeID)
    # print("模拟扫码...")
    teacher.scan_QR(zhixuelogin.deviceID, zhixuelogin.codeID)
    zhixuelogin.let_user_scan_QR(show_QR=False)
    # print("完成登录...")
    zhixuelogin.finish_login()
    # print("登录成功.")
    # print(zhixuelogin.session.get("https://www.zhixue.com/exam/common/getCurrentUser").json())
    response = zhixuelogin.session.get("https://www.zhixue.com/middleweb/newToken")
    headers = {
        "authorization": response.json().get("result").get("token"),
    }
    # 更新session的header
    zhixuelogin.session.headers.update(headers)

    payload = {
        "base": {
            "appId": "OAXI57PG",
            "appVersion": "",
            "sysVersion": "v1001",
            "sysType": "web",
            "packageName": "com.iflytek.edu.hw",
            "udid": "9190943208000048659",
            "expand": {}
        },
        "params": {
            "homeworkStatus": 1,
            "subjectCode": "",
            "classId": "",
            "hwTagCode": "",
            "page": 1,
            "size": 10,
            "createTimeLeftEndPoint": 1700000000000,
            "createTimeRightEndPoint": 1800000000000
        }
    }

    response = zhixuelogin.session.post(
        "https://mhw.zhixue.com/hw/tea/teaHwList",
        json=payload
    )

    # print(response.json())
    # print("待发布作业获取成功")
    hw_list = response.json().get("result").get("content")
    if not selection:
        printable_hw = "当前账号 sfzzhuhui 可超前点播的作业列表：\n"
        if hw_list:
            for i in range(len(hw_list)):
                # printable_hw += f"{hw.get('id')} ({hw.get('title')})\n"
                printable_hw += f"{i + 1}. {hw_list[i].get('title')}\n"
        else:
            printable_hw = "当前账号 sfzzhuhui 暂无可超前点播的作业"
        return printable_hw
        # print(printable_hw)

    payload = {
        "base": {
            "appId": "OAXI57PG",
            "appVersion": "",
            "sysVersion": "v1001",
            "sysType": "web",
            "packageName": "com.iflytek.edu.hw",
            "udid": "9190943208000048659",
            "expand": {}
        },
        "params": {
            "hwId": hw_list[int(selection) - 1].get("id")
        }
    }

    response = zhixuelogin.session.post(
        "https://mhw.zhixue.com/hw/manage/homework/redeploy",
        json=payload
    )

    return generate_markdown_from_json(response.json())



if __name__ == "__main__":
    result = fetch_hw_advance(1)
    print(result)