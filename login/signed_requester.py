import requests, hashlib, hmac, random, base64, time, uuid
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
from urllib.parse import urlparse
from rich import print

class IFLYTEKSignedRequest(requests.Session):
    def __init__(self, public_key, secret_key, app_id):
        super().__init__()
        self.public_key = public_key
        self.secret_key = secret_key
        self.app_id = app_id
        self.headers.update({'S-Auth-AppId': app_id})
        self.headers.update({'User-Agent': 'ShieldAndroidSDK'})
        self.headers.update({'S-Auth-ClientType': 'android'})
        self.headers.update({'S-Auth-Version': '1'})
        self.headers.update({'S-SDK-Version': '1.0.7'})
        self.headers.update({'S-Auth-Stage': 'RELEASE'})

    def sign_random_key(self) -> str:
        str2 = random.randint(100000, 1000000)
        str2 = str(str2)
        # 将十六进制字符串转换为字节
        public_key_bytes = base64.b64decode(self.public_key)
        # 从字节中恢复公钥
        rsa_key = RSA.import_key(public_key_bytes)
        # 创建一个使用PKCS1_OAEP填充的加密器
        cipher = PKCS1_OAEP.new(rsa_key)
        # 加密数据
        encrypted_data = cipher.encrypt(str2.encode('utf-8'))
        # 将加密后的数据转换为Base64编码的字符串
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def sign(self, url:str, random_key:str, nonce:str, timestamp:str, content_md5:str) -> str:
        # secret, path, random_key, auth_nonce, timestamp, app_id, content_md5
        # 处理path
        parsed_url = urlparse(url)
        path = parsed_url.path
        if parsed_url.query:
            path += '?' + parsed_url.query
        # 生成签名，n按照此顺序拼接
        string_to_sign = [path, random_key, nonce, timestamp, self.app_id, content_md5]
        # 删除空值
        string_to_sign = [s for s in string_to_sign if s]
        # 拼接字符串
        string_to_sign = '|'.join(string_to_sign)
        # 使用HMAC-SHA256算法生成签名
        signature = hmac.new(self.secret_key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha256).digest()
        # 将签名转换为Base64编码的字符串
        signature = base64.b64encode(signature).decode('utf-8')
        return signature
    def request(self, method: str, url: str, _options: dict = {}, **kwargs):
        options = _options.copy()
        options['auth_type'] = options.get('auth_type', 0)  # 0:不包含md5和随机字符串，1:包含md5和随机字符串
        options['auth_nonce'] = options.get('auth_nonce', str(uuid.uuid4()))
        options['timestamp'] = options.get('timestamp', str(round(time.time() * 1000)))
        if options['auth_type'] == 1:
            options['content_md5'] = options.get('content_md5', hashlib.md5(kwargs['data'].encode('utf-8')).hexdigest())
            options['random_key'] = options.get('random_key', self.sign_random_key())
            options['signature'] = options.get('signature', self.sign(url, options['random_key'], options['auth_nonce'], options['timestamp'], options['content_md5']))
        else:
            options['signature'] = options.get('signature', self.sign(url, '', options['auth_nonce'], options['timestamp'], ''))
        self.headers.update({'S-Auth-Nonce': options['auth_nonce']})
        self.headers.update({'S-Auth-Timestamp': options['timestamp']})
        self.headers.update({'S-Auth-Signature': options['signature']})
        return super().request(method, url, **kwargs)
    def get(self, url, options: dict = {}, **kwargs):
        return self.request('GET', url, options, **kwargs)
    def post(self, url, options: dict = {}, **kwargs):
        return self.request('POST', url, options, **kwargs)
    def put(self, url, options: dict = {}, **kwargs):
        return self.request('PUT', url, options, **kwargs)
    def delete(self, url, options: dict = {}, **kwargs):
        return self.request('DELETE', url, options, **kwargs)

if __name__ == '__main__':
    public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9BWRLAgT8FZ5wVwFsLskv3Jca42O8ZrFi9fs3Gq6ri62R9qW9zX8Mr9hQbyGy2vp+wbhPdkTf3HYpuUyF6R4ysVYovdzPn6JKZQL4RN0rElrR+foWXjcCi2pkVFYo45PDEFtDvxwCTyevTlETDKq03dLBtrkp0cTQOdgDoLpmIQIDAQAB"
    sign = IFLYTEKSignedRequest(public_key, "a6dc8753aa66268d", "xkt-zhkt-lsc")
    