import requests
import hashlib
import uuid
import json


import pythonmonkey as pm

# JavaScript代码，用于RC4加密
js_code = """
function rc4(key, str) {
    var s = [], j = 0, x, res = '';
    for (var i = 0; i < 256; i++) {
        s[i] = i;
    }
    for (i = 0; i < 256; i++) {
        j = (j + s[i] + key.charCodeAt(i % key.length)) % 256;
        x = s[i];
        s[i] = s[j];
        s[j] = x;
    }
    i = 0;
    j = 0;
    for (var y = 0; y < str.length; y++) {
        i = (i + 1) % 256;
        j = (j + s[i]) % 256;
        x = s[i];
        s[i] = s[j];
        s[j] = x;
        res += String.fromCharCode(str.charCodeAt(y) ^ s[(s[i] + s[j]) % 256]);
    }
    return res;
}
"""

# 加载JavaScript代码
pm.eval(js_code)

# 定义Python函数来调用JavaScript的RC4加密
def encode_password(password: str) -> str:
    # 使用JavaScript的rc4函数进行加密
    encrypted = pm.eval(f"rc4('iflytek_pass_edp', '{password}')")
    print(encrypted)
    return encrypted.encode('utf-8').hex()

# 示例调用
encoded_password = encode_password("your_password")
print(f"Encoded Password: {encoded_password}")


def get_auth_header() -> dict:
    # 生成认证头
    authguid = str(uuid.uuid1())
    authtimestamp = str(int(time.time() * 1000))
    authbizcode = "0001"
    authtoken = hashlib.md5((authguid + authtimestamp + "iflytek!@#123student").encode('utf-8')).hexdigest()
    return {
        "authguid": authguid,
        "authtimestamp": authtimestamp,
        "authtoken": authtoken,
        "authbizcode": authbizcode
    }


def login(username: str, password: str) -> dict:
    if not username.strip() or not password.strip():
        return {
            "errorCode": 1002,
            "errorInfo": "帐号或密码错误"
        }

    encoded_password = encode_password(password)
    data = {
        "loginName": username,
        "password": encoded_password,
        "description": json.dumps({"encrypt": ["password"]})
    }

    try:
        response = requests.get("https://www.zhixue.com/container/app/parWeakCheckLogin", params=data)
        body = response.json()

        if body.get("errorCode"):
            return {
                "errorCode": body["errorCode"],
                "errorInfo": body["errorInfo"]
            }

        return {
            "errorCode": 0,
            "errorInfo": "操作成功",
            "result": {
                "token": body["result"]["token"],
                "childId": body["result"]["id"],
                "user": {
                    "name": body["result"]["name"],
                    "avatar": body["result"]["userInfo"]["avatar"],
                    "code": body["result"]["userInfo"]["code"]
                },
                "class": {
                    "name": body["result"]["clazzInfo"]["name"]
                },
                "school": {
                    "name": body["result"]["userInfo"]["school"]["schoolName"]
                }
            }
        }
    except Exception as error:
        return {
            "errorCode": -1,
            "errorInfo": f"发送请求失败：{str(error)}"
        }


# 示例调用
result = login("310116200805160630", "56415880wen")
print(result)