# coding:utf-8
import os
import base64

# 留空即可
folder_name = 'output'
file_name = input('请输入生成文件的名称：')
os.makedirs(folder_name, exist_ok=True)

# 设置html文件密码
passwd = input('请设定打开网页时要输入的密码（无法找回！请务必牢记）：')

def convert_video_to_base64(filepath):
    with open(filepath, "rb") as video_file:
        video_base64bytes = base64.b64encode(video_file.read())
        video_base64str = video_base64bytes.decode('utf-8')
    return video_base64str

def generate_html(video_dict, filename, directory):
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Base64 Video Player</title>
    <meta charset="utf-8" />
    <script type="text/javascript">   
loopy()   
function loopy() {   
var sWord =""  
while (sWord != "%s") {//设置密码
sWord = prompt("输入完整性校验码：")   
}   
alert("欢迎访问")   
}   
</script>
</head>
<body>
"""%(passwd)

    for name, base64video in video_dict.items():
        html += f"<h2>{name}</h2>\n"
        html += '<video controls>\n'
        html += f'<source src="data:video/mp4;base64,{base64video}" type="video/mp4">\n'
        html += 'Your browser does not support the video element.\n'
        html += '</video>\n'

    html += """
</body>
</html>
"""
    with open(os.path.join(directory, '%s.docx' % filename), 'w', encoding='utf-8') as html_file:
        html_file.write(html)

def main(directory='video'):
    filenames = [f for f in os.listdir(directory) if f.endswith('.mp4')]
    for i in range(0, len(filenames), 2):
        video_dict = {}
        for filename in filenames[i:i+2]:  # Chunks of two files at a time
            filepath = os.path.join(directory, filename)
            base64video = convert_video_to_base64(filepath)
            video_dict[filename] = base64video
        generate_html(video_dict, f"{file_name}_{int(i/2)+1}", folder_name)

if __name__ == "__main__":
    main()
