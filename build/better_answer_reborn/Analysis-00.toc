(['E:\\code\\zhixue\\better_answer_reborn.py'],
 ['E:\\code\\zhixue'],
 [],
 ['E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
  'E:\\code\\zhixue\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'E:\\code\\zhixue\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.8.6 (tags/v3.8.6:db45529, Sep 23 2020, 15:52:53) [MSC v.1927 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('better_answer_reborn',
   'E:\\code\\zhixue\\better_answer_reborn.py',
   'PYSOURCE')],
 [('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bz2.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\csv.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ast.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('zhixuewang.account',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\account.py',
   'PYMODULE'),
  ('zhixuewang',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\__init__.py',
   'PYMODULE'),
  ('playwright.async_api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\async_api\\__init__.py',
   'PYMODULE'),
  ('playwright.async_api._context_manager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\async_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._transport',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_transport.py',
   'PYMODULE'),
  ('playwright._impl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._helper',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_helper.py',
   'PYMODULE'),
  ('playwright._impl._network',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_network.py',
   'PYMODULE'),
  ('playwright._impl._page',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_page.py',
   'PYMODULE'),
  ('playwright._impl._locator',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_locator.py',
   'PYMODULE'),
  ('playwright._impl._video',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_video.py',
   'PYMODULE'),
  ('playwright._impl._js_handle',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'PYMODULE'),
  ('playwright._impl._map',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_map.py',
   'PYMODULE'),
  ('playwright._impl._input',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_input.py',
   'PYMODULE'),
  ('playwright._impl._har_router',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'PYMODULE'),
  ('playwright._impl._local_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'PYMODULE'),
  ('playwright._impl._file_chooser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'PYMODULE'),
  ('playwright._impl._element_handle',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'PYMODULE'),
  ('playwright._impl._set_input_files_helpers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'PYMODULE'),
  ('playwright._impl._writable_stream',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'PYMODULE'),
  ('playwright._impl._download',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_download.py',
   'PYMODULE'),
  ('playwright._impl._console_message',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'PYMODULE'),
  ('playwright._impl._clock',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_clock.py',
   'PYMODULE'),
  ('playwright._impl._artifact',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'PYMODULE'),
  ('playwright._impl._stream',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_stream.py',
   'PYMODULE'),
  ('playwright._impl._accessibility',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'PYMODULE'),
  ('playwright._impl._frame',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_frame.py',
   'PYMODULE'),
  ('pyee',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pyee.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('playwright._impl._fetch',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'PYMODULE'),
  ('playwright._impl._playwright',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'PYMODULE'),
  ('playwright._impl._selectors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'PYMODULE'),
  ('playwright._impl._browser_type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'PYMODULE'),
  ('playwright._impl._json_pipe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'PYMODULE'),
  ('pyee.asyncio',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('playwright._impl._browser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_browser.py',
   'PYMODULE'),
  ('playwright._impl._cdp_session',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'PYMODULE'),
  ('playwright._impl._tracing',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'PYMODULE'),
  ('playwright._impl._browser_context',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'PYMODULE'),
  ('playwright._impl._web_error',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'PYMODULE'),
  ('playwright._impl._dialog',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'PYMODULE'),
  ('playwright._impl._waiter',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_waiter.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('playwright._impl._event_context_manager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._str_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'PYMODULE'),
  ('playwright._impl._greenlets',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_greenlets.py',
   'PYMODULE'),
  ('greenlet',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('playwright._impl._glob',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_glob.py',
   'PYMODULE'),
  ('playwright._impl._driver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_driver.py',
   'PYMODULE'),
  ('playwright._repo_version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_repo_version.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('playwright._impl._object_factory',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'PYMODULE'),
  ('playwright._impl._connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_connection.py',
   'PYMODULE'),
  ('playwright._impl._assertions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'PYMODULE'),
  ('playwright.async_api._generated',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\async_api\\_generated.py',
   'PYMODULE'),
  ('playwright._impl._async_base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_async_base.py',
   'PYMODULE'),
  ('playwright._impl._impl_to_api_mapping',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'PYMODULE'),
  ('playwright',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._errors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_errors.py',
   'PYMODULE'),
  ('playwright._impl._api_structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('zhixuewang.teacher.teacher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\teacher.py',
   'PYMODULE'),
  ('zhixuewang.teacher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.teacher.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\urls.py',
   'PYMODULE'),
  ('zhixuewang.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\urls.py',
   'PYMODULE'),
  ('zhixuewang.teacher.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\models.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('zhixuewang.student.student',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\student.py',
   'PYMODULE'),
  ('zhixuewang.student',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.student.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\urls.py',
   'PYMODULE'),
  ('zhixuewang.session',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\session.py',
   'PYMODULE'),
  ('requests',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('zhixuewang.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\models.py',
   'PYMODULE'),
  ('zhixuewang.tools.datetime_tool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\tools\\datetime_tool.py',
   'PYMODULE'),
  ('zhixuewang.tools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\tools\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\exceptions.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\glob.py',
   'PYMODULE')],
 [('playwright\\driver\\package\\bin\\PrintDeps.exe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\PrintDeps.exe',
   'BINARY'),
  ('playwright\\driver\\node.exe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('python38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python38.dll',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\greenlet\\_greenlet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'E:\\code\\zhixue\\build\\better_answer_reborn\\base_library.zip',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\workbench.DjbIuxix.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\workbench.DjbIuxix.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\network.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\profiler.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-Dg-JD506.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-Dg-JD506.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\NOTICE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\NOTICE',
   'DATA'),
  ('playwright\\driver\\package\\bin\\README.md',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\ascii.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\task.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zones.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.Bo_JtCyn.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.Bo_JtCyn.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\py.typed',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\README.md',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.html',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\crypto.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-DVt3E1Ef.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-DVt3E1Ef.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\workbench-D5oSwIMK.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\workbench-D5oSwIMK.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\index.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-BN0yUF4I.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-BN0yUF4I.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\env.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.D3cNFP6u.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.D3cNFP6u.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\multimap.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-B-MT5gKo.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-B-MT5gKo.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\errors.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.BRZXsPsq.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.BRZXsPsq.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\clock.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\types.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\testServerConnection-DeE2kSzz.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\testServerConnection-DeE2kSzz.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\rtti.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.DdEQ0QhM.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.DdEQ0QhM.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debug.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clock.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\headers.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\errors.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\recorderSource.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\recorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\time.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.w7WN2u1R.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.w7WN2u1R.css',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\LICENSE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\debug.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BeNbaIVa.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BeNbaIVa.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-ez37Vkbh.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-ez37Vkbh.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.ez37Vkbh.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.ez37Vkbh.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderActions.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderActions.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\comparators.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CrbWWHbf.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CrbWWHbf.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.DSXBckUd.css',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.DSXBckUd.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\transport.js',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\transport.js',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\INSTALLER',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\RECORD',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\RECORD',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\license_files\\LICENSE.BSD',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\license_files\\LICENSE.BSD',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\license_files\\LICENSE.APACHE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\license_files\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\METADATA',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\METADATA',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\WHEEL',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\license_files\\LICENSE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\license_files\\LICENSE',
   'DATA')])
