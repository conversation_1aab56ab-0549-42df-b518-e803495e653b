('E:\\code\\zhixue\\build\\better_answer_reborn\\PYZ-00.pyz',
 [('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dis.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\glob.py',
   'PYMODULE'),
  ('greenlet',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('idna',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('playwright',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\__init__.py',
   'PYMODULE'),
  ('playwright._impl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._accessibility',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'PYMODULE'),
  ('playwright._impl._api_structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'PYMODULE'),
  ('playwright._impl._artifact',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'PYMODULE'),
  ('playwright._impl._assertions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'PYMODULE'),
  ('playwright._impl._async_base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_async_base.py',
   'PYMODULE'),
  ('playwright._impl._browser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_browser.py',
   'PYMODULE'),
  ('playwright._impl._browser_context',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'PYMODULE'),
  ('playwright._impl._browser_type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'PYMODULE'),
  ('playwright._impl._cdp_session',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'PYMODULE'),
  ('playwright._impl._clock',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_clock.py',
   'PYMODULE'),
  ('playwright._impl._connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_connection.py',
   'PYMODULE'),
  ('playwright._impl._console_message',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'PYMODULE'),
  ('playwright._impl._dialog',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'PYMODULE'),
  ('playwright._impl._download',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_download.py',
   'PYMODULE'),
  ('playwright._impl._driver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_driver.py',
   'PYMODULE'),
  ('playwright._impl._element_handle',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'PYMODULE'),
  ('playwright._impl._errors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_errors.py',
   'PYMODULE'),
  ('playwright._impl._event_context_manager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._fetch',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'PYMODULE'),
  ('playwright._impl._file_chooser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'PYMODULE'),
  ('playwright._impl._frame',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_frame.py',
   'PYMODULE'),
  ('playwright._impl._glob',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_glob.py',
   'PYMODULE'),
  ('playwright._impl._greenlets',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_greenlets.py',
   'PYMODULE'),
  ('playwright._impl._har_router',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'PYMODULE'),
  ('playwright._impl._helper',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_helper.py',
   'PYMODULE'),
  ('playwright._impl._impl_to_api_mapping',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'PYMODULE'),
  ('playwright._impl._input',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_input.py',
   'PYMODULE'),
  ('playwright._impl._js_handle',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'PYMODULE'),
  ('playwright._impl._json_pipe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'PYMODULE'),
  ('playwright._impl._local_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'PYMODULE'),
  ('playwright._impl._locator',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_locator.py',
   'PYMODULE'),
  ('playwright._impl._map',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_map.py',
   'PYMODULE'),
  ('playwright._impl._network',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_network.py',
   'PYMODULE'),
  ('playwright._impl._object_factory',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'PYMODULE'),
  ('playwright._impl._page',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_page.py',
   'PYMODULE'),
  ('playwright._impl._playwright',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'PYMODULE'),
  ('playwright._impl._selectors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'PYMODULE'),
  ('playwright._impl._set_input_files_helpers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'PYMODULE'),
  ('playwright._impl._str_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'PYMODULE'),
  ('playwright._impl._stream',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_stream.py',
   'PYMODULE'),
  ('playwright._impl._tracing',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'PYMODULE'),
  ('playwright._impl._transport',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_transport.py',
   'PYMODULE'),
  ('playwright._impl._video',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_video.py',
   'PYMODULE'),
  ('playwright._impl._waiter',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_waiter.py',
   'PYMODULE'),
  ('playwright._impl._web_error',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'PYMODULE'),
  ('playwright._impl._writable_stream',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'PYMODULE'),
  ('playwright._repo_version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\_repo_version.py',
   'PYMODULE'),
  ('playwright.async_api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\async_api\\__init__.py',
   'PYMODULE'),
  ('playwright.async_api._context_manager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\async_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright.async_api._generated',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\playwright\\async_api\\_generated.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('pyee',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pyee.asyncio',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('pyee.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zhixuewang',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.account',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\account.py',
   'PYMODULE'),
  ('zhixuewang.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\exceptions.py',
   'PYMODULE'),
  ('zhixuewang.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\models.py',
   'PYMODULE'),
  ('zhixuewang.session',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\session.py',
   'PYMODULE'),
  ('zhixuewang.student',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.student.student',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\student.py',
   'PYMODULE'),
  ('zhixuewang.student.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\urls.py',
   'PYMODULE'),
  ('zhixuewang.teacher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.teacher.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\models.py',
   'PYMODULE'),
  ('zhixuewang.teacher.teacher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\teacher.py',
   'PYMODULE'),
  ('zhixuewang.teacher.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\urls.py',
   'PYMODULE'),
  ('zhixuewang.tools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\tools\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.tools.datetime_tool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\tools\\datetime_tool.py',
   'PYMODULE'),
  ('zhixuewang.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\urls.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE')])
