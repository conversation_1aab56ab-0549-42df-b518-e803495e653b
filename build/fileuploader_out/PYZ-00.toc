('E:\\code\\zhixue\\build\\fileuploader_out\\PYZ-00.pyz',
 [('Crypto',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC2',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\ARC2.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\DES.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES3',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\DES3.py',
   'PYMODULE'),
  ('Crypto.Cipher.PKCS1_OAEP',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\PKCS1_OAEP.py',
   'PYMODULE'),
  ('Crypto.Cipher.PKCS1_v1_5',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\PKCS1_v1_5.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Cipher._pkcs1_oaep_decode',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_pkcs1_oaep_decode.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.IO',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\IO\\__init__.py',
   'PYMODULE'),
  ('Crypto.IO.PEM',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\IO\\PEM.py',
   'PYMODULE'),
  ('Crypto.IO.PKCS8',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\IO\\PKCS8.py',
   'PYMODULE'),
  ('Crypto.IO._PBES',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\IO\\_PBES.py',
   'PYMODULE'),
  ('Crypto.Math',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\__init__.py',
   'PYMODULE'),
  ('Crypto.Math.Numbers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\Numbers.py',
   'PYMODULE'),
  ('Crypto.Math.Primality',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\Primality.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerBase',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\_IntegerBase.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerCustom',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\_IntegerCustom.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerGMP',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\_IntegerGMP.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerNative',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\_IntegerNative.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.PublicKey',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\__init__.py',
   'PYMODULE'),
  ('Crypto.PublicKey.RSA',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\RSA.py',
   'PYMODULE'),
  ('Crypto.PublicKey._openssh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\_openssh.py',
   'PYMODULE'),
  ('Crypto.Random',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Random.random',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Random\\random.py',
   'PYMODULE'),
  ('Crypto.Signature',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Signature\\__init__.py',
   'PYMODULE'),
  ('Crypto.Signature.pss',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Signature\\pss.py',
   'PYMODULE'),
  ('Crypto.Util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.Counter',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\Counter.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util.asn1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\asn1.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('aliyunsdkcore',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.acs_exception',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\acs_exception\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.acs_exception.error_code',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\acs_exception\\error_code.py',
   'PYMODULE'),
  ('aliyunsdkcore.acs_exception.error_msg',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\acs_exception\\error_msg.py',
   'PYMODULE'),
  ('aliyunsdkcore.acs_exception.error_type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\acs_exception\\error_type.py',
   'PYMODULE'),
  ('aliyunsdkcore.acs_exception.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\acs_exception\\exceptions.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.algorithm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\algorithm\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.algorithm.sha_hmac1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\algorithm\\sha_hmac1.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.algorithm.sha_hmac256',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\algorithm\\sha_hmac256.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.composer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\composer\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.composer.roa_signature_composer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\composer\\roa_signature_composer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.composer.rpc_signature_composer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\composer\\rpc_signature_composer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.credentials',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\credentials.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.access_key_signer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\access_key_signer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.ecs_ram_role_signer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\ecs_ram_role_signer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.ram_role_arn_signer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\ram_role_arn_signer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.rsa_key_pair_signer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\rsa_key_pair_signer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.signer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\signer.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.signer_factory',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\signer_factory.py',
   'PYMODULE'),
  ('aliyunsdkcore.auth.signers.sts_token_signer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\auth\\signers\\sts_token_signer.py',
   'PYMODULE'),
  ('aliyunsdkcore.client',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\client.py',
   'PYMODULE'),
  ('aliyunsdkcore.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\compat.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.chained_endpoint_resolver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\chained_endpoint_resolver.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.default_endpoint_resolver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\default_endpoint_resolver.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.endpoint_resolver_base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\endpoint_resolver_base.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.endpoint_resolver_rules',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\endpoint_resolver_rules.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.local_config_global_endpoint_resolver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\local_config_global_endpoint_resolver.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.local_config_regional_endpoint_resolver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\local_config_regional_endpoint_resolver.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.location',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\location\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.location.DescribeEndpointsRequest',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\location\\DescribeEndpointsRequest.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.location_service_endpoint_resolver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\location_service_endpoint_resolver.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.resolver_endpoint_request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\resolver_endpoint_request.py',
   'PYMODULE'),
  ('aliyunsdkcore.endpoint.user_customized_endpoint_resolver',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\endpoint\\user_customized_endpoint_resolver.py',
   'PYMODULE'),
  ('aliyunsdkcore.http',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\http\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.http.format_type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\http\\format_type.py',
   'PYMODULE'),
  ('aliyunsdkcore.http.http_request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\http\\http_request.py',
   'PYMODULE'),
  ('aliyunsdkcore.http.http_response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\http\\http_response.py',
   'PYMODULE'),
  ('aliyunsdkcore.http.method_type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\http\\method_type.py',
   'PYMODULE'),
  ('aliyunsdkcore.http.protocol_type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\http\\protocol_type.py',
   'PYMODULE'),
  ('aliyunsdkcore.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\request.py',
   'PYMODULE'),
  ('aliyunsdkcore.retry',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\retry\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.retry.backoff_strategy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\retry\\backoff_strategy.py',
   'PYMODULE'),
  ('aliyunsdkcore.retry.retry_condition',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\retry\\retry_condition.py',
   'PYMODULE'),
  ('aliyunsdkcore.retry.retry_policy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\retry\\retry_policy.py',
   'PYMODULE'),
  ('aliyunsdkcore.retry.retry_policy_context',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\retry\\retry_policy_context.py',
   'PYMODULE'),
  ('aliyunsdkcore.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\utils\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.utils.parameter_helper',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\utils\\parameter_helper.py',
   'PYMODULE'),
  ('aliyunsdkcore.utils.validation',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\utils\\validation.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.__version__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\__version__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests._internal_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\adapters.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\api.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.auth',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\auth.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.certs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\certs.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\compat.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.cookies',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\cookies.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\exceptions.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.hooks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\hooks.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\models.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.certifi',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.certifi.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\certifi\\core.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.big5freq',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.big5prober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.chardistribution',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.charsetgroupprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.charsetprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.codingstatemachine',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\compat.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.cp949prober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.enums',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\enums.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.escprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.escsm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.eucjpprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.euckrfreq',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.euckrprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.euctwfreq',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.euctwprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.gb2312freq',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.gb2312prober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.hebrewprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.jisfreq',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.jpcntx',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.langbulgarianmodel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.langcyrillicmodel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.langgreekmodel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.langhebrewmodel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.langthaimodel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.langturkishmodel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.latin1prober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.mbcharsetprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.mbcsgroupprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.mbcssm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.sbcharsetprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.sbcsgroupprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.sjisprober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.universaldetector',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.utf8prober',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.chardet.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\chardet\\version.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.connectionpool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.contrib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.contrib._appengine_environ',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.contrib.pyopenssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.contrib.socks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.fields',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.filepost',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.backports',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.backports.makefile',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.ordered_dict',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\ordered_dict.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.six',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.socks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\socks.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.ssl_match_hostname',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.packages.ssl_match_hostname._implementation',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.poolmanager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\request.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\response.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.queue',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.retry',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.ssl_',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.timeout',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.url',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.packages.urllib3.util.wait',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.sessions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\sessions.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.status_codes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\status_codes.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\structures.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.requests.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\utils.py',
   'PYMODULE'),
  ('aliyunsdkcore.vendored.six',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\six.py',
   'PYMODULE'),
  ('aliyunsdkkms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkkms.endpoint',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\endpoint.py',
   'PYMODULE'),
  ('aliyunsdkkms.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\request\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkkms.request.v20160120',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\request\\v20160120\\__init__.py',
   'PYMODULE'),
  ('aliyunsdkkms.request.v20160120.DecryptRequest',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\request\\v20160120\\DecryptRequest.py',
   'PYMODULE'),
  ('aliyunsdkkms.request.v20160120.EncryptRequest',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\request\\v20160120\\EncryptRequest.py',
   'PYMODULE'),
  ('aliyunsdkkms.request.v20160120.GenerateDataKeyRequest',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkkms\\request\\v20160120\\GenerateDataKeyRequest.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\codeop.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('crcmod',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\crcmod\\__init__.py',
   'PYMODULE'),
  ('crcmod._crcfunpy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\crcmod\\_crcfunpy.py',
   'PYMODULE'),
  ('crcmod.crcmod',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\crcmod\\crcmod.py',
   'PYMODULE'),
  ('crcmod.predefined',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\crcmod\\predefined.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.keywrap',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\keywrap.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\doctest.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('ecdsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\__init__.py',
   'PYMODULE'),
  ('ecdsa._compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\_compat.py',
   'PYMODULE'),
  ('ecdsa._sha3',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\_sha3.py',
   'PYMODULE'),
  ('ecdsa._version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\_version.py',
   'PYMODULE'),
  ('ecdsa.curves',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\curves.py',
   'PYMODULE'),
  ('ecdsa.der',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\der.py',
   'PYMODULE'),
  ('ecdsa.ecdh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\ecdh.py',
   'PYMODULE'),
  ('ecdsa.ecdsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\ecdsa.py',
   'PYMODULE'),
  ('ecdsa.eddsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\eddsa.py',
   'PYMODULE'),
  ('ecdsa.ellipticcurve',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\ellipticcurve.py',
   'PYMODULE'),
  ('ecdsa.errors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\errors.py',
   'PYMODULE'),
  ('ecdsa.keys',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\keys.py',
   'PYMODULE'),
  ('ecdsa.numbertheory',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\numbertheory.py',
   'PYMODULE'),
  ('ecdsa.rfc6979',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\rfc6979.py',
   'PYMODULE'),
  ('ecdsa.ssh',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\ssh.py',
   'PYMODULE'),
  ('ecdsa.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\ecdsa\\util.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('jmespath',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\__init__.py',
   'PYMODULE'),
  ('jmespath.ast',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\ast.py',
   'PYMODULE'),
  ('jmespath.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\compat.py',
   'PYMODULE'),
  ('jmespath.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\exceptions.py',
   'PYMODULE'),
  ('jmespath.functions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\functions.py',
   'PYMODULE'),
  ('jmespath.lexer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\lexer.py',
   'PYMODULE'),
  ('jmespath.parser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\parser.py',
   'PYMODULE'),
  ('jmespath.visitor',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jmespath\\visitor.py',
   'PYMODULE'),
  ('jose',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\__init__.py',
   'PYMODULE'),
  ('jose.backends',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\__init__.py',
   'PYMODULE'),
  ('jose.backends._asn1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\_asn1.py',
   'PYMODULE'),
  ('jose.backends.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\base.py',
   'PYMODULE'),
  ('jose.backends.cryptography_backend',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\cryptography_backend.py',
   'PYMODULE'),
  ('jose.backends.ecdsa_backend',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\ecdsa_backend.py',
   'PYMODULE'),
  ('jose.backends.native',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\native.py',
   'PYMODULE'),
  ('jose.backends.rsa_backend',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\backends\\rsa_backend.py',
   'PYMODULE'),
  ('jose.constants',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\constants.py',
   'PYMODULE'),
  ('jose.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\exceptions.py',
   'PYMODULE'),
  ('jose.jwk',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\jwk.py',
   'PYMODULE'),
  ('jose.jws',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\jws.py',
   'PYMODULE'),
  ('jose.jwt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\jwt.py',
   'PYMODULE'),
  ('jose.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\jose\\utils.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('oss2',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\__init__.py',
   'PYMODULE'),
  ('oss2.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\api.py',
   'PYMODULE'),
  ('oss2.auth',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\auth.py',
   'PYMODULE'),
  ('oss2.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\compat.py',
   'PYMODULE'),
  ('oss2.crc64_combine',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\crc64_combine.py',
   'PYMODULE'),
  ('oss2.credentials',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\credentials.py',
   'PYMODULE'),
  ('oss2.crypto',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\crypto.py',
   'PYMODULE'),
  ('oss2.crypto_bucket',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\crypto_bucket.py',
   'PYMODULE'),
  ('oss2.defaults',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\defaults.py',
   'PYMODULE'),
  ('oss2.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\exceptions.py',
   'PYMODULE'),
  ('oss2.headers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\headers.py',
   'PYMODULE'),
  ('oss2.http',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\http.py',
   'PYMODULE'),
  ('oss2.iterators',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\iterators.py',
   'PYMODULE'),
  ('oss2.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\models.py',
   'PYMODULE'),
  ('oss2.resumable',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\resumable.py',
   'PYMODULE'),
  ('oss2.select_params',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\select_params.py',
   'PYMODULE'),
  ('oss2.select_response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\select_response.py',
   'PYMODULE'),
  ('oss2.task_queue',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\task_queue.py',
   'PYMODULE'),
  ('oss2.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\utils.py',
   'PYMODULE'),
  ('oss2.xml_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\oss2\\xml_utils.py',
   'PYMODULE'),
  ('packaging',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('pyasn1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.encoder',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\cer\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der.encoder',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\der\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pycparser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('rsa',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\__init__.py',
   'PYMODULE'),
  ('rsa.asn1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\asn1.py',
   'PYMODULE'),
  ('rsa.common',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\common.py',
   'PYMODULE'),
  ('rsa.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\core.py',
   'PYMODULE'),
  ('rsa.key',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\key.py',
   'PYMODULE'),
  ('rsa.parallel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\parallel.py',
   'PYMODULE'),
  ('rsa.pem',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\pem.py',
   'PYMODULE'),
  ('rsa.pkcs1',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\pkcs1.py',
   'PYMODULE'),
  ('rsa.prime',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\prime.py',
   'PYMODULE'),
  ('rsa.randnum',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\randnum.py',
   'PYMODULE'),
  ('rsa.transform',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\rsa\\transform.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site.py',
   'PYMODULE'),
  ('six', 'E:\\code\\zhixue\\venv\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zhixuewang',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.account',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\account.py',
   'PYMODULE'),
  ('zhixuewang.exceptions',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\exceptions.py',
   'PYMODULE'),
  ('zhixuewang.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\models.py',
   'PYMODULE'),
  ('zhixuewang.session',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\session.py',
   'PYMODULE'),
  ('zhixuewang.student',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.student.student',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\student.py',
   'PYMODULE'),
  ('zhixuewang.student.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\student\\urls.py',
   'PYMODULE'),
  ('zhixuewang.teacher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.teacher.models',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\models.py',
   'PYMODULE'),
  ('zhixuewang.teacher.teacher',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\teacher.py',
   'PYMODULE'),
  ('zhixuewang.teacher.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\teacher\\urls.py',
   'PYMODULE'),
  ('zhixuewang.tools',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\tools\\__init__.py',
   'PYMODULE'),
  ('zhixuewang.tools.datetime_tool',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\tools\\datetime_tool.py',
   'PYMODULE'),
  ('zhixuewang.urls',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zhixuewang\\urls.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
