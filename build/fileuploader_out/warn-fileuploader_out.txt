
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by E:\code\zhixue\venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), E:\code\zhixue\venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named org - imported by pickle (optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named urllib.getproxies_environment - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.proxy_bypass_environment - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.proxy_bypass - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.getproxies - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.urlencode - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.unquote_plus - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.quote_plus - imported by urllib (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), oss2.compat (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib.quote - imported by urllib (conditional), oss2.compat (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named posix - imported by os (conditional, optional), shutil (conditional)
missing module named resource - imported by posix (top-level)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named jnius - imported by pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named platformdirs - imported by pkg_resources._vendor.platformdirs.__main__ (top-level)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), aliyunsdkcore.vendored.requests.utils (delayed, conditional)
missing module named _aix_support - imported by setuptools._distutils.py38compat (delayed, optional)
missing module named win32con - imported by setuptools._distutils.msvccompiler (optional)
missing module named win32api - imported by setuptools._distutils.msvccompiler (optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.register (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._entry_points (top-level), setuptools._reqs (top-level), setuptools.command._requirestxt (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools._core_metadata (top-level), setuptools._reqs (top-level), setuptools.config.setupcfg (top-level), setuptools.command._requirestxt (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools._core_metadata (top-level), setuptools.depends (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools._core_metadata (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level)
missing module named 'setuptools.extern.ordered_set' - imported by setuptools.dist (top-level)
missing module named StringIO - imported by six (conditional), Crypto.Util.py3compat (conditional), aliyunsdkcore.vendored.six (conditional), aliyunsdkcore.vendored.requests.packages.urllib3.packages.six (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named win_inet_pton - imported by aliyunsdkcore.vendored.requests.packages.urllib3.packages.socks (conditional, optional)
missing module named backports - imported by aliyunsdkcore.vendored.requests.packages.urllib3.packages.ssl_match_hostname (optional)
missing module named _abcoll - imported by aliyunsdkcore.vendored.requests.packages.urllib3.packages.ordered_dict (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional), aliyunsdkcore.vendored.requests.packages.urllib3.packages.ordered_dict (optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), aliyunsdkcore.vendored.requests.packages.urllib3.packages.ordered_dict (optional)
missing module named Queue - imported by aliyunsdkcore.vendored.requests.packages.urllib3.util.queue (conditional), oss2.task_queue (optional)
missing module named 'aliyunsdkcore.vendored.requests.packages.urllib3.packages.six.moves' - imported by aliyunsdkcore.vendored.requests.packages.urllib3.exceptions (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.connectionpool (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.connection (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.util.response (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.util.url (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.request (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.response (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.util.queue (top-level), aliyunsdkcore.vendored.requests.packages.urllib3.poolmanager (top-level)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional), aliyunsdkcore.vendored.requests.packages.urllib3.contrib.pyopenssl (delayed)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional), aliyunsdkcore.vendored.requests.packages.urllib3.contrib.pyopenssl (optional)
missing module named 'cryptography.hazmat.backends.openssl.x509' - imported by aliyunsdkcore.vendored.requests.packages.urllib3.contrib.pyopenssl (top-level)
missing module named 'OpenSSL.SSL' - imported by aliyunsdkcore.vendored.requests.packages.urllib3.contrib.pyopenssl (top-level)
missing module named Cookie - imported by aliyunsdkcore.vendored.requests.compat (conditional)
missing module named cookielib - imported by aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urllib2 - imported by aliyunsdkcore.vendored.requests.compat (conditional)
missing module named urlparse - imported by oss2.compat (conditional), aliyunsdkcore.vendored.requests.compat (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional), oss2.compat (optional), aliyunsdkcore.vendored.requests.compat (optional)
missing module named 'aliyunsdkcore.vendored.six.moves' - imported by aliyunsdkcore.client (top-level), aliyunsdkcore.auth.composer.rpc_signature_composer (top-level), aliyunsdkcore.auth.composer.roa_signature_composer (top-level), aliyunsdkcore.auth.signers.ecs_ram_role_signer (top-level)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named predefined - imported by crcmod (optional)
missing module named _uuid - imported by uuid (optional)
missing module named netbios - imported by uuid (delayed)
missing module named win32wnet - imported by uuid (delayed)
missing module named gmpy - imported by ecdsa.ellipticcurve (optional), ecdsa.numbertheory (optional)
missing module named gmpy2 - imported by ecdsa.ellipticcurve (optional), ecdsa.numbertheory (optional)
runtime module named six.moves - imported by ecdsa.numbertheory (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named 'jose.backends.pycrypto_backend' - imported by jose.backends (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
