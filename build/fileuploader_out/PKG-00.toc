('E:\\code\\zhixue\\build\\fileuploader_out\\fileuploader_out.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\code\\zhixue\\build\\fileuploader_out\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\code\\zhixue\\build\\fileuploader_out\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\code\\zhixue\\build\\fileuploader_out\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\code\\zhixue\\build\\fileuploader_out\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\code\\zhixue\\build\\fileuploader_out\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\code\\zhixue\\build\\fileuploader_out\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'E:\\code\\zhixue\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('fileuploader_out', 'E:\\code\\zhixue\\fileuploader_out.py', 'PYSOURCE'),
  ('python38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python38.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('crcmod\\_crcfunext.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\crcmod\\_crcfunext.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Program '
   'Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Program Files\\Zulu\\zulu-21\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\code\\zhixue\\build\\fileuploader_out\\base_library.zip',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\RECORD',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\RECORD',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\license_files\\LICENSE.BSD',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\license_files\\LICENSE.BSD',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\METADATA',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\METADATA',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\WHEEL',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\license_files\\LICENSE.APACHE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\license_files\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\INSTALLER',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-43.0.1.dist-info\\license_files\\LICENSE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\cryptography-43.0.1.dist-info\\license_files\\LICENSE',
   'DATA'),
  ('aliyunsdkcore\\data\\endpoints.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\data\\endpoints.json',
   'DATA'),
  ('aliyunsdkcore\\data\\timeout_config.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\data\\timeout_config.json',
   'DATA'),
  ('aliyunsdkcore\\data\\retry_config.json',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\data\\retry_config.json',
   'DATA'),
  ('aliyunsdkcore\\vendored\\requests\\packages\\certifi\\cacert.pem',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\aliyunsdkcore\\vendored\\requests\\packages\\certifi\\cacert.pem',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('wheel-0.41.2.dist-info\\entry_points.txt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.41.2.dist-info\\RECORD',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.41.2.dist-info\\METADATA',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.41.2.dist-info\\INSTALLER',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.41.2.dist-info\\WHEEL',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.41.2.dist-info\\LICENSE.txt',
   'E:\\code\\zhixue\\venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\LICENSE.txt',
   'DATA')],
 'python38.dll',
 False,
 False,
 True,
 [],
 None,
 None,
 None)
