# coding:utf-8
import requests

url = "https://xktptapi.changyan.com/futureclass/studentserver/desktop/task/source?phase=05&version=1"

headers = {
    "S-Auth-Timestamp": "1720588183787",
    "S-Auth-Signature": "EuR1PIpMuKlkFgLDVsXscm2Mm0Z0KZNyD3yW0Cy6V0s=",
    "S-Auth-Version": "1",
    "S-Auth-Nonce": "ebd16cd8-73bd-45b1-96a1-71f8094d3f65",
    "S-SDK-Version": "1.0.9",
    "User-Agent": "ShieldAndroidSDK",
    "S-Auth-Stage": "RELEASE",
    "S-Auth-AppId": "YFil5acT",
    "S-Auth-ClientType": "android",
    "S-Auth-GroupId": "d9aa3089227d4796b1c92f19c4fac0d3",
    "S-Auth-Token": "9852fe61-87a1-403d-ad24-6fc8d082c146",
    "S-Auth-DeviceId": "54bbeb01102d9746a04af757f4c2bbe3"
}

response = requests.get(url, headers=headers)

# 检查响应状态码
if response.status_code == 200:
    # 打印响应内容
    print(response.json())
else:
    print(f"请求失败，状态码: {response.status_code}")
