from zhixuewang import login_student
student = login_student('310116200805160630', '56415880wen')
print('正在获取所有的作业...')
homeworks = student.get_homeworks(200)
# print(homeworks)
print('正在筛选所有的非打卡作业...')
clock_homeworks = []
i = 1
for homework in homeworks:
    if homework.type.code != 107:
        clock_homeworks.append(homework)
        print(f'{i}.{homework.title}')
        i += 1
print('筛选完成！')
while True:
    choice = clock_homeworks[int(input('请选择要查询答案的作业：')) - 1]
    answers = student.get_exercise_answer(choice)
    for answer in answers:
        if answer.content == '':
            answer.content = '暂无答案'
        print(answer.title, '：', answer.content)
    input('按回车键以继续...')
    i = 1
    for homework in clock_homeworks:
        print(f'{i}.{homework.title}')
        i += 1
