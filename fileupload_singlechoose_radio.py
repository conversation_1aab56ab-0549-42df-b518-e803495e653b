import json
import os
import sys

import requests
from zhi<PERSON>uewang import login_student
student = login_student('310116200805160630', '56415880wen')
import cffi
import tkinter
from tkinter import filedialog
def upload_file_to_zhixue(student_id, token, local_file_path):
    import requests
    import json
    import uuid

    def generate_uuid():
        return str(uuid.uuid4())
    # 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOiIyMzVjNWEyZjI4ZGExN2RmNjdmNmI2MDc2NjRlZjEwOTk2MTRiMSIsImV4cCI6MTcwMDkxODM2MSwiaWF0IjoxNzAwODMxOTYxfQ.NSLjfuAWybvW7ZmufUucB45Z-7lPiYL7UMjw5fnrbiQ'

    filename = f'EDU_{student_id}_{generate_uuid()}.docx'
    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    filepath = "middleHomework/android/zxzy/" + today + filename
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": filename,
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
    def percentage(consumed_bytes, total_bytes):
        if total_bytes:
            rate = int(100 * (float(consumed_bytes) / float(total_bytes)))
            print('\r{0}% '.format(rate), end='')
            sys.stdout.flush()
    result = bucket.put_object_from_file(filepath, local_file_path,progress_callback=percentage)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"
    # HTTP返回码。
    print('http status: {0}'.format(result.status))
    # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    print('request_id: {0}'.format(result.request_id))
    # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    print('ETag: {0}'.format(result.etag))
    # HTTP响应头部。
    print('date: {0}'.format(result.headers['date']))
def edit_file_to_zhixue(student_id, token, local_file_path, filename, filepath):
    import requests
    import json
    import uuid

    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": filename,
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
    def percentage(consumed_bytes, total_bytes):
        if total_bytes:
            rate = int(100 * (float(consumed_bytes) / float(total_bytes)))
            print('\r{0}% '.format(rate), end='')
            sys.stdout.flush()
    result = bucket.put_object_from_file(filepath, local_file_path,progress_callback=percentage)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"
    # HTTP返回码。
    print('http status: {0}'.format(result.status))
    # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    print('request_id: {0}'.format(result.request_id))
    # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    print('ETag: {0}'.format(result.etag))
    # HTTP响应头部。
    print('date: {0}'.format(result.headers['date']))


#遍历video文件夹
for root, dirs, files in os.walk('output'):
    for file in files:
        filepath = os.path.join(root, file)
        print(filepath)
        # 获取无后缀文件名
        filename = os.path.basename(filepath)
        filename = os.path.splitext(filename)[0]

        if filepath.endswith(".zip"):
            print(filename)
            # 上传文件
            print(f'文件正在上传...')

            file_url = upload_file_to_zhixue(student.id, student.get_auth_header()['XToken'], filepath)
            # file_url = 'https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2023/11/29/EDU_1500000100217351485_bc4ea515-b8b4-4cb7-b47d-17a9c1b99423.docx'
            print(file_url)
            if True:
                reslisturl = "https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/02/09/EDU_1500000100217351485_e6fdaa31-ba78-45ed-8dac-23d344a1aa8b.docx"
                res_list = list(json.loads(requests.get(reslisturl).text))
                res_list.append({"name": filename,
                                 "type": "radio",
                                 "url": file_url
                                 })
                with open('res_list.json', 'w') as f:
                    f.write(json.dumps(res_list))
                try:
                    # student = login_student(input('请输入智学网用户名：'), input('请输入密码：'))
                    import re

                    filename_pattern = r'EDU_.*\.docx'
                    filepath_pattern = r'middleHomework.*\.docx'
                    filename = re.search(filename_pattern, reslisturl)[0]
                    filepath = re.search(filepath_pattern, reslisturl)[0]
                    print(filename, filepath)
                    local_file_path = "res_list.json"
                    print('正在上传...')
                    file_url = edit_file_to_zhixue(student.id, student.get_auth_header()['XToken'], local_file_path,
                                                   filename,
                                                   filepath)
                    print(file_url)
                    print('文件修改完成！')
                except Exception as e:
                    print('error:', e)

