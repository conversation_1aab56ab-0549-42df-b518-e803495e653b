import time
import requests
import hashlib
import base64

def process_image_from_url(file_url, auth_token, auth_uuid):
    try:
        # Step 1: Download the file
        response = requests.get(file_url)
        response.raise_for_status()  # Raises an HTTPError for bad responses

        file_data = response.content

        # Step 2: Calculate the file's hash
        file_hash = hashlib.sha256(file_data).hexdigest()

        # Step 3: Convert the file data to Base64
        file_base64 = base64.b64encode(file_data).decode('utf-8')

        # Step 4: Get the token
        token_response = requests.post(
            "https://web.baimiaoapp.com/api/perm/single",
            headers={
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json;charset=UTF-8",
                "x-auth-token": auth_token,
                "x-auth-uuid": auth_uuid,
            },
            json={"mode": "single"}
        )
        token_response.raise_for_status()
        token = token_response.json()['data']['token']

        # Step 5: Submit the OCR request
        ocr_response = requests.post(
            "https://web.baimiaoapp.com/api/ocr/image/baidu",
            headers={
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json;charset=UTF-8",
                "x-auth-token": auth_token,
                "x-auth-uuid": auth_uuid,
            },
            json={
                "token": token,
                "hash": file_hash,
                "name": str(int(time.time() * 1000)) + ".png",
                "size": len(file_data),
                "dataUrl": f"data:image/jpeg;base64,{file_base64}",
                "result": {},
                "status": "processing",
                "isSuccess": False
            }
        )
        ocr_response.raise_for_status()
        jobStatusId = ocr_response.json()['data']['jobStatusId']

        # Step 6: Check the OCR status
        time.sleep(1)  # Wait for processing
        status_response = requests.get(
            f"https://web.baimiaoapp.com/api/ocr/image/baidu/status?jobStatusId={jobStatusId}",
            headers={
                "accept": "application/json, text/plain, */*",
                "x-auth-token": auth_token,
                "x-auth-uuid": auth_uuid,
            }
        )
        status_response.raise_for_status()

        # Step 7: Extract and print the result
        words_result = status_response.json()['data']['ydResp']['words_result']
        result = "\n".join(word['words'] for word in words_result)
        print(result)

    except requests.exceptions.RequestException as e:
        print(f"HTTP request error: {e}")
    except KeyError as e:
        print(f"Key error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

# Example usage
file_url = "https://vocechat.xf-yun.cn/api/resource/file?file_path=2025%2F2%2F17%2F412abe41-b85a-4867-aeb7-c3e8938a218a"
auth_token = "IcLlftkinfaVje5ZmPLxOuOqvW9X3JxF5FZjASdoNorj9IUe40k4An4sfMx8vKny"
auth_uuid = "4e5ebc0e-1bac-45e5-9d69-0242381fa5eb"

process_image_from_url(file_url, auth_token, auth_uuid)
