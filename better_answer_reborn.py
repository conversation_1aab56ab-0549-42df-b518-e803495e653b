# coding:utf-8
import webbrowser
from zhixuewang.account import login_cookie

print("请先登录并用脚本获取cookie")
webbrowser.open('https://www.zhixue.com/wap_login.html')

# 复制的cookie字符串
cookie_string = input("请输入登陆后获取到的cookie：")

# 将cookie字符串转换为字典
cookies = dict(item.split("=") for item in cookie_string.split("; "))
student = login_cookie(cookies)

# 获取作业
print('正在获取所有的作业...')
homeworks = student.get_homeworks(200)
print('正在筛选所有的自由出题作业及学习任务...')
common_homeworks = []
i = 1

# 筛选作业
for homework in homeworks:
    if homework.type.code in [105, 107, 102]:
        common_homeworks.append(homework)
        print(f'{i}.{homework.title} (Type: {homework.type.code})')
        i += 1

print('筛选完成！')

# 用户选择要查看的作业
choices = input('请选择要查看答案的作业（用空格分隔多个数字）：').split()

# 打开对应的网页
for choice in choices:
    index = int(choice) - 1
    selected_homework = common_homeworks[index]
    if selected_homework.type.code == 105:
        url = f'https://www.zhixue.com/middlehomework/web-teacher/views/#/work-center/free-topic-report/explanation?hwId={selected_homework.id}&hwType=105&reviseType=3'
    elif selected_homework.type.code == 107:
        url = f'https://www.zhixue.com/middlehomework/web-teacher/views/#/work-center/learning-task-report/task-overview?hwId={selected_homework.id}&hwType=107'
    elif selected_homework.type.code == 102:
        url = f"https://www.zhixue.com/middlehomework/web-teacher/views/#/work-center/topic-lib-practice-report/overview?hwId={selected_homework.id}&hwType=102&subjectCode=05&hwTags&isAggregationFillBlank=false"
    webbrowser.open(url)
