<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="referrer" content="no-referrer">
    <link rel="shortcut icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Share 纤云</title>
    <script src="https://unpkg.onmicrosoft.cn/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.onmicrosoft.cn/element-ui@2.15.10/lib/index.js"></script>
    <script src="https://unpkg.onmicrosoft.cn/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.onmicrosoft.cn/element-ui@2.15.10/lib/theme-chalk/index.css" />
    <style>
        #app {
            margin: 30px;
            margin-top: 100px;
        }

        .v-center {
            margin: 0;
            text-align: center;
        }

        .file_line {
            display: flex;
            justify-content: center;
        }

        .del_butom {
            margin: 0 10px;
        }

        .el-input {
            max-width: 400px;
        }

        .box-card {
            margin: 20px;
        }

        .demo-table-expand {
            font-size: 0;
        }

        .demo-table-expand label {
            color: #99a9bf;
        }

        .demo-table-expand .el-form-item {
            margin-right: 0;
            margin-bottom: 0;
            width: 100%;
            padding-left: 15px;
        }

        .box-card {
            max-width: 750px;
            margin: 15px auto;
        }

        .el-form-item:first-child {
            padding-top: 20px;
        }

        .el-form-item:last-child {
            padding-bottom: 30px;
        }

        .el-form-item {
            margin-bottom: 0;
        }

        .el-form-item__content {
            padding-left: 10px;
        }

        .text-wrapper {
            white-space: pre-wrap;
            text-align: left;
            padding: 10px 0;
        }

        .text_center {
            text-align: center;
        }

        .el-card {
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
            min-height: 200px;
        }

        .el-table__expanded-cell,
        .el-table__body-wrapper {
            position: static;
        }

        .expand-wrapper {
            background-color: #fff;
            position: absolute;
            left: 0;
            z-index: 999;
            width: calc(100% - 1px);
            overflow: auto;
        }

        .expand_div {
            height: 255px;
        }

        .el-form-item {
            white-space: nowrap;
        }

        .el-table__expanded-cell {
            padding: 0 !important;
        }

        .el-progress--circle,
        .el-progress--dashboard {
            display: block;
        }

        .el-progress-circle {
            margin: 0 auto !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-card class="box-card">
            <div slot="header" class="text_center">
                <span>分享文件 - Share 纤云 是一个私有文件云平台，本文件由用户上传。</span>
                <br>
                <span v-if="globalDescription">{{ globalDescription }}</span>
            </div>
            

            <div v-if="!passwordEntered">
                <el-input v-model="password" placeholder="请输入密码" type="password"></el-input>
                <el-button type="primary" @click="checkPassword">确认</el-button>
            </div>

            <div v-else>
                <div v-if="Load_done">
                    <el-table :data="shareFileList" style="width: 100%;" v-if="IsAlive" class="cols-fixed-table">
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-form label-position="left" inline class="demo-table-expand expand-wrapper">
                                    <el-form-item label="文件类型">
                                        <span>{{ props.row.filetype }}</span>
                                    </el-form-item>
                                    <el-form-item label="文件名">
                                        <span>{{ props.row.name }}</span>
                                    </el-form-item>
                                    <el-form-item label="文件大小">
                                        <span>{{ props.row.file_size }}</span>
                                    </el-form-item>
                                    <el-form-item label="上传时间">
                                        <span>{{ new Date(props.row.timestamp * 1000).toLocaleString() }}</span>
                                    </el-form-item>
                                </el-form>
                                <div class="expand_div"></div>
                            </template>
                        </el-table-column>
                        <el-table-column label="文件名称" prop="name"></el-table-column>
                        <el-table-column label="文件大小" prop="file_size" v-if="!Too_tight" width="150px"></el-table-column>
                        <el-table-column prop="actions" label="操作" width="100px" fixed="right">
                            <template slot-scope="scope">
                                <el-popover placement="right" trigger="hover" width="100">
                                    <el-progress type="circle" :percentage="scope.row.downloadProgress"
                                        :status="(scope.row.downloadProgress === 100)?'success':undefined "></el-progress>
                                    <el-button slot="reference"
                                        @click="download(scope.row.url, scope.row.name, scope.$index, false)" type="primary"
                                        size="small"><i class="el-icon-download el-icon--left"></i>下载</el-button>
                                </el-popover>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div v-else>
                        <el-alert title="该分享文件已失效" type="error" :closable="false" show-icon center>
                        </el-alert>
                    </div>
                </div>
                <div v-else>
                    <el-skeleton :rows="12" animated :throttle="300"></el-skeleton>
                </div>
            </div>
        </el-card>
    </div>
    <script>
        var Main = {
            data() {
                return {
                    shareFileList: [],
                    globalDescription: '',
                    IsAlive: false,
                    Load_done: false,
                    Too_tight: false,
                    password: '',
                    passwordEntered: false
                }
            },
            methods: {
                checkPassword() {
                    if (this.password) {
                        // If password is entered, refresh with ?res=password
                        window.location.search = `?res=${this.password}`;
                    } else {
                        // If no password, refresh with ?res=reslist
                        window.location.search = '?res=reslist';
                    }
                },
                LoadShare() {
                    const params = new URLSearchParams(window.location.search);
                    const resParam = params.get('res');

                    let url = 'https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/10/01/';
                    url += resParam ? `${resParam}.json` : '';

                    axios.get(url)
                        .then(response => {
                            if (response.status === 200) {
                                let DataList = response.data;
                                if (DataList.length > 0) {
                                    this.globalDescription = DataList[0] || "没有说明";
                                }
                                DataList.forEach(element => {
                                    element.file_size = this.formatBytes(element.file_size);
                                    element.downloadProgress = 0;
                                    element.allowDownload = true;
                                });
                                this.shareFileList = DataList.slice(1);
                                this.IsAlive = true;
                            } else {
                                this.$message.error('无法加载文件列表');
                            }
                        })
                        .catch(error => {
                            this.$message.error('请求失败: ' + error);
                        })
                        .finally(() => {
                            this.Load_done = true;
                        });
                },
                formatBytes(a, b) {
                    if (0 == a) return "0 B";
                    var c = 1024, d = b || 2, e = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"], f = Math.floor(Math.log(a) / Math.log(c));
                    return parseFloat((a / Math.pow(c, f)).toFixed(d)) + " " + e[f];
                },
                download(url, filename, index, auto_rename) {
                    const fileType = this.shareFileList[index].filetype.split('/').pop();
                    const fullFilename = `${filename}.${fileType}`;

                    if (auto_rename) {
                        this.$message({
                            message: '本文件已自动重命名，开始下载文件: ' + fullFilename,
                            type: 'success'
                        });
                        this.down_direct(url, fullFilename, index);
                        return;
                    }
                    if (this.shareFileList[index].allowDownload == false) {
                        this.$message({
                            message: '文件正在下载中，请稍后',
                            type: 'warning'
                        });
                        return;
                    } else {
                        this.$message({
                            message: '开始下载文件: ' + fullFilename,
                            type: 'success'
                        });
                    }
                    this.shareFileList[index].allowDownload = false;
                    this.getBlob(url, index, blob => {
                        this.saveAs(blob, fullFilename);
                    });
                },
                getBlob(url, index, cb) {
                    var xhr = new XMLHttpRequest();
                    xhr.open("GET", url, true);
                    xhr.onprogress = e => {
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total).toFixed(2);
                            this.shareFileList[index].downloadProgress = Number((percentComplete * 100).toFixed(0));
                        }
                    };
                    xhr.responseType = "blob";
                    xhr.onload = () => {
                        if (xhr.status === 200) {
                            this.shareFileList[index].allowDownload = true;
                            cb(xhr.response);
                        }
                    };
                    xhr.onerror = () => {
                        this.$message.warning("目标文件未设置 ACAO 响应头，尝试直接浏览器下载");
                        this.down_direct(url, this.shareFileList[index].name, index);
                    };
                    xhr.send();
                },
                saveAs(blob, filename) {
                    if (window.navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, filename);
                    } else {
                        var link = document.createElement("a");
                        var body = document.querySelector("body");

                        link.href = window.URL.createObjectURL(blob);
                        link.download = filename;

                        link.style.display = "none";
                        body.appendChild(link);

                        link.click();
                        body.removeChild(link);

                        window.URL.revokeObjectURL(link.href);
                    }
                },
                down_direct(url, filename, index) {
                    var link = document.createElement("a");
                    var body = document.querySelector("body");
                    link.href = url;
                    link.download = filename;
                    link.style.display = "none";
                    body.appendChild(link);
                    link.click();
                    body.removeChild(link);

                    this.shareFileList[index].downloadProgress = 100;
                    return;
                },
                _isMobile() {
                    let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
                    return flag;
                }
            },
            created() {
                const params = new URLSearchParams(window.location.search);
                const resParam = params.get('res');

                // Check if there's a query parameter
                if (resParam) {
                    this.passwordEntered = true; // Skip password input
                }
                this.LoadShare(); // Load the share data
                if (this._isMobile()) {
                    this.Too_tight = true;
                }
            },
            watch: {
                shareFileList: function (val) {
                    let title = 'Share 纤云 | 共 ' + val.length + ' 个文件' + ' | ';
                    val.forEach(element => {
                        title += element.name + ' | ';
                    });
                    document.title = title;
                },
            }
        }
        var Vue = Vue.extend(Main);

        new Vue().$mount("#app");
    </script>

</body>

</html>