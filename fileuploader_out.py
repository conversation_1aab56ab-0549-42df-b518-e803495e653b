import json
import os
import webbrowser

import requests
from zhi<PERSON><PERSON>wang.account import login_cookie
from jose import jwt, JWTError
from datetime import datetime
import secrets
# 获取当前目录
current_directory = os.getcwd()

# 构建文件路径
file_path = os.path.join(current_directory, "secret")
try:
    with open(file_path, "r", encoding="utf-8") as file:
        SECRET_KEY = file.read()
except IOError as e:
    print(f"读取文件时出错: {e}")
    SECRET_KEY = input("请输入SECRET_KEY：")
ALGORITHM = "HS256"


def verify_jwt(token: str):
    try:
        # 解码 JWT
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # 检查 JWT 是否过期
        exp = payload.get("exp")
        if exp is None:
            return False

        if datetime.utcfromtimestamp(exp) < datetime.utcnow():
            return False
        return True

    except JWTError:
        return False
result = verify_jwt(requests.get("https://api.haorwen.top/file").json()['token'])
if result:
    print("JWT 验证成功，进入程序...")
    # 获取当前目录
    current_directory = os.getcwd()

    # 构建文件路径
    file_path = os.path.join(current_directory, "secret")

    # 写入文件
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(SECRET_KEY)
        print("密钥已自动保存，请勿删除secret文件，若更换新设备需重新输入")
    except IOError as e:
        print(f"写入文件时出错: {e}")
else:
    input("JWT 验证失败或已过期...")
    exit()

print("请先登录并用脚本获取cookie")
webbrowser.open('https://www.zhixue.com/wap_login.html')

# 复制的cookie字符串
cookie_string = input("请输入登陆后获取到的cookie：")

# 将cookie字符串转换为字典
cookies = dict(item.split("=") for item in cookie_string.split("; "))
student = login_cookie(cookies)

def upload_file_to_zhixue(student_id, token, local_file_path):
    import os
    import requests
    from urllib.parse import urljoin

    def send_file_info(file_path, base_url, file_url):
        # 获取文件名
        file_name = os.path.basename(file_path)

        # 获取文件大小（以字节为单位）
        file_size = os.path.getsize(file_path)

        # 准备要发送的数据
        data = {
            "file_name": file_name,
            "file_url": file_url,
            "file_size": file_size
        }

        # 发送 POST 请求
        response = requests.post(urljoin(base_url, "fileinfo"), json=data)

        # 检查响应
        if response.status_code == 200:
            print("File info sent successfully!")
            print(response.json())
        else:
            print(f"Error: {response.status_code}")

    base_url = "https://api.haorwen.top/"  # 替换为您的 FastAPI 服务器地址


    import json
    import uuid

    def generate_uuid():
        return str(uuid.uuid4())
    # 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOiIyMzVjNWEyZjI4ZGExN2RmNjdmNmI2MDc2NjRlZjEwOTk2MTRiMSIsImV4cCI6MTcwMDkxODM2MSwiaWF0IjoxNzAwODMxOTYxfQ.NSLjfuAWybvW7ZmufUucB45Z-7lPiYL7UMjw5fnrbiQ'
    # 让用户自己决定文件扩展名
    # 回车默认docx
    filename_extension = input("请输入文件扩展名（默认docx）：")
    filename_extension = filename_extension or "docx"
    filename = f'EDU_{student_id}_{generate_uuid()}.{filename_extension}'
    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    filepath = "middleHomework/android/zxzy/" + today + filename
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": filename,
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': '',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)
    result = bucket.put_object_from_file(filepath, local_file_path)
    send_file_info(local_file_path, base_url, f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}")

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"
    # HTTP返回码。
    # print('http status: {0}'.format(result.status))
    # # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    # print('request_id: {0}'.format(result.request_id))
    # # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    # print('ETag: {0}'.format(result.etag))
    # # HTTP响应头部。
    # print('date: {0}'.format(result.headers['date']))
i = 1

input('请将要上传的文件(后缀改为docx)放入files文件夹中，按回车键继续...')
if input('是否覆盖原有的filedata.json文件？(y/n)') == 'y':
    # 备份原有的filedata.json文件为filedata.json.bak
    try:
        f = open('filedata.json', 'r')
        content = f.read()
        f.close()
        f = open('filedata.json.bak', 'w')
        f.write(content)
        f.close()
        print('已备份原有的filedata.json文件为filedata.json.bak，如需恢复请将filedata.json.bak重命名为filedata.json')
    except:
        pass
    attachmentDTOs = []
else:
    try:
        f = open('filedata.json', 'r')
        content = f.read()
        attachmentDTOs = json.loads(content)
        i = attachmentDTOs[-1]['sort'] + 1
        f.close()
    except:
        attachmentDTOs = []
for filename in os.listdir('files'):
    if filename:  # 或者使用其他文件扩展名
        filepath = os.path.join('files', filename)
        file_size = os.path.getsize(filepath)
        # 判断文件大小是否大于300MB
        if file_size > 300 * 1024 * 1024:  # 注意300MB需要转换为字节
            print(f"{filename} 文件大小超过限制！请调整到小于300mb以免触发智学网风控！")
        print(f'第{i}个文件正在上传...')
        file_url = upload_file_to_zhixue(student.id, student.get_auth_header()['XToken'], filepath)
        # file_url = 'https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2023/11/29/EDU_1500000100217351485_bc4ea515-b8b4-4cb7-b47d-17a9c1b99423.docx'
        print(file_url)
        attachmentDTOs.append({
            "fileType": 4,
            "sourceType": 1,
            "path": file_url,
            "sort": i,
            "videoDuration": 0,
            "videoCoverUrl": "",
            "name": filename,
            "extensionName": "docx"
        })
        i += 1

b = json.dumps(attachmentDTOs,indent=4)
f2 = open('filedata.json', 'w')
f2.write(b)
f2.close()
print('filedata.json已生成，接下来你可打开提交作业的脚本')
input('按回车键退出本程序...')