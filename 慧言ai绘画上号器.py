import random
import requests
import json
import webbrowser
from datetime import datetime

# 生成随机手机号
def generate_phone():
    return '158' + ''.join(str(random.randint(0, 9)) for _ in range(8))
choice = input("确定要注册一个新的账号吗？（建议在上一个账号使用完积分前不要注册，账号注册最小间隔为五分钟）（y/n）")
if choice.lower() == 'n':
    exit()
# 注册请求
phone = generate_phone()
register_url = "https://chat.huiyan-ai.cn/api/user-register"
register_headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "content-type": "application/json",
    "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Microsoft Edge\";v=\"132\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "Referer": "https://chat.huiyan-ai.cn/",
}

register_data = {
    "username": phone,
    "password": "1145141919810",
    "registerType": "2",
    "code": ""
}

# 发送注册请求
register_response = requests.post(
    register_url,
    headers=register_headers,
    json=register_data
)

if register_response.json()["code"] != 200:
    print("注册失败！")
    print(register_response.json())
    input("注册失败，请检查网络或重新运行程序！按回车键退出...")
    exit()
token = register_response.json()['data']['token']

# 配置通用请求头
common_headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "authorization": f"Bearer {token}",
    "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Microsoft Edge\";v=\"132\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "Referer": "https://chat.huiyan-ai.cn/",
}

# 获取用户信息
user_info_response = requests.get(
    "https://chat.huiyan-ai.cn/api/user-info",
    headers=common_headers
)
user_data = user_info_response.json()['data']
nick_name = user_data['nickName']
show_point = user_data['userMember']['showPoint']

# 打印并记录日志
print(f"注册成功！用户昵称：{nick_name}\n剩余积分数：{show_point}")
choice = input("是否上号？（y/n）")
log_entry = {
    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    "phone": phone,
    "nickname": nick_name,
    "points": show_point,
    "choice": choice.lower()
}
if choice.lower() == 'n':
    exit()



with open("accounts.log", "a", encoding="utf-8") as f:
    f.write(json.dumps(log_entry, ensure_ascii=False, indent=2) + "\n")

# 获取OpenFlowToken
token_response = requests.get(
    "https://chat.huiyan-ai.cn/api/getOpenFlowToken",
    headers=common_headers
)
openflow_token = token_response.json()['data']

# 打开浏览器
webbrowser.open(f"https://openflowai.net/midjourney?hyToken={openflow_token}")

input("上号完成，回车键退出...")