<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Document Downloader</title>
    <script>
        function loadFile(input) {
        const file = input.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const contents = e.target.result;

                // 假设我们在此处有一个有效的HTML内容
                const parser = new DOMParser();
                const doc = parser.parseFromString(contents, 'text/html');
                const bodyContent = doc.body.innerHTML;
                const headContent = doc.head.innerHTML;

                // 将解析的 HTML 内容设置到当前页面的 body 中
                document.body.innerHTML = bodyContent;

                // 为当前页面追加 head 中的内容（例如样式表链接）
                document.head.innerHTML += headContent;
            };
            reader.readAsText(file);
        } else {
            // 如果用户没有选择文件
            alert("Please select a file.");
        }
    }
        function downloadFile() {
            // 创建a元素
            var a = document.createElement('a');
            // 设置文件下载链接
            a.href = 'https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2023/12/09/EDU_1500000100217351485_bfbe47b1-82e7-4490-839b-a7f096d138cb.docx';
            // 设置下载属性和文件名
            a.setAttribute('download', 'file.docx');
            // 添加a元素到body
            document.body.appendChild(a);
            // 触发点击事件开始下载
            a.click();
            // 移除a元素
            document.body.removeChild(a);
        }
        // 当页面载入完成后，运行downloadFile函数
        window.onload = downloadFile;

    </script>
</head>
<body>
    <p>如果下载未自动开始，请 <a href="#" onclick="downloadFile()">点击此处</a> 手动下载。</p>
    <p>用下方按钮打开下载好的文档即可查看答案</p>
    <input type="file" onchange="loadFile(this)" accept=".docx,text/plain">
</body>
</html>
