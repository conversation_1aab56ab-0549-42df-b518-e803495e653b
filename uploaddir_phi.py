import json
import os

import requests
from zhi<PERSON>uewang import login_student
student = login_student('310116200805160630', '56415880wen')
def edit_file_to_zhixue(token, local_dir,dirname):
    import requests
    import json
    import uuid

    # filename = "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx"
    # 将当前日期格式化为"2023/11/24"的格式
    import datetime
    today = datetime.date.today()
    today = today.strftime("%Y/%m/%d/")
    # filepath = 'middleHomework/android/zxzy/2023/11/24/EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx'
    data = {
        "stsTokenQueryList": [
            {
                "appKey": "XXX_ANDROID_ZXZY_STU",
                "chunks": 1,
                "fileName": "EDU_1500000100217351485_f4972112-0dc7-4226-9580-1750a218c902.docx",
                "productKey": ""
            }
        ]
    }

    headers = {
        'clientType': 'android',
        'epasAppId': 'zhixue_parent',
        'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
        'token': token,
        'Content-Type': 'application/json; charset=utf-8',
        'Content-Length': '163',
        'Host': 'aidp.changyan.com',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'okhttp/3.12.12'
    }

    response = requests.post('https://aidp.changyan.com/open-svc/file/listStsTokenV2', headers=headers,
                             data=json.dumps(data))
    ossinfo = response.json()
    import oss2

    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = ossinfo['data'][0]['accessId']
    sts_access_key_secret = ossinfo['data'][0]['accessSecret']
    # 填写从STS服务获取的安全令牌（SecurityToken）。
    security_token = ossinfo['data'][0]['securityToken']
    # 使用临时访问凭证中的认证信息初始化StsAuth实例。
    auth = oss2.StsAuth(sts_access_key_id,
                        sts_access_key_secret,
                        security_token)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')
    oss_base_prefix = f'middleHomework/android/zxzy/2024/02/12/phitogether/pez'
    for root, dirs, files in os.walk(local_dir):
        # 计算当前目录相对于初始目录的相对路径
        relative_path = os.path.relpath(root, local_dir)
        if relative_path == '.':
            relative_path = ''

        # 过滤以.开头的文件夹
        dirs[:] = [d for d in dirs if not d.startswith('.')]

        for file in files:
            # 跳过以.开头的文件
            if file.startswith('.'):
                continue

            local_file_path = os.path.join(root, file)
            # 保持文件在OSS上的路径与本地相对路径一致
            oss_file_path = os.path.join(oss_base_prefix, relative_path, file).replace('\\', '/')
            # 上传文件
            bucket.put_object_from_file(oss_file_path, local_file_path)
            print(f'Uploaded {local_file_path} to {oss_file_path}')
    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    # result = bucket.put_object('exampleobject.txt', 'Hello OSS', headers=headers)

    return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/02/12/{dirname}/index.html"
    # HTTP返回码。
    print('http status: {0}'.format(result.status))
    # 请求ID。请求ID是本次请求的唯一标识，强烈建议在程序日志中添加此参数。
    print('request_id: {0}'.format(result.request_id))
    # ETag是put_object方法返回值特有的属性，用于标识一个Object的内容。
    print('ETag: {0}'.format(result.etag))
    # HTTP响应头部。
    print('date: {0}'.format(result.headers['date']))


# 先把html文件夹下所有文件和文件夹打包为zip
try:
    # student = login_student(input('请输入智学网用户名：'), input('请输入密码：'))
    import re
    # htmlzipurl = "https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/middleHomework/android/zxzy/2024/02/12/EDU_1500000100217351485_f59bd141-3423-4b53-987d-4f522412ff13.html"
    # filename_pattern = r'EDU_.*\.html'
    # filepath_pattern = r'middleHomework.*\.html'
    # filename = re.search(filename_pattern, htmlzipurl)[0]
    # filepath = re.search(filepath_pattern, htmlzipurl)[0]
    # print(filename, filepath)
    dirname = "phitogether"
    local_dir = f"html/.phitogether/pez/"
    print('正在上传...')
    file_url = edit_file_to_zhixue(student.get_auth_header()['XToken'], local_dir,dirname)
    print(file_url)
except Exception as e:
    print('error:', e)